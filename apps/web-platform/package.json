{"name": "@apps/web-platform", "version": "1.0.0", "description": "工商联-运管平台", "license": "不对外分发", "types": "index.d.ts", "scripts": {"dev": "vite --force", "build": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build", "build:docker": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --outDir ./docker/dist/", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "prettier": "prettier --write .", "clean": "rimraf node_modules"}, "dependencies": {"@axolo/json-editor-vue": "^0.3.2", "@chenfengyuan/vue-qrcode": "^2.0.0", "@element-plus/icons-vue": "^2.0.10", "@popperjs/core": "2.11.8", "@tinymce/tinymce-vue": "5.1.0", "@types/file-saver": "^2.0.7", "@vueuse/core": "^10.4.1", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "@zszc/co-form-v3": "3.1.7", "@zszc/co-preview-v3": "^1.0.1", "@zszc/co-upload-v3": "^1.0.1", "@zszc/co-verify-v3": "^0.0.7", "@zszc/sms-verify-v3": "^0.1.5", "autoprefixer": "^10.4.7", "axios": "^1.3.3", "codemirror": "5.65.5", "crypto-js": "^3.1.9-1", "driver.js": "^0.9.8", "echarts": "^5.4.1", "element-plus": "^2.9.10", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "form-designer-plus": "^0.1.5", "highlight.js": "^11.7.0", "html-to-image": "^1.11.13", "js-base64": "^3.7.7", "js-cookie": "^3.0.1", "jsencrypt": "^3.3.2", "json-editor-vue3": "^1.1.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pinia": "^2.0.32", "postcss": "8.4.40", "qrcode": "1.5.1", "qs": "^6.11.0", "sass": "^1.63.2", "screenfull": "^6.0.2", "sm-crypto": "^0.3.12", "sortablejs": "^1.15.0", "splitpanes": "^3.1.5", "tailwindcss": "3.4.6", "tinymce": "6.8.4", "v-calendar": "3.1.2", "v-viewer": "^3.0.21", "vue": "3.4.15", "vue-clipboard3": "^2.0.0", "vue-echarts": "6.6.1", "vue-i18n": "9.2.2", "vue-router": "^4.1.6", "vue3-ace-editor": "^2.2.4", "vue3-tree-org": "^4.2.2", "vue3-video-play": "1.3.1-beta.6", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/markdown-it": "^14.1.1", "@types/node": "^18.14.0", "@types/nprogress": "^0.2.0", "@types/qrcode": "1.5.1", "@types/sm-crypto": "^0.3.4", "@types/sortablejs": "^1.15.0", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/compiler-sfc": "^3.2.47", "consola": "^2.15.3", "cross-env": "7.0.3", "daisyui": "4.11.1", "eslint": "^8.34.0", "eslint-plugin-vue": "^9.9.0", "pinia-plugin-persist": "^1.0.0", "prettier": "2.8.4", "terser": "^5.31.1", "typescript": "^4.9.5", "unplugin-auto-import": "^0.13.0", "vite": "^4.3.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-top-level-await": "^1.3.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.1.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus"]}