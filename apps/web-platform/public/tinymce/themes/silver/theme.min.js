/**
 * TinyMCE version 6.0.0 (2020-03-03)
 */
!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=(e,t)=>(...o)=>e(t.apply(null,o)),y=e=>()=>e,x=e=>e,w=(e,t)=>e===t;function S(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const k=e=>t=>!e(t),C=e=>()=>{throw new Error(e)},O=e=>e(),_=y(!1),T=y(!0);var E=tinymce.util.Tools.resolve("tinymce.ThemeManager");class B{constructor(e,t){this.tag=e,this.value=t}static some(e){return new B(!0,e)}static none(){return B.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?B.some(e(this.value)):B.none()}bind(e){return this.tag?e(this.value):B.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:B.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?B.some(e):B.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}B.singletonNone=new B(!1);const M=Array.prototype.slice,A=Array.prototype.indexOf,D=Array.prototype.push,F=(e,t)=>A.call(e,t),I=(e,t)=>{const o=F(e,t);return-1===o?B.none():B.some(o)},V=(e,t)=>F(e,t)>-1,R=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},z=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},H=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=M.call(e,n,n+t);o.push(s)}return o},P=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},L=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},W=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},U=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),j=(e,t,o)=>(N(e,((e,n)=>{o=t(o,e,n)})),o),G=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return B.some(s);if(o(s,n))break}return B.none()})(e,t,_),$=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return B.some(o);return B.none()},q=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);D.apply(t,e[o])}return t},X=(e,t)=>q(P(e,t)),K=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},Y=e=>{const t=M.call(e,0);return t.reverse(),t},J=(e,t)=>W(e,(e=>!V(t,e))),Z=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],ee=(e,t)=>{const o=M.call(e,0);return o.sort(t),o},te=(e,t)=>t>=0&&t<e.length?B.some(e[t]):B.none(),oe=e=>te(e,0),ne=e=>te(e,e.length-1),se=p(Array.from)?Array.from:e=>M.call(e),re=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return B.none()},ae=Object.keys,ie=Object.hasOwnProperty,le=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},ce=(e,t)=>de(e,((e,o)=>({k:o,v:t(e,o)}))),de=(e,t)=>{const o={};return le(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},ue=e=>(t,o)=>{e[o]=t},me=(e,t,o,n)=>(le(e,((e,s)=>{(t(e,s)?o:n)(e,s)})),{}),ge=(e,t)=>{const o=[];return le(e,((e,n)=>{o.push(t(e,n))})),o},pe=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return B.some(r)}return B.none()},he=e=>ge(e,x),fe=(e,t)=>be(e,t)?B.from(e[t]):B.none(),be=(e,t)=>ie.call(e,t),ve=(e,t)=>be(e,t)&&void 0!==e[t]&&null!==e[t],ye=(e,t,o=w)=>e.exists((e=>o(e,t))),xe=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},we=(e,t,o)=>e.isSome()&&t.isSome()?B.some(o(e.getOrDie(),t.getOrDie())):B.none(),Se=(e,t)=>e?B.some(t):B.none(),ke=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,Ce=(e,t)=>-1!==e.indexOf(t),Oe=(e,t)=>ke(e,t,e.length-t.length),_e=(Ie=/^\s+|\s+$/g,e=>e.replace(Ie,"")),Te=e=>e.length>0,Ee=e=>void 0!==e.style&&p(e.style.getPropertyValue),Be=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Me=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return Be(o.childNodes[0])},Ae=(e,t)=>{const o=(t||document).createElement(e);return Be(o)},De=(e,t)=>{const o=(t||document).createTextNode(e);return Be(o)},Fe=Be;var Ie;"undefined"!=typeof window?window:Function("return this;")();const Ve=e=>e.dom.nodeName.toLowerCase(),Re=e=>t=>(e=>e.dom.nodeType)(t)===e,ze=Re(1),He=Re(3),Pe=Re(9),Ne=Re(11),Le=e=>t=>ze(t)&&Ve(t)===e,We=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Ue=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,je=(e,t)=>e.dom===t.dom,Ge=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},$e=e=>Fe(e.dom.ownerDocument),qe=e=>Pe(e)?e:$e(e),Xe=e=>Fe(qe(e).dom.documentElement),Ke=e=>Fe(qe(e).dom.defaultView),Ye=e=>B.from(e.dom.parentNode).map(Fe),Je=e=>B.from(e.dom.parentElement).map(Fe),Ze=e=>B.from(e.dom.offsetParent).map(Fe),Qe=e=>P(e.dom.childNodes,Fe),et=(e,t)=>{const o=e.dom.childNodes;return B.from(o[t]).map(Fe)},tt=(e,t)=>({element:e,offset:t}),ot=(e,t)=>{const o=Qe(e);return o.length>0&&t<o.length?tt(o[t],0):tt(e,t)},nt=e=>Ne(e)&&g(e.dom.host),st=p(Element.prototype.attachShadow)&&p(Node.prototype.getRootNode),rt=y(st),at=st?e=>Fe(e.dom.getRootNode()):qe,it=e=>nt(e)?e:Fe(qe(e).dom.body),lt=e=>{const t=at(e);return nt(t)?B.some(t):B.none()},ct=e=>Fe(e.dom.host),dt=e=>{const t=He(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return lt(Fe(t)).fold((()=>o.body.contains(t)),(n=dt,s=ct,e=>n(s(e))));var n,s},ut=()=>mt(Fe(document)),mt=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return Fe(t)},gt=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},pt=(e,t,o)=>{gt(e.dom,t,o)},ht=(e,t)=>{const o=e.dom;le(t,((e,t)=>{gt(o,t,e)}))},ft=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},bt=(e,t)=>B.from(ft(e,t)),vt=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},yt=(e,t)=>{e.dom.removeAttribute(t)},xt=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Ee(e)&&e.style.setProperty(t,o)},wt=(e,t)=>{Ee(e)&&e.style.removeProperty(t)},St=(e,t,o)=>{const n=e.dom;xt(n,t,o)},kt=(e,t)=>{const o=e.dom;le(t,((e,t)=>{xt(o,t,e)}))},Ct=(e,t)=>{const o=e.dom;le(t,((e,t)=>{e.fold((()=>{wt(o,t)}),(e=>{xt(o,t,e)}))}))},Ot=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||dt(e)?n:_t(o,t)},_t=(e,t)=>Ee(e)?e.style.getPropertyValue(t):"",Tt=(e,t)=>{const o=e.dom,n=_t(o,t);return B.from(n).filter((e=>e.length>0))},Et=e=>{const t={},o=e.dom;if(Ee(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},Bt=(e,t,o)=>{const n=Ae(e);return St(n,t,o),Tt(n,t).isSome()},Mt=(e,t)=>{const o=e.dom;wt(o,t),ye(bt(e,"style").map(_e),"")&&yt(e,"style")},At=e=>e.dom.offsetWidth,Dt=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=Ot(o,e);return parseFloat(t)||0}return n},n=(e,t)=>j(t,((t,o)=>{const n=Ot(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Ee(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},Ft=Dt("height",(e=>{const t=e.dom;return dt(e)?t.getBoundingClientRect().height:t.offsetHeight})),It=e=>Ft.get(e),Vt=e=>Ft.getOuter(e),Rt=(e,t)=>({left:e,top:t,translate:(o,n)=>Rt(e+o,t+n)}),zt=Rt,Ht=(e,t)=>void 0!==e?e:void 0!==t?t:0,Pt=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return zt(o.offsetLeft,o.offsetTop);const r=Ht(null==n?void 0:n.pageYOffset,s.scrollTop),a=Ht(null==n?void 0:n.pageXOffset,s.scrollLeft),i=Ht(s.clientTop,o.clientTop),l=Ht(s.clientLeft,o.clientLeft);return Nt(e).translate(a-l,r-i)},Nt=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?zt(o.offsetLeft,o.offsetTop):dt(e)?(e=>{const t=e.getBoundingClientRect();return zt(t.left,t.top)})(t):zt(0,0)},Lt=Dt("width",(e=>e.dom.offsetWidth)),Wt=e=>Lt.get(e),Ut=e=>Lt.getOuter(e),jt=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},Gt=()=>$t(0,0),$t=(e,t)=>({major:e,minor:t}),qt={nu:$t,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?Gt():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return $t(n(1),n(2))})(e,o)},unknown:Gt},Xt=(e,t)=>{const o=String(t).toLowerCase();return G(e,(e=>e.search(o)))},Kt=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Yt=e=>t=>Ce(t,e),Jt=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Ce(e,"edge/")&&Ce(e,"chrome")&&Ce(e,"safari")&&Ce(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Kt],search:e=>Ce(e,"chrome")&&!Ce(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Ce(e,"msie")||Ce(e,"trident")},{name:"Opera",versionRegexes:[Kt,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Yt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Yt("firefox")},{name:"Safari",versionRegexes:[Kt,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Ce(e,"safari")||Ce(e,"mobile/"))&&Ce(e,"applewebkit")}],Zt=[{name:"Windows",search:Yt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Ce(e,"iphone")||Ce(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Yt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:Yt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Yt("linux"),versionRegexes:[]},{name:"Solaris",search:Yt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Yt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Yt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Qt={browsers:y(Jt),oses:y(Zt)},eo="Edge",to="Chromium",oo="Opera",no="Firefox",so="Safari",ro=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(eo),isChromium:n(to),isIE:n("IE"),isOpera:n(oo),isFirefox:n(no),isSafari:n(so)}},ao=()=>ro({current:void 0,version:qt.unknown()}),io=ro,lo=(y(eo),y(to),y("IE"),y(oo),y(no),y(so),"Windows"),co="Android",uo="Linux",mo="macOS",go="Solaris",po="FreeBSD",ho="ChromeOS",fo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(lo),isiOS:n("iOS"),isAndroid:n(co),isMacOS:n(mo),isLinux:n(uo),isSolaris:n(go),isFreeBSD:n(po),isChromeOS:n(ho)}},bo=()=>fo({current:void 0,version:qt.unknown()}),vo=fo,yo=(y(lo),y("iOS"),y(co),y(uo),y(mo),y(go),y(po),y(ho),e=>window.matchMedia(e).matches);let xo=jt((()=>((e,t,o)=>{const n=Qt.browsers(),s=Qt.oses(),r=t.bind((e=>((e,t)=>re(t.brands,(t=>{const o=t.brand.toLowerCase();return G(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:qt.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>Xt(e,t).map((e=>{const o=qt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(ao,io),a=((e,t)=>Xt(e,t).map((e=>{const o=qt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(bo,vo),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:y(s),isiPhone:y(r),isTablet:y(l),isPhone:y(c),isTouch:y(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:y(d),isDesktop:y(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(navigator.userAgent,B.from(navigator.userAgentData),yo)));const wo=()=>xo(),So=e=>{const t=Fe((e=>{if(rt()&&g(e.target)){const t=Fe(e.target);if(ze(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return oe(t)}}return B.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=v(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},ko=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(So(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:S(Co,e,t,r,s)}},Co=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Oo=(e,t)=>{Ye(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},_o=(e,t)=>{const o=(e=>B.from(e.dom.nextSibling).map(Fe))(e);o.fold((()=>{Ye(e).each((e=>{Eo(e,t)}))}),(e=>{Oo(e,t)}))},To=(e,t)=>{const o=(e=>et(e,0))(e);o.fold((()=>{Eo(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},Eo=(e,t)=>{e.dom.appendChild(t.dom)},Bo=(e,t)=>{N(t,(t=>{Eo(e,t)}))},Mo=e=>{e.dom.textContent="",N(Qe(e),(e=>{Ao(e)}))},Ao=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Do=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return zt(o,n)},Fo=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},Io=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Vo=e=>{const t=void 0===e?window:e,o=t.document,n=Do(Fe(o));return(e=>{const t=void 0===e?window:e;return wo().browser.isFirefox()?B.none():B.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return Io(n.left,n.top,o,s)}),(e=>Io(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},Ro=()=>Fe(document),zo=(e,t)=>e.view(t).fold(y([]),(t=>{const o=e.owner(t),n=zo(e,o);return[t].concat(n)}));var Ho=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?B.none():B.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(Fe)},owner:e=>$e(e)});const Po=e=>{const t=Ro(),o=Do(t),n=((e,t)=>{const o=t.owner(e),n=zo(t,o);return B.some(n)})(e,Ho);return n.fold(S(Pt,e),(t=>{const n=Nt(e),s=U(t,((e,t)=>{const o=Nt(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return zt(s.left+n.left+o.left,s.top+n.top+o.top)}))},No=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Lo=e=>{const t=Pt(e),o=Ut(e),n=Vt(e);return No(t.left,t.top,o,n)},Wo=e=>{const t=Po(e),o=Ut(e),n=Vt(e);return No(t.left,t.top,o,n)},Uo=()=>Vo(window),jo=e=>{const t=t=>t(e),o=y(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:T,isError:_,map:t=>$o.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>B.some(e)};return s},Go=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:_,isError:T,map:t,mapError:t=>$o.error(t(e)),bind:t,exists:_,forall:T,getOr:x,or:x,getOrThunk:O,orThunk:O,getOrDie:C(String(e)),each:b,toOptional:B.none};return o},$o={value:jo,error:Go,fromOption:(e,t)=>e.fold((()=>Go(t)),jo)};var qo;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(qo||(qo={}));const Xo=(e,t,o)=>e.stype===qo.Error?t(e.serror):o(e.svalue),Ko=e=>({stype:qo.Value,svalue:e}),Yo=e=>({stype:qo.Error,serror:e}),Jo=Ko,Zo=Yo,Qo=Xo,en=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),tn=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},on=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)be(s,t)&&(o[t]=e(o[t],s[t]))}return o},nn=on(((e,t)=>i(e)&&i(t)?nn(e,t):t)),sn=on(((e,t)=>t)),rn=e=>({tag:"defaultedThunk",process:e}),an=e=>rn(y(e)),ln=e=>({tag:"mergeWithThunk",process:e}),cn=e=>{const t=(e=>{const t=[],o=[];return N(e,(e=>{Xo(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,v(Zo,q)(o)):Jo(t.values);var o},dn=e=>a(e)&&ae(e).length>100?" removed due to size":JSON.stringify(e,null,2),un=(e,t)=>Zo([{path:e,getErrorInfo:t}]),mn=e=>({extract:(t,o)=>{return n=e(o),s=e=>((e,t)=>un(e,y(t)))(t,e),n.stype===qo.Error?s(n.serror):n;var n,s},toString:y("val")}),gn=mn(Jo),pn=(e,t,o,n)=>n(fe(e,t).getOrThunk((()=>o(e)))),hn=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>Jo(B.none())),(e=>{const o=s.extract(t.concat([n]),e);return r=o,a=B.some,r.stype===qo.Value?{stype:qo.Value,svalue:a(r.svalue)}:r;var r,a}));switch(e.tag){case"required":return((e,t,o,n)=>fe(t,o).fold((()=>((e,t,o)=>un(e,(()=>'Could not find valid *required* value for "'+t+'" in '+dn(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return pn(o,n,e.process,r);case"option":return((e,t,o)=>o(fe(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(fe(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return pn(o,n,y({}),(t=>{const n=nn(e.process(o),t);return r(n)}))}},fn=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),bn=e=>ae(((e,t)=>{const o={};return me(e,t,ue(o),b),o})(e,g)),vn=e=>{const t=yn(e),o=U(e,((e,t)=>tn(t,(t=>nn(e,{[t]:!0})),y(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:bn(n),r=W(s,(e=>!ve(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>un(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},yn=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)tn(r,((o,r,a,i)=>{const l=hn(a,e,t,o,i);Qo(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?Zo(s):Jo(n)})(t,o,e),toString:()=>{const t=P(e,(e=>tn(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),xn=e=>({extract:(t,o)=>{const n=P(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return cn(n)},toString:()=>"array("+e.toString()+")"}),wn=e=>({extract:(t,o)=>{const n=[];for(const s of e){const e=s.extract(t,o);if(e.stype===qo.Value)return e;n.push(e)}return cn(n)},toString:()=>"oneOf("+P(e,(e=>e.toString())).join(", ")+")"}),Sn=(e,t)=>({extract:(o,n)=>{const s=ae(n),r=((t,o)=>xn(mn(e)).extract(t,o))(o,s);return i=e=>{const s=P(e,(e=>en(e,e,{tag:"required",process:{}},t)));return yn(s).extract(o,n)},(a=r).stype===qo.Value?i(a.svalue):a;var a,i},toString:()=>"setOf("+t.toString()+")"}),kn=v(xn,yn),Cn=y(gn),On=(e,t)=>mn((o=>{const n=typeof o;return e(o)?Jo(o):Zo(`Expected type: ${t} but got: ${n}`)})),_n=On(h,"number"),Tn=On(r,"string"),En=On(d,"boolean"),Bn=On(p,"function"),Mn=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>Mn(e[t])));default:return!1}},An=mn((e=>Mn(e)?Jo(e):Zo("Expected value to be acceptable for sending via postMessage"))),Dn=(e,t)=>({extract:(o,n)=>fe(n,e).fold((()=>((e,t)=>un(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>fe(o,n).fold((()=>((e,t,o)=>un(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+dn(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+ae(t)}),Fn=e=>mn((t=>e(t).fold(Zo,Jo))),In=(e,t)=>Sn((t=>e(t).fold(Yo,Ko)),t),Vn=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===qo.Error?{stype:qo.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),Xo(n,$o.error,$o.value);var n},Rn=e=>e.fold((e=>{throw new Error(Hn(e))}),x),zn=(e,t,o)=>Rn(Vn(e,t,o)),Hn=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:y("... (only showing first ten failures)")}]):e;return P(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+dn(e.input),Pn=(e,t)=>Dn(e,ce(t,yn)),Nn=en,Ln=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),Wn=e=>Fn((t=>V(e,t)?$o.value(t):$o.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),Un=e=>Nn(e,e,{tag:"required",process:{}},Cn()),jn=(e,t)=>Nn(e,e,{tag:"required",process:{}},t),Gn=e=>jn(e,_n),$n=e=>jn(e,Tn),qn=(e,t)=>Nn(e,e,{tag:"required",process:{}},Wn(t)),Xn=e=>jn(e,Bn),Kn=(e,t)=>Nn(e,e,{tag:"required",process:{}},yn(t)),Yn=(e,t)=>Nn(e,e,{tag:"required",process:{}},kn(t)),Jn=(e,t)=>Nn(e,e,{tag:"required",process:{}},xn(t)),Zn=e=>Nn(e,e,{tag:"option",process:{}},Cn()),Qn=(e,t)=>Nn(e,e,{tag:"option",process:{}},t),es=e=>Qn(e,_n),ts=e=>Qn(e,Tn),os=e=>Qn(e,Bn),ns=(e,t)=>Qn(e,xn(t)),ss=(e,t)=>Qn(e,yn(t)),rs=(e,t)=>Nn(e,e,an(t),Cn()),as=(e,t,o)=>Nn(e,e,an(t),o),is=(e,t)=>as(e,t,_n),ls=(e,t)=>as(e,t,Tn),cs=(e,t,o)=>as(e,t,Wn(o)),ds=(e,t)=>as(e,t,En),us=(e,t)=>as(e,t,Bn),ms=(e,t,o)=>as(e,t,xn(o)),gs=(e,t,o)=>as(e,t,yn(o)),ps=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},hs=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return N(e,((n,s)=>{const r=ae(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=ae(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!K(t,(e=>V(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o};hs([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const fs=(e,t)=>((e,t)=>({[e]:t}))(e,t),bs=e=>(e=>{const t={};return N(e,(e=>{t[e.key]=e.value})),t})(e),vs=e=>p(e)?e:_,ys=(e,t,o)=>{let n=e.dom;const s=vs(o);for(;n.parentNode;){n=n.parentNode;const e=Fe(n),o=t(e);if(o.isSome())return o;if(s(e))break}return B.none()},xs=(e,t,o)=>{const n=t(e),s=vs(o);return n.orThunk((()=>s(e)?B.none():ys(e,t,s)))},ws=(e,t)=>je(e.element,t.event.target),Ss={can:T,abort:_,run:b},ks=e=>{if(!ve(e,"can")&&!ve(e,"abort")&&!ve(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...Ss,...e}},Cs=y,Os=Cs("touchstart"),_s=Cs("touchmove"),Ts=Cs("touchend"),Es=Cs("touchcancel"),Bs=Cs("mousedown"),Ms=Cs("mousemove"),As=Cs("mouseout"),Ds=Cs("mouseup"),Fs=Cs("mouseover"),Is=Cs("focusin"),Vs=Cs("focusout"),Rs=Cs("keydown"),zs=Cs("keyup"),Hs=Cs("input"),Ps=Cs("change"),Ns=Cs("click"),Ls=Cs("transitioncancel"),Ws=Cs("transitionend"),Us=Cs("transitionstart"),js=Cs("selectstart"),Gs=e=>y("alloy."+e),$s={tap:Gs("tap")},qs=Gs("focus"),Xs=Gs("blur.post"),Ks=Gs("paste.post"),Ys=Gs("receive"),Js=Gs("execute"),Zs=Gs("focus.item"),Qs=$s.tap,er=Gs("longpress"),tr=Gs("sandbox.close"),or=Gs("typeahead.cancel"),nr=Gs("system.init"),sr=Gs("system.touchmove"),rr=Gs("system.touchend"),ar=Gs("system.scroll"),ir=Gs("system.resize"),lr=Gs("system.attached"),cr=Gs("system.detached"),dr=Gs("system.dismissRequested"),ur=Gs("system.repositionRequested"),mr=Gs("focusmanager.shifted"),gr=Gs("slotcontainer.visibility"),pr=Gs("change.tab"),hr=Gs("dismiss.tab"),fr=Gs("highlight"),br=Gs("dehighlight"),vr=(e,t)=>{Sr(e,e.element,t,{})},yr=(e,t,o)=>{Sr(e,e.element,t,o)},xr=e=>{vr(e,Js())},wr=(e,t,o)=>{Sr(e,t,o,{})},Sr=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},kr=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},Cr=e=>bs(e),Or=(e,t)=>({key:e,value:ks({abort:t})}),_r=e=>({key:e,value:ks({run:(e,t)=>{t.event.prevent()}})}),Tr=(e,t)=>({key:e,value:ks({run:t})}),Er=(e,t,o)=>({key:e,value:ks({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),Br=e=>t=>({key:e,value:ks({run:(e,o)=>{ws(e,o)&&t(e,o)}})}),Mr=(e,t,o)=>((e,t)=>Tr(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{kr(t,t.element,e,n)}))})))(e,t.partUids[o]),Ar=(e,t)=>Tr(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>xs(n.target,(t=>e.getSystem().getByDom(t).toOptional()),_).getOr(e)));t(e,s,o)})),Dr=e=>Tr(e,((e,t)=>{t.cut()})),Fr=e=>Tr(e,((e,t)=>{t.stop()})),Ir=(e,t)=>Br(e)(t),Vr=Br(lr()),Rr=Br(cr()),zr=Br(nr()),Hr=(jr=Js(),e=>Tr(jr,e)),Pr=e=>e.dom.innerHTML,Nr=(e,t)=>{const o=$e(e).dom,n=Fe(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,Qe(Fe(o))})(t,o);Bo(n,s),Mo(e),Eo(e,n)},Lr=e=>nt(e)?"#shadow-root":(e=>{const t=Ae("div"),o=Fe(e.dom.cloneNode(!0));return Eo(t,o),Pr(t)})((e=>((e,t)=>Fe(e.dom.cloneNode(!1)))(e))(e)),Wr=e=>Lr(e),Ur=Cr([((e,t)=>({key:e,value:ks({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>je(t,e.element)&&!je(t,o))(e,n,s)||(console.warn(qs()+" did not get interpreted by the desired target. \nOriginator: "+Wr(n)+"\nTarget: "+Wr(s)+"\nCheck the "+qs()+" event handlers"),!1)}})}))(qs())]);var jr,Gr=Object.freeze({__proto__:null,events:Ur});let $r=0;const qr=e=>{const t=(new Date).getTime(),o=Math.floor(1e9*Math.random());return $r++,e+"_"+o+$r+String(t)},Xr=y("alloy-id-"),Kr=y("data-alloy-id"),Yr=Xr(),Jr=Kr(),Zr=(e,t)=>{Object.defineProperty(e.dom,Jr,{value:t,writable:!0})},Qr=e=>{const t=ze(e)?e.dom[Jr]:null;return B.from(t)},ea=e=>qr(e),ta=x,oa=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+Wr(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:y("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:_}},na=oa(),sa=e=>P(e,(e=>Oe(e,"/*")?e.substring(0,e.length-"/*".length):e)),ra=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:sa(r)}),e},aa=qr("alloy-premade"),ia=e=>(Object.defineProperty(e.element.dom,aa,{value:e.uid,writable:!0}),fs(aa,e)),la=e=>fe(e,aa),ca=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:sa(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),da={init:()=>ua({readState:y("No State required")})},ua=e=>e,ma=(e,t)=>{const o={};return le(e,((e,n)=>{le(e,((e,s)=>{const r=fe(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},ga=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),pa=e=>e.cHandler,ha=(e,t)=>({name:e,handler:t}),fa=(e,t)=>{const o={};return N(e,(e=>{o[e.name()]=e.handlers(t)})),o},ba=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const t=ee(o,((t,o)=>{const s=t.name,r=o.name,a=n.indexOf(s),i=n.indexOf(r);if(-1===a)throw new Error("The ordering for "+e+" does not have an entry for "+s+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));return a<i?-1:i<a?1:0}));return $o.value(t)}catch(e){return $o.error([e])}})("Event: "+o,0,e,n).map((e=>(e=>{const t=((e,t)=>(...t)=>j(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=((e,t)=>(...t)=>j(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{N(e,(e=>{e.run.apply(void 0,t)}))}}})(P(e,(e=>e.handler))))):((e,t)=>$o.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(P(t,(e=>e.name)),null,2)]))(o,e)},va=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return N(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,$o.error(q(n))):((e,t)=>0===e.length?$o.value(t):$o.value(nn(t,sn.apply(void 0,e))))(o.values,t);var n})(ge(e,((e,o)=>(1===e.length?$o.value(e[0].handler):ba(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:T,abort:_,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?W(t[o],(t=>R(e,(e=>e.name===t)))).join(" > "):e[0].name;return fs(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),ya="alloy.base.behaviour",xa=yn([Nn("dom","dom",{tag:"required",process:{}},yn([Un("tag"),rs("styles",{}),rs("classes",[]),rs("attributes",{}),Zn("value"),Zn("innerHtml")])),Un("components"),Un("uid"),rs("events",{}),rs("apis",{}),Nn("eventOrder","eventOrder",(Ga={[Js()]:["disabling",ya,"toggling","typeaheadevents"],[qs()]:[ya,"focusing","keying"],[nr()]:[ya,"disabling","toggling","representing"],[Hs()]:[ya,"representing","streaming","invalidating"],[cr()]:[ya,"representing","item-events","tooltipping"],[Bs()]:["focusing",ya,"item-type-events"],[Os()]:["focusing",ya,"item-type-events"],[Fs()]:["item-type-events","tooltipping"],[Ys()]:["receiving","reflecting","tooltipping"]},ln(y(Ga))),Cn()),Zn("domModification")]),wa=e=>e.events,Sa=(e,t)=>{const o=ft(e,t);return void 0===o||""===o?[]:o.split(" ")},ka=e=>void 0!==e.dom.classList,Ca=e=>Sa(e,"class"),Oa=(e,t)=>{ka(e)?e.dom.classList.add(t):((e,t)=>{((e,t,o)=>{const n=Sa(e,t).concat([o]);pt(e,t,n.join(" "))})(e,"class",t)})(e,t)},_a=(e,t)=>{ka(e)?e.dom.classList.remove(t):((e,t)=>{((e,t,o)=>{const n=W(Sa(e,t),(e=>e!==o));n.length>0?pt(e,t,n.join(" ")):yt(e,t)})(e,"class",t)})(e,t),(e=>{0===(ka(e)?e.dom.classList:Ca(e)).length&&yt(e,"class")})(e)},Ta=(e,t)=>ka(e)&&e.dom.classList.contains(t),Ea=(e,t)=>{N(t,(t=>{Oa(e,t)}))},Ba=(e,t)=>{N(t,(t=>{_a(e,t)}))},Ma=e=>e.dom.value,Aa=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},Da=(e,t,o)=>{o.fold((()=>Eo(e,t)),(e=>{je(e,t)||(Oo(e,t),Ao(e))}))},Fa=(e,t,o)=>{const n=P(t,o),s=Qe(e);return N(s.slice(n.length),Ao),n},Ia=(e,t,o,n)=>{const s=et(e,t),r=n(o,s),a=((e,t,o)=>et(e,t).map((e=>{if(o.exists((t=>!je(t,e)))){const t=o.map(Ve).getOr("span"),n=Ae(t);return Oo(e,n),n}return e})))(e,t,s);return Da(e,r.element,a),r},Va=(e,t)=>{const o=ae(e),n=ae(t);return{toRemove:J(n,o),toSet:((e,o)=>{const n={},s={};return me(e,((e,o)=>!be(t,o)||e!==t[o]),ue(n),ue(s)),{t:n,f:s}})(e).t}},Ra=(e,t)=>{const{class:o,style:n,...s}=(e=>j(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=Va(e.attributes,s),i=Et(t),{toSet:l,toRemove:c}=Va(e.styles,i),d=(e=>ka(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):Ca(e))(t),u=J(d,e.classes),m=J(e.classes,d);return N(a,(e=>yt(t,e))),ht(t,r),Ea(t,m),Ba(t,u),N(c,(e=>Mt(t,e))),kt(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{Fa(e,t,((t,o)=>{const n=et(e,o);return Da(e,t,n),t}))})(t,o)}),(e=>{Nr(t,e)})),(()=>{const o=t;e.value.filter((e=>e!==Ma(o))).each((e=>Aa(o,e)))})(),t},za=e=>{const t=(e=>{const t=fe(e,"behaviours").getOr({});return X(ae(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=P(t,(e=>ss(e.name(),[Un("config"),rs("state",da)]))),n=Vn("component.behaviours",yn(o),e.behaviours).fold((t=>{throw new Error(Hn(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),x);return{list:t,data:ce(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return y(t)}))}})(e,t))(e,t)},Ha=(e,t)=>{const o=()=>m,n=ps(na),s=Rn((e=>Vn("custom.definition",xa,e))(e)),r=za(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:P(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>ga({})),ga))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};N(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=ma(s,((e,t)=>({name:e,modification:t}))),a=e=>U(e,((e,t)=>({...t.modification,...e})),{}),i=U(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return ga({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=((e,t)=>{const o=t.filter((t=>Ve(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>be(e.dom,aa))(t))).bind((t=>((e,t)=>{try{const o=Ra(e,t);return B.some(o)}catch(e){return B.none()}})(e,t))).getOrThunk((()=>(e=>{const t=Ae(e.tag);ht(t,e.attributes),Ea(t,e.classes),kt(t,e.styles),e.innerHtml.each((e=>Nr(t,e)));const o=e.domChildren;return Bo(t,o),e.value.each((e=>{Aa(t,e)})),t})(e)));return Zr(o,e.uid),o})(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":wa(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...fa(t,e)};return ma(n,ha)})(e,o,n);return va(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=ps(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(oa(o))},element:c,syncComponents:()=>{const e=Qe(c),t=X(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},Pa=e=>{const t=De(e);return Na({element:t})},Na=e=>{const t=zn("external.component",vn([Un("element"),Zn("uid")]),e),o=ps(oa()),n=t.uid.getOrThunk((()=>ea("external")));Zr(t.element,n);const s={uid:n,getSystem:o.get,config:B.none,hasConfigured:_,connect:e=>{o.set(e)},disconnect:()=>{o.set(oa((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:y("No state"),syncComponents:b,components:y([]),events:{}};return ia(s)},La=ea,Wa=(e,t)=>la(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=ta(e),s=((e,t)=>{const o=fe(e,"components").getOr([]);return t.fold((()=>P(o,Ua)),(e=>P(o,((t,o)=>Wa(t,et(e,o))))))})(n,t),r={...n,events:{...Gr,...o},components:s};return $o.value(Ha(r,t))})((e=>be(e,"uid"))(e)?e:{uid:La(""),...e},t).getOrDie())),Ua=e=>Wa(e,B.none()),ja=ia;var Ga,$a=(e,t,o,n,s)=>e(o,n)?B.some(o):p(s)&&s(o)?B.none():t(o,n,s);const qa=(e,t,o)=>{let n=e.dom;const s=p(o)?o:_;for(;n.parentNode;){n=n.parentNode;const e=Fe(n);if(t(e))return B.some(e);if(s(e))break}return B.none()},Xa=(e,t,o)=>$a(((e,t)=>t(e)),qa,e,t,o),Ka=(e,t,o)=>Xa(e,t,o).isSome(),Ya=(e,t,o)=>qa(e,(e=>We(e,t)),o),Ja=(e,t)=>((e,o)=>G(e.dom.childNodes,(e=>{return o=Fe(e),We(o,t);var o})).map(Fe))(e),Za=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Ue(o)?B.none():B.from(o.querySelector(e)).map(Fe)})(t,e),Qa=(e,t,o)=>$a(((e,t)=>We(e,t)),Ya,e,t,o),ei="aria-controls",ti=()=>{const e=qr(ei);return{id:e,link:t=>{pt(t,ei,e)},unlink:e=>{yt(e,ei)}}},oi=(e,t)=>Ka(t,(t=>je(t,e.element)),_)||((e,t)=>(e=>Xa(e,(e=>{if(!ze(e))return!1;const t=ft(e,"id");return void 0!==t&&t.indexOf(ei)>-1})).bind((e=>{const t=ft(e,"id"),o=at(e);return Za(o,`[${ei}="${t}"]`)})))(t).exists((t=>oi(e,t))))(e,t);var ni;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(ni||(ni={}));const si=ps({}),ri=["alloy/data/Fields","alloy/debugging/Debugging"],ai=(e,t,o)=>((e,t,o)=>{switch(fe(si.get(),e).orThunk((()=>{const t=ae(si.get());return re(t,(t=>e.indexOf(t)>-1?B.some(si.get()[t]):B.none()))})).getOr(ni.NORMAL)){case ni.NORMAL:return o(ii());case ni.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();V(["mousemove","mouseover","mouseout",nr()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:P(o,(e=>V(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+Wr(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case ni.STOP:return!0}})(e,t,o),ii=y({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),li=y([Un("menu"),Un("selectedMenu")]),ci=y([Un("item"),Un("selectedItem")]);y(yn(ci().concat(li())));const di=y(yn(ci())),ui=Kn("initSize",[Un("numColumns"),Un("numRows")]),mi=()=>Kn("markers",[Un("backgroundMenu")].concat(li()).concat(ci())),gi=e=>Kn("markers",P(e,Un)),pi=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");G(t,(e=>e.indexOf("alloy")>0&&!R(ri,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),Nn(t,t,o,Fn((e=>$o.value(((...t)=>e.apply(void 0,t))))))),hi=e=>pi(0,e,an(b)),fi=e=>pi(0,e,an(B.none)),bi=e=>pi(0,e,{tag:"required",process:{}}),vi=e=>pi(0,e,{tag:"required",process:{}}),yi=(e,t)=>Ln(e,y(t)),xi=e=>Ln(e,x),wi=y(ui),Si=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),ki=hs([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Ci=ki.southeast,Oi=ki.southwest,_i=ki.northeast,Ti=ki.northwest,Ei=ki.south,Bi=ki.north,Mi=ki.east,Ai=ki.west,Di=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},Fi=(e,t,o)=>Math.min(Math.max(e,t),o),Ii=(e,t)=>Z(["left","right","top","bottom"],(o=>fe(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),Vi="layout",Ri=e=>e.x,zi=(e,t)=>e.x+e.width/2-t.width/2,Hi=(e,t)=>e.x+e.width-t.width,Pi=(e,t)=>e.y-t.height,Ni=e=>e.y+e.height,Li=(e,t)=>e.y+e.height/2-t.height/2,Wi=(e,t,o)=>Si(Ri(e),Ni(e),o.southeast(),Ci(),"southeast",Ii(e,{left:1,top:3}),Vi),Ui=(e,t,o)=>Si(Hi(e,t),Ni(e),o.southwest(),Oi(),"southwest",Ii(e,{right:0,top:3}),Vi),ji=(e,t,o)=>Si(Ri(e),Pi(e,t),o.northeast(),_i(),"northeast",Ii(e,{left:1,bottom:2}),Vi),Gi=(e,t,o)=>Si(Hi(e,t),Pi(e,t),o.northwest(),Ti(),"northwest",Ii(e,{right:0,bottom:2}),Vi),$i=(e,t,o)=>Si(zi(e,t),Pi(e,t),o.north(),Bi(),"north",Ii(e,{bottom:2}),Vi),qi=(e,t,o)=>Si(zi(e,t),Ni(e),o.south(),Ei(),"south",Ii(e,{top:3}),Vi),Xi=(e,t,o)=>Si((e=>e.x+e.width)(e),Li(e,t),o.east(),Mi(),"east",Ii(e,{left:0}),Vi),Ki=(e,t,o)=>Si(((e,t)=>e.x-t.width)(e,t),Li(e,t),o.west(),Ai(),"west",Ii(e,{right:1}),Vi),Yi=()=>[Wi,Ui,ji,Gi,qi,$i,Xi,Ki],Ji=()=>[Ui,Wi,Gi,ji,qi,$i,Xi,Ki],Zi=()=>[ji,Gi,Wi,Ui,$i,qi],Qi=()=>[Gi,ji,Ui,Wi,$i,qi],el=()=>[Wi,Ui,ji,Gi,qi,$i],tl=()=>[Ui,Wi,Gi,ji,qi,$i];var ol=Object.freeze({__proto__:null,events:e=>Cr([Tr(Ys(),((t,o)=>{const n=e.channels,s=ae(n),r=o,a=((e,t)=>t.universal?e:W(e,(e=>V(t.channels,e))))(s,r);N(a,(e=>{const o=n[e],s=o.schema,a=zn("channel["+e+"] data\nReceiver: "+Wr(t.element),s,r.data);o.onReceive(t,a)}))}))])}),nl=[jn("channels",In($o.value,vn([bi("onReceive"),rs("schema",Cn())])))];const sl=(e,t,o)=>zr(((n,s)=>{o(n,e,t)})),rl=e=>({key:e,value:void 0}),al=(e,t,o,n,s,r,a)=>{const i=e=>ve(e,o)?e[o]():B.none(),l=ce(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:sa(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:y(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...ce(r,((e,t)=>ra(e,t))),...l,revoke:S(rl,o),config:t=>{const n=zn(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:jt((()=>zn(o+"-config",e,t))),initialConfig:t,state:a}}},schema:y(t),exhibit:(e,t)=>we(i(e),fe(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>ga({}))),name:y(o),handlers:e=>i(e).map((e=>fe(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},il=e=>bs(e),ll=vn([Un("fields"),Un("name"),rs("active",{}),rs("apis",{}),rs("state",da),rs("extra",{})]),cl=e=>{const t=zn("Creating behaviour: "+e.name,ll,e);return((e,t,o,n,s,r)=>{const a=vn(e),i=ss(t,[("config",l=e,Qn("config",vn(l)))]);var l;return al(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},dl=vn([Un("branchKey"),Un("branches"),Un("name"),rs("active",{}),rs("apis",{}),rs("state",da),rs("extra",{})]),ul=e=>{const t=zn("Creating behaviour: "+e.name,dl,e);return((e,t,o,n,s,r)=>{const a=e,i=ss(t,[Qn("config",e)]);return al(a,i,t,o,n,s,r)})(Pn(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},ml=y(void 0),gl=cl({fields:nl,name:"receiving",active:ol});var pl=Object.freeze({__proto__:null,exhibit:(e,t)=>ga({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const hl=e=>e.dom.focus(),fl=e=>{const t=at(e).dom;return e.dom===t.activeElement},bl=(e=Ro())=>B.from(e.dom.activeElement).map(Fe),vl=e=>bl(at(e)).filter((t=>e.dom.contains(t.dom))),yl=(e,t)=>{const o=at(t),n=bl(o).bind((e=>{const o=t=>je(e,t);return o(t)?B.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=Fe(e.childNodes[n]);if(t(s))return B.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return B.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{bl(o).filter((t=>je(t,e))).fold((()=>{hl(e)}),b)})),s},xl=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},wl=(e,t)=>{Ct(e,(e=>({...e,position:B.some(e.position)}))(t))},Sl=hs([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),kl=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=B.some(i),m=B.some(l),g=B.some(c),p=B.some(d),h=B.none();return t.direction.fold((()=>xl(e,u,m,h,h)),(()=>xl(e,h,m,g,h)),(()=>xl(e,u,h,h,p)),(()=>xl(e,h,h,g,p)),(()=>xl(e,u,m,h,h)),(()=>xl(e,u,h,h,p)),(()=>xl(e,u,m,h,h)),(()=>xl(e,h,m,g,h)))},Cl=(e,t)=>e.fold((()=>{const e=t.rect;return xl("absolute",B.some(e.x),B.some(e.y),B.none(),B.none())}),((e,o,n,s)=>kl("absolute",t,e,o,n,s)),((e,o,n,s)=>kl("fixed",t,e,o,n,s))),Ol=(e,t)=>{const o=S(Po,t),n=e.fold(o,o,(()=>{const e=Do();return Po(t).translate(-e.left,-e.top)})),s=Ut(t),r=Vt(t);return No(n.left,n.top,s,r)},_l=(e,t)=>t.fold((()=>e.fold(Uo,Uo,No)),(t=>e.fold(t,t,(()=>{const o=t(),n=Tl(e,o.x,o.y);return No(n.left,n.top,o.width,o.height)})))),Tl=(e,t,o)=>{const n=zt(t,o);return e.fold(y(n),y(n),(()=>{const e=Do();return n.translate(-e.left,-e.top)}))};Sl.none;const El=Sl.relative,Bl=Sl.fixed,Ml="data-alloy-placement",Al=e=>bt(e,Ml),Dl=hs([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),Fl=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?Fi(i,e.y,e.bottom):Fi(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return No(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=No(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=Fi(a,o,d),g=Fi(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return No(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=y(t.bottom-o.y),s=y(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=y(t.right-o.x),i=y(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),x={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?Dl.fit(x):Dl.nofit(x,m,g,f)},Il=e=>{const t=ps(B.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(B.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(B.some(e))}}},Vl=()=>Il((e=>e.unbind())),Rl=()=>{const e=Il(b);return{...e,on:t=>e.get().each(t)}},zl=T,Hl=(e,t,o)=>((e,t,o,n)=>ko(e,t,o,n,!1))(e,t,zl,o),Pl=(e,t,o)=>((e,t,o,n)=>ko(e,t,o,n,!0))(e,t,zl,o),Nl=So,Ll=["top","bottom","right","left"],Wl="data-alloy-transition-timer",Ul=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>((e,t)=>K(t,(t=>Ta(e,t))))(e,t.classes))(e,n)){St(e,"position",o.position);const a=Ol(t,e),l=Cl(t,{...s,rect:a}),c=Z(Ll,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return pe(t,((t,n)=>!((e,t,o=w)=>we(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(Ct(e,c),i&&((e,t)=>{Ea(e,t.classes),bt(e,Wl).each((t=>{clearTimeout(parseInt(t,10)),yt(e,Wl)})),((e,t)=>{const o=Vl(),n=Vl();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return je(t.target,e)&&!Te(n)&&V(Ll,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===Ws())&&(clearTimeout(s),yt(e,Wl),Ba(e,t.classes))}},l=Hl(e,Us(),(t=>{a(t)&&(l.unbind(),o.set(Hl(e,Ws(),i)),n.set(Hl(e,Ls(),i)))})),c=(e=>{const t=t=>{const o=Ot(e,t).split(/\s*,\s*/);return W(o,Te)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return Oe(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return j(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);requestAnimationFrame((()=>{s=setTimeout(i,c+17),pt(e,Wl,s)}))})(e,t)})(e,n),At(e))}else Ba(e,n.classes)},jl=(e,t)=>{((e,t)=>{const o=Ft.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);St(e,"max-height",o+"px")})(e,Math.floor(t))},Gl=y(((e,t)=>{jl(e,t),kt(e,{"overflow-x":"hidden","overflow-y":"auto"})})),$l=y(((e,t)=>{jl(e,t)})),ql=(e,t,o)=>void 0===e[t]?o:e[t],Xl=(e,t,o,n)=>{const s=((e,t,o,n)=>{Mt(t,"max-height"),Mt(t,"max-width");const s={width:Ut(r=t),height:Vt(r)};var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=Fl(m,a,i,r);return g.fold(y(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:Dl.nofit(l,c,d,u)))};return j(t,((e,t)=>{const o=S(l,t);return e.fold(y(e),o)}),Dl.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:Ci(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(x,x)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=Cl(o.origin,t);o.transition.each((s=>{Ul(e,o.origin,n,s,t,o.lastPlacement)})),wl(e,n)})(t,s,n),((e,t)=>{((e,t)=>{pt(e,Ml,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;Ba(e,o.off),Ea(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},Kl=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],Yl=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>fe(o,e).getOr([]),i=(e,t,o)=>{const n=J(Kl,o);return{offset:zt(e,t),classesOn:X(o,a),classesOff:X(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},Jl=()=>Yl(0,0,{}),Zl=x,Ql=(e,t)=>o=>"rtl"===ec(o)?t:e,ec=e=>"rtl"===Ot(e,"direction")?"rtl":"ltr";var tc;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(tc||(tc={}));const oc="data-alloy-vertical-dir",nc=e=>Ka(e,(e=>ze(e)&&ft(e,"data-alloy-vertical-dir")===tc.BottomToTop)),sc=()=>ss("layouts",[Un("onLtr"),Un("onRtl"),Zn("onBottomLtr"),Zn("onBottomRtl")]),rc=(e,t,o,n,s,r,a)=>{const i=a.map(nc).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return Ql(d,u)(e)};var ac=[Un("hotspot"),Zn("bubble"),rs("overrides",{}),sc(),yi("placement",((e,t,o)=>{const n=t.hotspot,s=Ol(o,n.element),r=rc(e.element,t,el(),tl(),Zi(),Qi(),B.some(t.hotspot.element));return B.some(Zl({anchorBox:s,bubble:t.bubble.getOr(Jl()),overrides:t.overrides,layouts:r,placer:B.none()}))}))],ic=[Un("x"),Un("y"),rs("height",0),rs("width",0),rs("bubble",Jl()),rs("overrides",{}),sc(),yi("placement",((e,t,o)=>{const n=Tl(o,t.x,t.y),s=No(n.left,n.top,t.width,t.height),r=rc(e.element,t,Yi(),Ji(),Yi(),Ji(),B.none());return B.some(Zl({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r,placer:B.none()}))}))];const lc=hs([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),cc=e=>e.fold(x,((e,t,o)=>e.translate(-t,-o))),dc=e=>e.fold(x,x),uc=e=>j(e,((e,t)=>e.translate(t.left,t.top)),zt(0,0)),mc=e=>{const t=P(e,dc);return uc(t)},gc=lc.screen,pc=lc.absolute,hc=(e,t,o)=>{const n=$e(e.element),s=Do(n),r=((e,t,o)=>{const n=Ke(o.root).dom;return B.from(n.frameElement).map(Fe).filter((t=>{const o=$e(t),n=$e(e.element);return je(o,n)})).map(Pt)})(e,0,o).getOr(s);return pc(r,s.left,s.top)},fc=(e,t,o,n)=>{const s=gc(zt(e,t));return B.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},bc=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>mc(r),l=()=>mc(r),c=()=>(e=>{const t=P(e,cc);return uc(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?Zi():el(),m=o.showAbove?Qi():tl(),g=rc(s,o,u,m,u,m,B.none());var p,h,f,b;return Zl({anchorBox:d,bubble:o.bubble.getOr(Jl()),overrides:o.overrides,layouts:g,placer:B.none()})}));var vc=[Un("node"),Un("root"),Zn("bubble"),sc(),rs("overrides",{}),rs("showAbove",!1),yi("placement",((e,t,o)=>{const n=hc(e,0,t);return t.node.filter(dt).bind((s=>{const r=s.dom.getBoundingClientRect(),a=fc(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return bc(a,n,t,o,i)}))}))];const yc=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),xc=hs([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),wc=(xc.before,xc.on,xc.after,e=>e.fold(x,x,x)),Sc=hs([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),kc={domRange:Sc.domRange,relative:Sc.relative,exact:Sc.exact,exactFromRange:e=>Sc.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>Fe(e.startContainer),relative:(e,t)=>wc(e),exact:(e,t,o,n)=>e}))(e);return Ke(t)},range:yc},Cc=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},Oc=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},_c=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),Tc=hs([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Ec=(e,t,o)=>t(Fe(o.startContainer),o.startOffset,Fe(o.endContainer),o.endOffset),Bc=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:y(e),rtl:B.none}),relative:(t,o)=>({ltr:jt((()=>Cc(e,t,o))),rtl:jt((()=>B.some(Cc(e,o,t))))}),exact:(t,o,n,s)=>({ltr:jt((()=>Oc(e,t,o,n,s))),rtl:jt((()=>B.some(Oc(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>Tc.rtl(Fe(e.endContainer),e.endOffset,Fe(e.startContainer),e.startOffset))).getOrThunk((()=>Ec(0,Tc.ltr,o))):Ec(0,Tc.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});Tc.ltr,Tc.rtl;const Mc=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Ue(o)?[]:P(o.querySelectorAll(e),Fe)})(t,e),Ac=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return B.some(yc(Fe(t.startContainer),t.startOffset,Fe(o.endContainer),o.endOffset))}return B.none()},Dc=e=>{if(null===e.anchorNode||null===e.focusNode)return Ac(e);{const t=Fe(e.anchorNode),o=Fe(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=$e(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=je(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?B.some(yc(t,e.anchorOffset,o,e.focusOffset)):Ac(e)}},Fc=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?B.some(o).map(_c):B.none()})(Bc(e,t)),Ic=((e,t)=>{const o=t=>e(t)?B.from(t.dom.nodeValue):B.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(He),Vc=(e,t)=>({element:e,offset:t}),Rc=(e,t)=>He(e)?Vc(e,t):((e,t)=>{const o=Qe(e);if(0===o.length)return Vc(e,t);if(t<o.length)return Vc(o[t],0);{const e=o[o.length-1],t=He(e)?(e=>Ic.get(e))(e).length:Qe(e).length;return Vc(e,t)}})(e,t),zc=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>B.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(Dc))(e)))().map((e=>{const t=Rc(e.start,e.soffset),o=Rc(e.finish,e.foffset);return kc.range(t.element,t.offset,o.element,o.offset)}));var Hc=[Zn("getSelection"),Un("root"),Zn("bubble"),sc(),rs("overrides",{}),rs("showAbove",!1),yi("placement",((e,t,o)=>{const n=Ke(t.root).dom,s=hc(e,0,t),r=zc(n,t).bind((e=>{const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?B.some(t).map(_c):B.none()})(Bc(e,t)))(n,kc.exactFromRange(e)).orThunk((()=>{const t=De("\ufeff");Oo(e.start,t);const o=Fc(n,kc.exact(t,0,t,1));return Ao(t),o}));return t.bind((e=>fc(e.left,e.top,e.width,e.height)))})),a=zc(n,t).bind((e=>ze(e.start)?B.some(e.start):Je(e.start))).getOr(e.element);return bc(r,s,t,o,a)}))];const Pc="link-layout",Nc=e=>e.x+e.width,Lc=(e,t)=>e.x-t.width,Wc=(e,t)=>e.y-t.height+e.height,Uc=e=>e.y,jc=(e,t,o)=>Si(Nc(e),Uc(e),o.southeast(),Ci(),"southeast",Ii(e,{left:0,top:2}),Pc),Gc=(e,t,o)=>Si(Lc(e,t),Uc(e),o.southwest(),Oi(),"southwest",Ii(e,{right:1,top:2}),Pc),$c=(e,t,o)=>Si(Nc(e),Wc(e,t),o.northeast(),_i(),"northeast",Ii(e,{left:0,bottom:3}),Pc),qc=(e,t,o)=>Si(Lc(e,t),Wc(e,t),o.northwest(),Ti(),"northwest",Ii(e,{right:1,bottom:3}),Pc),Xc=()=>[jc,Gc,$c,qc],Kc=()=>[Gc,jc,qc,$c];var Yc=[Un("item"),sc(),rs("overrides",{}),yi("placement",((e,t,o)=>{const n=Ol(o,t.item.element),s=rc(e.element,t,Xc(),Kc(),Xc(),Kc(),B.none());return B.some(Zl({anchorBox:n,bubble:Jl(),overrides:t.overrides,layouts:s,placer:B.none()}))}))],Jc=Pn("type",{selection:Hc,node:vc,hotspot:ac,submenu:Yc,makeshift:ic});const Zc=[Jn("classes",Tn),cs("mode","all",["all","layout","placement"])],Qc=[rs("useFixed",_),Zn("getBounds")],ed=[jn("anchor",Jc),ss("transition",Zc)],td=(e,t,o,n,s,r,a)=>((e,t,o,n,s,r,a,i)=>{const l=ql(a,"maxHeightFunction",Gl()),c=ql(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:_l(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return Xl(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(o.anchorBox,t),s.element,o.bubble,o.layouts,r,n,o.overrides,a),od=(e,t,o,n,s,r)=>{const a=r.map(Lo);return nd(e,t,o,n,s,a)},nd=(e,t,o,n,s,r)=>{const a=zn("placement.info",yn(ed),s),i=a.anchor,l=n.element,c=o.get(n.uid);yl((()=>{St(l,"position","fixed");const s=Tt(l,"visibility");St(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return Bl(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=Pt(e.element),o=e.element.dom.getBoundingClientRect();return El(t.left,t.top,o.width,o.height)})(e),u=i.placement,m=r.map(y).or(t.getBounds);u(e,i,d).each((t=>{const s=t.placer.getOr(td)(e,d,t,m,n,c,a.transition);o.set(n.uid,s)})),s.fold((()=>{Mt(l,"visibility")}),(e=>{St(l,"visibility",e)})),Tt(l,"left").isNone()&&Tt(l,"top").isNone()&&Tt(l,"right").isNone()&&Tt(l,"bottom").isNone()&&ye(Tt(l,"position"),"fixed")&&Mt(l,"position")}),l)};var sd=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{od(e,t,o,n,s,B.none())},positionWithin:od,positionWithinBounds:nd,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;N(["position","left","right","top","bottom"],(e=>Mt(s,e))),(e=>{yt(e,Ml)})(s),o.clear(n.uid)}});const rd=cl({fields:Qc,name:"positioning",active:pl,apis:sd,state:Object.freeze({__proto__:null,init:()=>{let e={};return ua({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>fe(e,t)})}})}),ad=e=>e.getSystem().isConnected(),id=e=>{vr(e,cr());const t=e.components();N(t,id)},ld=e=>{const t=e.components();N(t,ld),vr(e,lr())},cd=(e,t)=>{e.getSystem().addToWorld(t),dt(e.element)&&ld(t)},dd=e=>{id(e),e.getSystem().removeFromWorld(e)},ud=(e,t)=>{Eo(e.element,t.element)},md=(e,t)=>{gd(e,t,Eo)},gd=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),dt(e.element)&&ld(t),e.syncComponents()},pd=e=>{id(e),Ao(e.element),e.getSystem().removeFromWorld(e)},hd=e=>{const t=Ye(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));pd(e),t.each((e=>{e.syncComponents()}))},fd=e=>{const t=e.components();N(t,pd),Mo(e.element),e.syncComponents()},bd=(e,t)=>{vd(e,t,Eo)},vd=(e,t,o)=>{o(e,t.element);const n=Qe(t.element);N(n,(e=>{t.getByDom(e).each(ld)}))},yd=e=>{const t=Qe(e.element);N(t,(t=>{e.getByDom(t).each(id)})),Ao(e.element)},xd=(e,t,o,n)=>{o.get().each((t=>{fd(e)}));const s=t.getAttachPoint(e);md(s,e);const r=e.getSystem().build(n);return md(e,r),o.set(r),r},wd=(e,t,o,n)=>{const s=xd(e,t,o,n);return t.onOpen(e,s),s},Sd=(e,t,o)=>{o.get().each((n=>{fd(e),hd(e),t.onClose(e,n),o.clear()}))},kd=(e,t,o)=>o.isOpen(),Cd=(e,t,o)=>{const n=t.getAttachPoint(e);St(e.element,"position",rd.getMode(n)),((e,t,o,n)=>{Tt(e.element,t).fold((()=>{yt(e.element,o)}),(t=>{pt(e.element,o,t)})),St(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},Od=(e,t,o)=>{(e=>R(["top","left","right","bottom"],(t=>Tt(e,t).isSome())))(e.element)||Mt(e.element,"position"),((e,t,o)=>{bt(e.element,o).fold((()=>Mt(e.element,t)),(o=>St(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var _d=Object.freeze({__proto__:null,cloak:Cd,decloak:Od,open:wd,openWhileCloaked:(e,t,o,n,s)=>{Cd(e,t),wd(e,t,o,n),s(),Od(e,t)},close:Sd,isOpen:kd,isPartOf:(e,t,o,n)=>kd(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>xd(e,t,o,n)))}),Td=Object.freeze({__proto__:null,events:(e,t)=>Cr([Tr(tr(),((o,n)=>{Sd(o,e,t)}))])}),Ed=[hi("onOpen"),hi("onClose"),Un("isPartOf"),Un("getAttachPoint"),rs("cloakVisibilityAttr","data-precloak-visibility")],Bd=Object.freeze({__proto__:null,init:()=>{const e=Rl(),t=y("not-implemented");return ua({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const Md=cl({fields:Ed,name:"sandboxing",active:Td,apis:_d,state:Bd}),Ad=y("dismiss.popups"),Dd=y("reposition.popups"),Fd=y("mouse.released"),Id=vn([rs("isExtraPart",_),ss("fireEventInstead",[rs("event",dr())])]),Vd=e=>{const t=zn("Dismissal",Id,e);return{[Ad()]:{schema:vn([Un("target")]),onReceive:(e,o)=>{Md.isOpen(e)&&(Md.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>Md.close(e)),(t=>vr(e,t.event))))}}}},Rd=vn([ss("fireEventInstead",[rs("event",ur())]),Xn("doReposition")]),zd=e=>{const t=zn("Reposition",Rd,e);return{[Dd()]:{onReceive:e=>{Md.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>vr(e,t.event)))}}}},Hd=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},Pd=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var Nd=Object.freeze({__proto__:null,onLoad:Hd,onUnload:Pd,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),Ld=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[Vr(((o,n)=>{Hd(o,e,t)})),Rr(((o,n)=>{Pd(o,e,t)}))]:[sl(e,t,Hd)];return Cr(o)}});const Wd=()=>{const e=ps(null);return ua({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},Ud=()=>{const e=ps({}),t=ps({});return ua({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>fe(e.get(),o).orThunk((()=>fe(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};N(o,(e=>{r[e.value]=e,fe(e,"meta").each((t=>{fe(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var jd=Object.freeze({__proto__:null,memory:Wd,dataset:Ud,manual:()=>ua({readState:b}),init:e=>e.store.manager.state(e)});const Gd=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var $d=[Zn("initialValue"),Un("getFallbackEntry"),Un("getDataKey"),Un("setValue"),yi("manager",{setValue:Gd,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{Gd(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:Ud})],qd=[Un("getValue"),rs("setValue",b),Zn("initialValue"),yi("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:da.init})],Xd=[Zn("initialValue"),yi("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:Wd})],Kd=[as("store",{mode:"memory"},Pn("mode",{memory:Xd,manual:qd,dataset:$d})),hi("onSetValue"),rs("resetOnDom",!1)];const Yd=cl({fields:Kd,name:"representing",active:Ld,apis:Nd,extra:{setValueFrom:(e,t)=>{const o=Yd.getValue(t);Yd.setValue(e,o)}},state:jd}),Jd=(e,t)=>gs(e,{},P(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,Nn(o,o,{tag:"option",process:{}},mn((e=>Zo("The field: "+o+" is forbidden. "+n))));var o,n})).concat([Ln("dump",x)])),Zd=e=>e.dump,Qd=(e,t)=>({...il(t),...e.dump}),eu=Jd,tu=Qd,ou="placeholder",nu=hs([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),su=e=>be(e,"uiType"),ru=(e,t,o,n)=>((e,t,o,n)=>su(o)&&o.uiType===ou?((e,t,o,n)=>e.exists((e=>e!==o.owner))?nu.single(!0,y(o)):fe(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+ae(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):nu.single(!1,y(o)))(e,0,o,n).fold(((s,r)=>{const a=su(o)?r(t,o.config,o.validated):r(t),i=fe(a,"components").getOr([]),l=X(i,(o=>ru(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(su(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(x)(e)}return n(t)})),au=nu.single,iu=nu.multiple,lu=y(ou),cu=hs([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),du=rs("factory",{sketch:x}),uu=rs("schema",[]),mu=Un("name"),gu=Nn("pname","pname",rn((e=>"<alloy."+qr(e.name)+">")),Cn()),pu=Ln("schema",(()=>[Zn("preprocess")])),hu=rs("defaults",y({})),fu=rs("overrides",y({})),bu=yn([du,uu,mu,gu,hu,fu]),vu=yn([du,uu,mu,hu,fu]),yu=yn([du,uu,mu,gu,hu,fu]),xu=yn([du,pu,mu,Un("unit"),gu,hu,fu]),wu=e=>e.fold(B.some,B.none,B.some,B.some),Su=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},ku=(e,t)=>o=>{const n=zn("Converting part type",t,o);return e(n)},Cu=ku(cu.required,bu),Ou=ku(cu.external,vu),_u=ku(cu.optional,yu),Tu=ku(cu.group,xu),Eu=y("entirety");var Bu=Object.freeze({__proto__:null,required:Cu,external:Ou,optional:_u,group:Tu,asNamedPart:wu,name:Su,asCommon:e=>e.fold(x,x,x,x),original:Eu});const Mu=(e,t,o,n)=>nn(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),Au=(e,t)=>{const o={};return N(t,(t=>{wu(t).each((t=>{const n=Du(e,t.pname);o[t.name]=o=>{const s=zn("Part: "+t.name+" in "+e,yn(t.schema),o);return{...n,config:o,validated:s}}}))})),o},Du=(e,t)=>({uiType:lu(),owner:e,name:t}),Fu=(e,t,o)=>({uiType:lu(),owner:e,name:t,config:o,validated:{}}),Iu=e=>X(e,(e=>e.fold(B.none,B.some,B.none,B.none).map((e=>Kn(e.name,e.schema.concat([xi(Eu())])))).toArray())),Vu=e=>P(e,Su),Ru=(e,t,o)=>((e,t,o)=>{const n={},s={};return N(o,(e=>{e.fold((e=>{n[e.pname]=au(!0,((t,o,n)=>e.factory.sketch(Mu(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=y(e.factory.sketch(Mu(t,e,o[Eu()]),o))}),(e=>{n[e.pname]=au(!1,((t,o,n)=>e.factory.sketch(Mu(t,e,o,n))))}),(e=>{n[e.pname]=iu(!0,((t,o,n)=>{const s=t[e.name];return P(s,(o=>e.factory.sketch(nn(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:y(n),externals:y(s)}})(0,t,o),zu=(e,t,o)=>((e,t,o,n)=>{const s=ce(n,((e,t)=>((e,t)=>{let o=!1;return{name:y(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>X(o,(o=>ru(e,t,o,n))))(e,t,o,s);return le(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(B.some(e),t,t.components,o),Hu=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},Pu=(e,t,o)=>Hu(e,t,o).getOrDie("Could not find part: "+o),Nu=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return N(o,(e=>{n[e]=y(r.getByUid(s[e]))})),n},Lu=(e,t)=>{const o=e.getSystem();return ce(t.partUids,((e,t)=>y(o.getByUid(e))))},Wu=e=>ae(e.partUids),Uu=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return N(o,(e=>{n[e]=y(r.getByUid(s[e]).getOrDie())})),n},ju=(e,t)=>{const o=Vu(t);return bs(P(o,(t=>({key:t,value:e+"-"+t}))))},Gu=e=>Nn("partUids","partUids",ln((t=>ju(t.uid,e))),Cn());var $u=Object.freeze({__proto__:null,generate:Au,generateOne:Fu,schemas:Iu,names:Vu,substitutes:Ru,components:zu,defaultUids:ju,defaultUidsSchema:Gu,getAllParts:Lu,getAllPartNames:Wu,getPart:Hu,getPartOrDie:Pu,getParts:Nu,getPartsOrDie:Uu});const qu=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[Kn("parts",e)]:[]).concat([Un("uid"),rs("dom",{}),rs("components",[]),xi("originalSpec"),rs("debug.sketcher",{})]).concat(t))(n,s);return zn(e+" [SpecSchema]",vn(r.concat(t)),o)},Xu=(e,t,o,n,s)=>{const r=Ku(s),a=Iu(o),i=Gu(o),l=qu(e,t,r,a,[i]),c=Ru(0,l,o);return n(l,zu(e,l,c.internals()),r,c.externals())},Ku=e=>(e=>be(e,"uid"))(e)?e:{...e,uid:ea("uid")},Yu=vn([Un("name"),Un("factory"),Un("configFields"),rs("apis",{}),rs("extraApis",{})]),Ju=vn([Un("name"),Un("factory"),Un("configFields"),Un("partFields"),rs("apis",{}),rs("extraApis",{})]),Zu=e=>{const t=zn("Sketcher for "+e.name,Yu,e),o=ce(t.apis,ca),n=ce(t.extraApis,((e,t)=>ra(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=Ku(n);return o(qu(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},Qu=e=>{const t=zn("Sketcher for "+e.name,Ju,e),o=Au(t.name,t.partFields),n=ce(t.apis,ca),s=ce(t.extraApis,((e,t)=>ra(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>Xu(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},em=e=>Le("input")(e)&&"radio"!==ft(e,"type")||Le("textarea")(e);var tm=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const om=[Un("find")],nm=cl({fields:om,name:"composing",apis:tm}),sm=["input","button","textarea","select"],rm=(e,t,o)=>{(t.disabled()?um:mm)(e,t)},am=(e,t)=>!0===t.useNative&&V(sm,Ve(e.element)),im=e=>{pt(e.element,"disabled","disabled")},lm=e=>{yt(e.element,"disabled")},cm=e=>{pt(e.element,"aria-disabled","true")},dm=e=>{pt(e.element,"aria-disabled","false")},um=(e,t,o)=>{t.disableClass.each((t=>{Oa(e.element,t)})),(am(e,t)?im:cm)(e),t.onDisabled(e)},mm=(e,t,o)=>{t.disableClass.each((t=>{_a(e.element,t)})),(am(e,t)?lm:dm)(e),t.onEnabled(e)},gm=(e,t)=>am(e,t)?(e=>vt(e.element,"disabled"))(e):(e=>"true"===ft(e.element,"aria-disabled"))(e);var pm=Object.freeze({__proto__:null,enable:mm,disable:um,isDisabled:gm,onLoad:rm,set:(e,t,o,n)=>{(n?um:mm)(e,t)}}),hm=Object.freeze({__proto__:null,exhibit:(e,t)=>ga({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>Cr([Or(Js(),((t,o)=>gm(t,e))),sl(e,t,rm)])}),fm=[us("disabled",_),rs("useNative",!0),Zn("disableClass"),hi("onDisabled"),hi("onEnabled")];const bm=cl({fields:fm,name:"disabling",active:hm,apis:pm}),vm=(e,t,o,n)=>{const s=Mc(e.element,"."+t.highlightClass);N(s,(o=>{R(n,(e=>e.element===o))||(_a(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),vr(o,br())})))}))},ym=(e,t,o,n)=>{vm(e,t,0,[n]),xm(e,t,o,n)||(Oa(n.element,t.highlightClass),t.onHighlight(e,n),vr(n,fr()))},xm=(e,t,o,n)=>Ta(n.element,t.highlightClass),wm=(e,t,o)=>Za(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),Sm=(e,t,o)=>{const n=Mc(e.element,"."+t.itemClass);return(n.length>0?B.some(n[n.length-1]):B.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},km=(e,t,o,n)=>{const s=Mc(e.element,"."+t.itemClass);return $(s,(e=>Ta(e,t.highlightClass))).bind((t=>{const o=Di(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},Cm=(e,t,o)=>{const n=Mc(e.element,"."+t.itemClass);return xe(P(n,(t=>e.getSystem().getByDom(t).toOptional())))};var Om=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>vm(e,t,0,[]),dehighlight:(e,t,o,n)=>{xm(e,t,o,n)&&(_a(n.element,t.highlightClass),t.onDehighlight(e,n),vr(n,br()))},highlight:ym,highlightFirst:(e,t,o)=>{wm(e,t).each((n=>{ym(e,t,o,n)}))},highlightLast:(e,t,o)=>{Sm(e,t).each((n=>{ym(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=Mc(e.element,"."+t.itemClass);return B.from(s[n]).fold((()=>$o.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{ym(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=Cm(e,t);G(s,n).each((n=>{ym(e,t,o,n)}))},isHighlighted:xm,getHighlighted:(e,t,o)=>Za(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:wm,getLast:Sm,getPrevious:(e,t,o)=>km(e,t,0,-1),getNext:(e,t,o)=>km(e,t,0,1),getCandidates:Cm}),_m=[Un("highlightClass"),Un("itemClass"),hi("onHighlight"),hi("onDehighlight")];const Tm=cl({fields:_m,name:"highlighting",apis:Om}),Em=[8],Bm=[9],Mm=[13],Am=[27],Dm=[32],Fm=[37],Im=[38],Vm=[39],Rm=[40],zm=(e,t,o)=>{const n=Y(e.slice(0,t)),s=Y(e.slice(t+1));return G(n.concat(s),o)},Hm=(e,t,o)=>{const n=Y(e.slice(0,t));return G(n,o)},Pm=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return G(s.concat(n),o)},Nm=(e,t,o)=>{const n=e.slice(t+1);return G(n,o)},Lm=e=>t=>{const o=t.raw;return V(e,o.which)},Wm=e=>t=>K(e,(e=>e(t))),Um=e=>!0===e.raw.shiftKey,jm=e=>!0===e.raw.ctrlKey,Gm=k(Um),$m=(e,t)=>({matches:e,classification:t}),qm=(e,t,o)=>{t.exists((e=>o.exists((t=>je(t,e)))))||yr(e,mr(),{prevFocus:t,newFocus:o})},Xm=()=>{const e=e=>vl(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);qm(t,n,s)}}},Km=()=>{const e=e=>Tm.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Tm.highlight(t,e)}));const s=e(t);qm(t,n,s)}}};var Ym;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(Ym||(Ym={}));const Jm=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,G(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([rs("focusManager",Xm()),as("focusInside","onFocus",Fn((e=>V(["onFocus","onEnterOrSpace","onApi"],e)?$o.value(e):$o.error("Invalid value for focusInside")))),yi("handler",a),yi("state",t),yi("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==Ym.OnFocusMode?B.none():s(e).map((o=>Tr(qs(),((n,s)=>{o(n,e,t),s.stop()})))),i=[Tr(Rs(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=Lm(Dm.concat(Mm))(n.event);e.focusInside===Ym.OnEnterOrSpaceMode&&r&&ws(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),Tr(zs(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return Cr(a.toArray().concat(i))}};return a},Zm=e=>{const t=[Zn("onEscape"),Zn("onEnter"),rs("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),rs("firstTabstop",0),rs("useTabstopAt",T),Zn("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>Qa(t,e))).getOr(t);return It(o)>0},n=(e,t,n)=>{((e,t)=>{const n=Mc(e.element,t.selector),s=W(n,(e=>o(t,e)));return B.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},s=(e,t,n,s)=>{const r=Mc(e.element,n.selector);return((e,t)=>t.focusManager.get(e).bind((e=>Qa(e,t.selector))))(e,n).bind((t=>$(r,S(je,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?B.some(!0):B.none()),(t=>(s.focusManager.set(e,t),B.some(!0)))))(e,r,t,n,s)))))},r=y([$m(Wm([Um,Lm(Bm)]),((e,t,o)=>{const n=o.cyclic?zm:Hm;return s(e,0,o,n)})),$m(Lm(Bm),((e,t,o)=>{const n=o.cyclic?Pm:Nm;return s(e,0,o,n)})),$m(Lm(Am),((e,t,o)=>o.onEscape.bind((o=>o(e,t))))),$m(Wm([Gm,Lm(Mm)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),a=y([]);return Jm(t,da.init,r,a,(()=>B.some(n)))};var Qm=Zm(Ln("cyclic",_)),eg=Zm(Ln("cyclic",T));const tg=(e,t,o)=>em(o)&&Lm(Dm)(t.event)?B.none():((e,t,o)=>(wr(e,o,Js()),B.some(!0)))(e,0,o),og=(e,t)=>B.some(!0),ng=[rs("execute",tg),rs("useSpace",!1),rs("useEnter",!0),rs("useControlEnter",!1),rs("useDown",!1)],sg=(e,t,o)=>o.execute(e,t,e.element);var rg=Jm(ng,da.init,((e,t,o,n)=>{const s=o.useSpace&&!em(e.element)?Dm:[],r=o.useEnter?Mm:[],a=o.useDown?Rm:[],i=s.concat(r).concat(a);return[$m(Lm(i),sg)].concat(o.useControlEnter?[$m(Wm([jm,Lm(Mm)]),sg)]:[])}),((e,t,o,n)=>o.useSpace&&!em(e.element)?[$m(Lm(Dm),og)]:[]),(()=>B.none()));const ag=()=>{const e=Rl();return ua({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var ig=Object.freeze({__proto__:null,flatgrid:ag,init:e=>e.state(e)});const lg=e=>(t,o,n,s)=>{const r=e(t.element);return mg(r,t,o,n,s)},cg=(e,t)=>{const o=Ql(e,t);return lg(o)},dg=(e,t)=>{const o=Ql(t,e);return lg(o)},ug=e=>(t,o,n,s)=>mg(e,t,o,n,s),mg=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),gg=ug,pg=ug,hg=ug,fg=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),bg=(e,t,o)=>{const n=Mc(e,o);return((e,o)=>$(e,(e=>je(e,t))).map((t=>({index:t,candidates:e}))))(W(n,fg))},vg=(e,t)=>$(e,(e=>je(t,e))),yg=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?B.some(e[n]):B.none()})),xg=(e,t,o,n,s)=>yg(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=Di(r,s,0,a-1);return B.some({row:t,column:i})})),wg=(e,t,o,n,s)=>yg(e,t,n,((t,r)=>{const a=Di(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=Fi(r,0,i-1);return B.some({row:a,column:l})})),Sg=[Un("selector"),rs("execute",tg),fi("onEscape"),rs("captureTab",!1),wi()],kg=(e,t,o)=>{Za(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},Cg=e=>(t,o,n,s)=>bg(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),Og=(e,t,o)=>o.captureTab?B.some(!0):B.none(),_g=Cg(((e,t,o,n)=>xg(e,t,o,n,-1))),Tg=Cg(((e,t,o,n)=>xg(e,t,o,n,1))),Eg=Cg(((e,t,o,n)=>wg(e,t,o,n,-1))),Bg=Cg(((e,t,o,n)=>wg(e,t,o,n,1))),Mg=y([$m(Lm(Fm),cg(_g,Tg)),$m(Lm(Vm),dg(_g,Tg)),$m(Lm(Im),gg(Eg)),$m(Lm(Rm),pg(Bg)),$m(Wm([Um,Lm(Bm)]),Og),$m(Wm([Gm,Lm(Bm)]),Og),$m(Lm(Am),((e,t,o)=>o.onEscape(e,t))),$m(Lm(Dm.concat(Mm)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>Qa(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),Ag=y([$m(Lm(Dm),og)]);var Dg=Jm(Sg,ag,Mg,Ag,(()=>B.some(kg)));const Fg=(e,t,o,n)=>{const s=(e,t,o)=>{const r=Di(t,n,0,o.length-1);return r===e?B.none():(a=o[r],"button"===Ve(a)&&"disabled"===ft(a,"disabled")?s(e,r,o):B.from(o[r]));var a};return bg(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return s(t,t,o)}))},Ig=[Un("selector"),rs("getInitial",B.none),rs("execute",tg),fi("onEscape"),rs("executeOnMove",!1),rs("allowVertical",!0)],Vg=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>Qa(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),Rg=(e,t,o)=>{t.getInitial(e).orThunk((()=>Za(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},zg=(e,t,o)=>Fg(e,o.selector,t,-1),Hg=(e,t,o)=>Fg(e,o.selector,t,1),Pg=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?Vg(t,o,n):B.some(!0))),Ng=(e,t,o)=>o.onEscape(e,t),Lg=y([$m(Lm(Dm),og)]);var Wg=Jm(Ig,da.init,((e,t,o,n)=>{const s=Fm.concat(o.allowVertical?Im:[]),r=Vm.concat(o.allowVertical?Rm:[]);return[$m(Lm(s),Pg(cg(zg,Hg))),$m(Lm(r),Pg(dg(zg,Hg))),$m(Lm(Mm),Vg),$m(Lm(Dm),Vg),$m(Lm(Am),Ng)]}),Lg,(()=>B.some(Rg)));const Ug=(e,t,o)=>B.from(e[t]).bind((e=>B.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),jg=(e,t,o,n)=>{const s=e[t].length,r=Di(o,n,0,s-1);return Ug(e,t,r)},Gg=(e,t,o,n)=>{const s=Di(o,n,0,e.length-1),r=e[s].length,a=Fi(t,0,r-1);return Ug(e,s,a)},$g=(e,t,o,n)=>{const s=e[t].length,r=Fi(o+n,0,s-1);return Ug(e,t,r)},qg=(e,t,o,n)=>{const s=Fi(o+n,0,e.length-1),r=e[s].length,a=Fi(t,0,r-1);return Ug(e,s,a)},Xg=[Kn("selectors",[Un("row"),Un("cell")]),rs("cycles",!0),rs("previousSelector",B.none),rs("execute",tg)],Kg=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return Za(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},Yg=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return Qa(n,s.selectors.row).bind((e=>{const t=Mc(e,s.selectors.cell);return vg(t,n).bind((t=>{const n=Mc(o,s.selectors.row);return vg(n,e).bind((e=>{const o=((e,t)=>P(e,(e=>Mc(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},Jg=Yg(((e,t,o)=>jg(e,t,o,-1)),((e,t,o)=>$g(e,t,o,-1))),Zg=Yg(((e,t,o)=>jg(e,t,o,1)),((e,t,o)=>$g(e,t,o,1))),Qg=Yg(((e,t,o)=>Gg(e,o,t,-1)),((e,t,o)=>qg(e,o,t,-1))),ep=Yg(((e,t,o)=>Gg(e,o,t,1)),((e,t,o)=>qg(e,o,t,1))),tp=y([$m(Lm(Fm),cg(Jg,Zg)),$m(Lm(Vm),dg(Jg,Zg)),$m(Lm(Im),gg(Qg)),$m(Lm(Rm),pg(ep)),$m(Lm(Dm.concat(Mm)),((e,t,o)=>vl(e.element).bind((n=>o.execute(e,t,n)))))]),op=y([$m(Lm(Dm),og)]);var np=Jm(Xg,da.init,tp,op,(()=>B.some(Kg)));const sp=[Un("selector"),rs("execute",tg),rs("moveOnTab",!1)],rp=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),ap=(e,t,o)=>{Za(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},ip=(e,t,o)=>Fg(e,o.selector,t,-1),lp=(e,t,o)=>Fg(e,o.selector,t,1),cp=y([$m(Lm(Im),hg(ip)),$m(Lm(Rm),hg(lp)),$m(Wm([Um,Lm(Bm)]),((e,t,o,n)=>o.moveOnTab?hg(ip)(e,t,o,n):B.none())),$m(Wm([Gm,Lm(Bm)]),((e,t,o,n)=>o.moveOnTab?hg(lp)(e,t,o,n):B.none())),$m(Lm(Mm),rp),$m(Lm(Dm),rp)]),dp=y([$m(Lm(Dm),og)]);var up=Jm(sp,da.init,cp,dp,(()=>B.some(ap)));const mp=[fi("onSpace"),fi("onEnter"),fi("onShiftEnter"),fi("onLeft"),fi("onRight"),fi("onTab"),fi("onShiftTab"),fi("onUp"),fi("onDown"),fi("onEscape"),rs("stopSpaceKeyup",!1),Zn("focusIn")];var gp=Jm(mp,da.init,((e,t,o)=>[$m(Lm(Dm),o.onSpace),$m(Wm([Gm,Lm(Mm)]),o.onEnter),$m(Wm([Um,Lm(Mm)]),o.onShiftEnter),$m(Wm([Um,Lm(Bm)]),o.onShiftTab),$m(Wm([Gm,Lm(Bm)]),o.onTab),$m(Lm(Im),o.onUp),$m(Lm(Rm),o.onDown),$m(Lm(Fm),o.onLeft),$m(Lm(Vm),o.onRight),$m(Lm(Dm),o.onSpace),$m(Lm(Am),o.onEscape)]),((e,t,o)=>o.stopSpaceKeyup?[$m(Lm(Dm),og)]:[]),(e=>e.focusIn));const pp=Qm.schema(),hp=eg.schema(),fp=Wg.schema(),bp=Dg.schema(),vp=np.schema(),yp=rg.schema(),xp=up.schema(),wp=gp.schema(),Sp=ul({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:pp,cyclic:hp,flow:fp,flatgrid:bp,matrix:vp,execution:yp,menu:xp,special:wp}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ve(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:ig}),kp=(e,t)=>{yl((()=>{((e,t,o)=>{const n=e.components();(e=>{N(e.components(),(e=>Ao(e.element))),Mo(e.element),e.syncComponents()})(e);const s=o(t),r=J(n,s);N(r,(t=>{id(t),e.getSystem().removeFromWorld(t)})),N(s,(t=>{ad(t)?ud(e,t):(e.getSystem().addToWorld(t),ud(e,t),dt(e.element)&&ld(t))})),e.syncComponents()})(e,t,(()=>P(t,e.getSystem().build)))}),e.element)},Cp=(e,t)=>{yl((()=>{((o,n,s)=>{const r=o.components(),a=X(n,(e=>la(e).toArray()));N(r,(e=>{V(a,e)||dd(e)}));const i=((e,t,o)=>Fa(e,t,((t,n)=>Ia(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),l=J(r,i);N(l,(e=>{ad(e)&&dd(e)})),N(i,(e=>{ad(e)||cd(o,e)})),o.syncComponents()})(e,t)}),e.element)},Op=(e,t,o,n)=>{dd(t);const s=Ia(e.element,o,n,e.getSystem().buildOrPatch);cd(e,s),e.syncComponents()},_p=(e,t,o)=>{const n=e.getSystem().build(o);gd(e,n,t)},Tp=(e,t,o,n)=>{hd(t),_p(e,((e,t)=>((e,t,o)=>{et(e,o).fold((()=>{Eo(e,t)}),(e=>{Oo(e,t)}))})(e,t,o)),n)},Ep=(e,t)=>e.components(),Bp=(e,t,o,n,s)=>{const r=Ep(e);return B.from(r[n]).map((o=>(s.fold((()=>hd(o)),(s=>{(t.reuseDom?Op:Tp)(e,o,n,s)})),o)))};var Mp=Object.freeze({__proto__:null,append:(e,t,o,n)=>{_p(e,Eo,n)},prepend:(e,t,o,n)=>{_p(e,To,n)},remove:(e,t,o,n)=>{const s=Ep(e),r=G(s,(e=>je(n.element,e.element)));r.each(hd)},replaceAt:Bp,replaceBy:(e,t,o,n,s)=>{const r=Ep(e);return $(r,n).bind((o=>Bp(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?Cp:kp)(e,n),contents:Ep});const Ap=cl({fields:[ds("reuseDom",!0)],name:"replacing",apis:Mp}),Dp=(e,t)=>{const o=((e,t)=>{const o=Cr(t);return cl({fields:[Un("enabled")],name:e,active:{events:y(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:y({}),initialConfig:{},state:da}}},Fp=(e,t)=>{t.ignore||(hl(e.element),t.onFocus(e))};var Ip=Object.freeze({__proto__:null,focus:Fp,blur:(e,t)=>{t.ignore||(e=>{e.dom.blur()})(e.element)},isFocused:e=>fl(e.element)}),Vp=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return ga(o)},events:e=>Cr([Tr(qs(),((t,o)=>{Fp(t,e),o.stop()}))].concat(e.stopMousedown?[Tr(Bs(),((e,t)=>{t.event.prevent()}))]:[]))}),Rp=[hi("onFocus"),rs("stopMousedown",!1),rs("ignore",!1)];const zp=cl({fields:Rp,name:"focusing",active:Vp,apis:Ip}),Hp=(e,t,o)=>{const n=t.aria;n.update(e,n,o.get())},Pp=(e,t,o)=>{t.toggleClass.each((t=>{o.get()?Oa(e.element,t):_a(e.element,t)}))},Np=(e,t,o)=>{Up(e,t,o,!o.get())},Lp=(e,t,o)=>{o.set(!0),Pp(e,t,o),Hp(e,t,o)},Wp=(e,t,o)=>{o.set(!1),Pp(e,t,o),Hp(e,t,o)},Up=(e,t,o,n)=>{(n?Lp:Wp)(e,t,o)},jp=(e,t,o)=>{Up(e,t,o,t.selected)};var Gp=Object.freeze({__proto__:null,onLoad:jp,toggle:Np,isOn:(e,t,o)=>o.get(),on:Lp,off:Wp,set:Up}),$p=Object.freeze({__proto__:null,exhibit:()=>ga({}),events:(e,t)=>{const o=(n=e,s=t,r=Np,Hr((e=>{r(e,n,s)})));var n,s,r;const a=sl(e,t,jp);return Cr(q([e.toggleOnExecute?[o]:[],[a]]))}});const qp=(e,t,o)=>{pt(e.element,"aria-expanded",o)};var Xp=[rs("selected",!1),Zn("toggleClass"),rs("toggleOnExecute",!0),as("aria",{mode:"none"},Pn("mode",{pressed:[rs("syncWithExpanded",!1),yi("update",((e,t,o)=>{pt(e.element,"aria-pressed",o),t.syncWithExpanded&&qp(e,0,o)}))],checked:[yi("update",((e,t,o)=>{pt(e.element,"aria-checked",o)}))],expanded:[yi("update",qp)],selected:[yi("update",((e,t,o)=>{pt(e.element,"aria-selected",o)}))],none:[yi("update",b)]}))];const Kp=cl({fields:Xp,name:"toggling",active:$p,apis:Gp,state:(!1,{init:()=>{const e=ps(false);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(false),readState:()=>e.get()}}})});const Yp=()=>{const e=(e,t)=>{t.stop(),xr(e)};return[Tr(Ns(),e),Tr(Qs(),e),Dr(Os()),Dr(Bs())]},Jp=e=>Cr(q([e.map((e=>Hr(((t,o)=>{e(t),o.stop()})))).toArray(),Yp()])),Zp="alloy.item-hover",Qp="alloy.item-focus",eh=e=>{(vl(e.element).isNone()||zp.isFocused(e))&&(zp.isFocused(e)||zp.focus(e),yr(e,Zp,{item:e}))},th=e=>{yr(e,Qp,{item:e})},oh=y(Zp),nh=y(Qp),sh=[Un("data"),Un("components"),Un("dom"),rs("hasSubmenu",!1),Zn("toggling"),eu("itemBehaviours",[Kp,zp,Sp,Yd]),rs("ignoreFocus",!1),rs("domModification",{}),yi("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:e.toggling.isSome()?"menuitemcheckbox":"menuitem",...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:tu(e.itemBehaviours,[e.toggling.fold(Kp.revoke,(e=>Kp.config({aria:{mode:"checked"},...e}))),zp.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{th(e)}}),Sp.config({mode:"execution"}),Yd.config({store:{mode:"memory",initialValue:e.data}}),Dp("item-type-events",[...Yp(),Tr(Fs(),eh),Tr(Zs(),zp.focus)])]),components:e.components,eventOrder:e.eventOrder}))),rs("eventOrder",{})],rh=[Un("dom"),Un("components"),yi("builder",(e=>({dom:e.dom,components:e.components,events:Cr([Fr(Zs())])})))],ah=y("item-widget"),ih=y([Cu({name:"widget",overrides:e=>({behaviours:il([Yd.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),lh=[Un("uid"),Un("data"),Un("components"),Un("dom"),rs("autofocus",!1),rs("ignoreFocus",!1),eu("widgetBehaviours",[Yd,zp,Sp]),rs("domModification",{}),Gu(ih()),yi("builder",(e=>{const t=Ru(ah(),e,ih()),o=zu(ah(),e,t.internals()),n=t=>Hu(t,e,"widget").map((e=>(Sp.focusIn(e),e))),s=(t,o)=>em(o.event.target)?B.none():e.autofocus?(o.setSource(t.element),B.none()):B.none();return{dom:e.dom,components:o,domModification:e.domModification,events:Cr([Hr(((e,t)=>{n(e).each((e=>{t.stop()}))})),Tr(Fs(),eh),Tr(Zs(),((t,o)=>{e.autofocus?n(t):zp.focus(t)}))]),behaviours:tu(e.widgetBehaviours,[Yd.config({store:{mode:"memory",initialValue:e.data}}),zp.config({ignore:e.ignoreFocus,onFocus:e=>{th(e)}}),Sp.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:ml(),onLeft:s,onRight:s,onEscape:(t,o)=>zp.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),B.none()):B.none():(zp.focus(t),B.some(!0))})])}}))],ch=Pn("type",{widget:lh,item:sh,separator:rh}),dh=y([Tu({factory:{sketch:e=>{const t=zn("menu.spec item",ch,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>be(t,"uid")?t:{...t,uid:ea("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),uh=y([Un("value"),Un("items"),Un("dom"),Un("components"),rs("eventOrder",{}),Jd("menuBehaviours",[Tm,Yd,nm,Sp]),as("movement",{mode:"menu",moveOnTab:!0},Pn("mode",{grid:[wi(),yi("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[yi("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},focusManager:e.focusManager}))),Un("rowSelector")],menu:[rs("moveOnTab",!0),yi("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),jn("markers",di()),rs("fakeFocus",!1),rs("focusManager",Xm()),hi("onHighlight")]),mh=y("alloy.menu-focus"),gh=Qu({name:"Menu",configFields:uh(),partFields:dh(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Qd(e.menuBehaviours,[Tm.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight}),Yd.config({store:{mode:"memory",initialValue:e.value}}),nm.config({find:B.some}),Sp.config(e.movement.config(e,e.movement))]),events:Cr([Tr(nh(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Tm.highlight(e,o),t.stop(),yr(e,mh(),{menu:e,item:o})}))})),Tr(oh(),((e,t)=>{const o=t.event.item;Tm.highlight(e,o)}))]),components:t,eventOrder:e.eventOrder,domModification:{attributes:{role:"menu"}}})}),ph=(e,t,o,n)=>fe(o,n).bind((n=>fe(e,n).bind((n=>{const s=ph(e,t,o,n);return B.some([n].concat(s))})))).getOr([]),hh=e=>"prepared"===e.type?B.some(e.menu):B.none(),fh=()=>{const e=ps({}),t=ps({}),o=ps({}),n=Rl(),s=ps({}),r=e=>a(e).bind(hh),a=e=>fe(t.get(),e),i=t=>fe(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};le(e,((e,t)=>{N(e,(e=>{o[e]=t}))}));const n=t,s=de(t,((e,t)=>({k:e,v:t}))),r=ce(s,((e,t)=>[t].concat(ph(o,n,s,t))));return ce(o,(e=>fe(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>fe(e.get(),t).map((e=>{const n=fe(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>fe(o.get(),e),collapse:e=>fe(o.get(),e).bind((e=>e.length>1?B.some(e.slice(1)):B.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return J(ae(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=W(i(t).toArray(),(e=>r(e).isSome()));return fe(o.get(),t).bind((t=>{const o=Y(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return B.none();t.push(n.getOrDie())}return B.some(t)})(X(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>pe(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>ye(n.get(),t)?[]:[B.none()]),(e=>[B.some(e)])))))}))}}},bh=hh,vh=y("collapse-item"),yh=Zu({name:"TieredMenu",configFields:[vi("onExecute"),vi("onEscape"),bi("onOpenMenu"),bi("onOpenSubmenu"),hi("onRepositionMenu"),hi("onCollapseMenu"),rs("highlightImmediately",!0),Kn("data",[Un("primary"),Un("menus"),Un("expansions")]),rs("fakeFocus",!1),hi("onHighlight"),hi("onHover"),mi(),Un("dom"),rs("navigateOnHover",!0),rs("stayInDom",!1),Jd("tmenuBehaviours",[Sp,Tm,nm,Ap]),rs("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=Rl(),n=fh(),s=e=>Yd.getValue(e).value,r=t=>ce(e.data.menus,((e,t)=>X(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=(e,t)=>{Tm.highlight(e,t),Tm.getHighlighted(t).orThunk((()=>Tm.getFirst(t))).each((t=>{wr(e,t.element,Zs())}))},i=(e,t)=>xe(P(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?B.some(e.menu):B.none()))))),l=(t,o,n)=>{const s=i(o,o.otherMenus(n));N(s,(o=>{Ba(o.element,[e.markers.backgroundMenu]),e.stayInDom||Ap.remove(t,o)}))},c=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=Mc(t.element,`.${e.markers.item}`),a=W(r,(e=>"true"===ft(e,"aria-haspopup")));return N(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);le(r,((e,t)=>{const o=V(n,t);pt(e.element,"aria-expanded",o)}))},d=(t,o,n)=>B.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return B.none();{const r=s.menu,c=i(o,n.slice(1));return N(c,(t=>{Oa(t.element,e.markers.backgroundMenu)})),dt(r.element)||Ap.append(t,ja(r)),Ba(r.element,[e.markers.backgroundMenu]),a(t,r),l(t,o,n),B.some(r)}}))));let u;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(u||(u={}));const m=(t,o,r=u.HighlightSubmenu)=>{if(o.hasConfigured(bm)&&bm.isDisabled(o))return B.some(o);{const a=s(o);return n.expand(a).bind((s=>(c(t,s),B.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return dt(l.element)||Ap.append(t,ja(l)),e.onOpenSubmenu(t,o,l,Y(s)),r===u.HighlightSubmenu?(Tm.highlightFirst(l),d(t,n,s)):(Tm.dehighlightAll(l),B.some(o))})))))))}},g=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(c(t,s),d(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},p=t=>(o,n)=>Qa(n.getSource(),"."+e.markers.item).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(T))))),h=Cr([Tr(mh(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Tm.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>l(e,n,t)))}))})),Hr(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&g(t,o),m(t,o,u.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),Vr(((t,o)=>{(t=>{const o=((t,o,n)=>ce(n,((n,s)=>{const r=()=>gh.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:e.onHighlight,focusManager:e.fakeFocus?Km():Xm()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{Ap.append(t,ja(o)),e.onOpenMenu(t,o),e.highlightImmediately&&a(t,o)}))}))].concat(e.navigateOnHover?[Tr(oh(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(c(e,t),d(e,n,t))))})(t,r),m(t,r,u.HighlightParent),e.onHover(t,r)}))]:[])),f=e=>Tm.getHighlighted(e).bind(Tm.getHighlighted),v={collapseMenu:e=>{f(e).each((t=>{g(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{a(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>f(t).bind((e=>{const t=s(e),o=he(n.getMenus()),r=xe(P(o,bh));return n.getTriggeringPath(t,(e=>((e,t,o)=>re(t,(e=>{if(!e.getSystem().isConnected())return B.none();const t=Tm.getCandidates(e);return G(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>B.from(e.components()[0]).filter((e=>"menu"===ft(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Qd(e.tmenuBehaviours,[Sp.config({mode:"special",onRight:p(((e,t)=>em(t.element)?B.none():m(e,t,u.HighlightSubmenu))),onLeft:p(((e,t)=>em(t.element)?B.none():g(e,t))),onEscape:p(((t,o)=>g(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{wr(e,t.element,Zs())}))}}),Tm.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),nm.config({find:e=>Tm.getHighlighted(e)}),Ap.config({})]),eventOrder:e.eventOrder,apis:v,events:h}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:fs(e,t),expansions:{}}),collapseItem:e=>({value:qr(vh()),meta:{text:e}})}}),xh=Zu({name:"InlineView",configFields:[Un("lazySink"),hi("onShow"),hi("onHide"),os("onEscape"),Jd("inlineBehaviours",[Md,Yd,gl]),ss("fireDismissalEventInstead",[rs("event",dr())]),ss("fireRepositionEventInstead",[rs("event",ur())]),rs("getRelated",B.none),rs("isExtraPart",_),rs("eventOrder",B.none)],factory:(e,t)=>{const o=(e,t,o,s)=>{n(e,t,o,(()=>s.map((e=>Lo(e)))))},n=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();Md.openWhileCloaked(t,o,(()=>rd.positionWithinBounds(r,t,n,s()))),Yd.setValue(t,B.some({mode:"position",config:n,getBounds:s}))},s=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>el(),onRtl:()=>tl()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return yh.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightImmediately:n.menu.highlightImmediately,onEscape:()=>(Md.close(t),e.onEscape.map((e=>e(t))),B.some(!0)),onExecute:()=>B.some(!0),onOpenMenu:(e,t)=>{rd.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();rd.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();rd.positionWithinBounds(a,t,o,s()),N(n,(e=>{const t=i(e.triggeringPath);rd.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);Md.open(t,r),Yd.setValue(t,B.some({mode:"menu",menu:r}))},r=t=>{Md.isOpen(t)&&Yd.getValue(t).each((o=>{switch(o.mode){case"menu":Md.getState(t).each(yh.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();rd.positionWithinBounds(n,t,o.config,o.getBounds())}}))},a={setContent:(e,t)=>{Md.setContent(e,t)},showAt:(e,t,n)=>{o(e,t,n,B.none())},showWithin:o,showWithinBounds:n,showMenuAt:(e,t,o)=>{s(e,t,o,B.none)},showMenuWithinBounds:s,hide:e=>{Md.isOpen(e)&&(Yd.setValue(e,B.none()),Md.close(e))},getContent:e=>Md.getState(e),reposition:r,isOpen:Md.isOpen};return{uid:e.uid,dom:e.dom,behaviours:Qd(e.inlineBehaviours,[Md.config({isPartOf:(t,o,n)=>oi(o,n)||((t,o)=>e.getRelated(t).exists((e=>oi(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),Yd.config({store:{mode:"memory",initialValue:B.none()}}),gl.config({channels:{...Vd({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...zd({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:r})}})]),eventOrder:e.eventOrder,apis:a}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithin:(e,t,o,n,s)=>{e.showWithin(t,o,n,s)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}});var wh=tinymce.util.Tools.resolve("tinymce.util.Delay");const Sh=Zu({name:"Button",factory:e=>{const t=Jp(e.action),o=e.dom.tag,n=t=>fe(e.dom,"attributes").bind((e=>fe(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:tu(e.buttonBehaviours,[zp.config({}),Sp.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:n("role").getOr("button")}},eventOrder:e.eventOrder}},configFields:[rs("uid",void 0),Un("dom"),rs("components",[]),eu("buttonBehaviours",[zp,Sp]),Zn("action"),Zn("role"),rs("eventOrder",{})]}),kh=e=>{const t=(e=>void 0!==e.uid)(e)&&ve(e,"uid")?e.uid:ea("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}};var Ch=tinymce.util.Tools.resolve("tinymce.util.I18n");const Oh={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},_h="temporary-placeholder",Th=e=>()=>fe(e,_h).getOr("!not found!"),Eh=(e,t)=>{const o=e.toLowerCase();if(Ch.isRtl()){const e=((e,t)=>Oe(e,t)?e:((e,t)=>e+"-rtl")(e))(o,"-rtl");return be(t,e)?e:o}return o},Bh=(e,t)=>fe(t,Eh(e,t)),Mh=(e,t)=>{const o=t();return Bh(e,o).getOrThunk(Th(o))},Ah=()=>Dp("add-focusable",[Vr((e=>{Ja(e.element,"svg").each((e=>pt(e,"focusable","false")))}))]),Dh=(e,t,o,n)=>{var s,r;const a=(e=>!!Ch.isRtl()&&be(Oh,e))(t)?["tox-icon--flip"]:[],i=fe(o,Eh(t,o)).or(n).getOrThunk(Th(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(a),innerHtml:i},behaviours:il([...null!==(r=e.behaviours)&&void 0!==r?r:[],Ah()])}},Fh=(e,t,o,n=B.none())=>Dh(t,e,o(),n),Ih={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},Vh=Zu({name:"Notification",factory:e=>{const t=kh({dom:{tag:"p",innerHtml:e.translationProvider(e.text)},behaviours:il([Ap.config({})])}),o=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),n=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),s=kh({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(0)]},n(0)],behaviours:il([Ap.config({})])}),r={updateProgress:(e,t)=>{e.getSystem().isConnected()&&s.getOpt(e).each((e=>{Ap.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(t)]},n(t)])}))},updateText:(e,o)=>{if(e.getSystem().isConnected()){const n=t.get(e);Ap.set(n,[Pa(o)])}}},a=q([e.icon.toArray(),e.level.toArray(),e.level.bind((e=>B.from(Ih[e]))).toArray()]),i=kh(Sh.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[Fh("close",{tag:"div",classes:["tox-icon"],attributes:{"aria-label":e.translationProvider("Close")}},e.iconProvider)],action:t=>{e.onAction(t)}})),l=((e,t,o)=>{const n=o(),s=G(e,(e=>be(n,Eh(e,n))));return Dh({tag:"div",classes:["tox-notification__icon"]},s.getOr(_h),n,B.none())})(a,0,e.iconProvider),c=[l,{dom:{tag:"div",classes:["tox-notification__body"]},components:[t.asSpec()],behaviours:il([Ap.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert"},classes:e.level.map((e=>["tox-notification","tox-notification--in",`tox-notification--${e}`])).getOr(["tox-notification","tox-notification--in"])},behaviours:il([zp.config({}),Dp("notification-events",[Tr(Is(),(e=>{i.getOpt(e).each(zp.focus)}))])]),components:c.concat(e.progress?[s.asSpec()]:[]).concat(e.closeButton?[i.asSpec()]:[]),apis:r}},configFields:[Zn("level"),Un("progress"),Un("icon"),Un("onAction"),Un("text"),Un("iconProvider"),Un("translationProvider"),ds("closeButton",!0)],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var Rh,zh,Hh=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Ph=tinymce.util.Tools.resolve("tinymce.EditorManager"),Nh=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(Rh||(Rh={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(zh||(zh={}));const Lh=e=>t=>t.options.get(e),Wh=e=>t=>B.from(e(t)),Uh=e=>{const t=Nh.deviceType.isPhone(),o=Nh.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:Hh.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),z(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:zh.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!Nh.deviceType.isTouch()})},jh=Lh("readonly"),Gh=Lh("height"),$h=Lh("width"),qh=Wh(Lh("min_width")),Xh=Wh(Lh("min_height")),Kh=Wh(Lh("max_width")),Yh=Wh(Lh("max_height")),Jh=Wh(Lh("style_formats")),Zh=Lh("style_formats_merge"),Qh=Lh("style_formats_autohide"),ef=Lh("content_langs"),tf=Lh("removed_menuitems"),of=Lh("toolbar_mode"),nf=Lh("toolbar_groups"),sf=Lh("toolbar_location"),rf=Lh("fixed_toolbar_container"),af=Lh("fixed_toolbar_container_target"),lf=Lh("toolbar_persist"),cf=Lh("toolbar_sticky_offset"),df=Lh("menubar"),uf=Lh("toolbar"),mf=Lh("file_picker_callback"),gf=Lh("file_picker_validator_handler"),pf=Lh("file_picker_types"),hf=Lh("typeahead_urls"),ff=Lh("anchor_top"),bf=Lh("anchor_bottom"),vf=Lh("draggable_modal"),yf=Lh("statusbar"),xf=Lh("elementpath"),wf=Lh("branding"),Sf=Lh("resize"),kf=Lh("paste_as_text"),Cf=e=>!1===e.options.get("skin"),Of=e=>!1!==e.options.get("menubar"),_f=e=>{const t=e.options.get("skin_url");if(Cf(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return Ph.baseURL+"/skins/ui/"+t}},Tf=e=>e.options.get("line_height_formats").split(" "),Ef=e=>{const t=uf(e),o=r(t),n=l(t)&&t.length>0;return!Mf(e)&&(n||o||!0===t)},Bf=e=>{const t=z(9,(t=>e.options.get("toolbar"+(t+1)))),o=W(t,r);return Se(o.length>0,o)},Mf=e=>Bf(e).fold((()=>{const t=uf(e);return f(t,r)&&t.length>0}),T),Af=e=>sf(e)===zh.bottom,Df=e=>{if(!e.inline)return B.none();const t=rf(e);if(t.length>0)return Za(ut(),t);const o=af(e);return g(o)?B.some(Fe(o)):B.none()},Ff=e=>e.inline&&Df(e).isSome(),If=e=>Df(e).getOrThunk((()=>it(at(Fe(e.getElement()))))),Vf=e=>e.inline&&!Of(e)&&!Ef(e)&&!Mf(e),Rf=e=>(e.options.get("toolbar_sticky")||e.inline)&&!Ff(e)&&!Vf(e),zf=e=>{const t=e.options.get("menu");return ce(t,(e=>({...e,items:e.items})))};var Hf=Object.freeze({__proto__:null,get ToolbarMode(){return Rh},get ToolbarLocation(){return zh},register:Uh,getSkinUrl:_f,isReadOnly:jh,isSkinDisabled:Cf,getHeightOption:Gh,getWidthOption:$h,getMinWidthOption:qh,getMinHeightOption:Xh,getMaxWidthOption:Kh,getMaxHeightOption:Yh,getUserStyleFormats:Jh,shouldMergeStyleFormats:Zh,shouldAutoHideStyleFormats:Qh,getLineHeightFormats:Tf,getContentLanguages:ef,getRemovedMenuItems:tf,isMenubarEnabled:Of,isMultipleToolbars:Mf,isToolbarEnabled:Ef,isToolbarPersist:lf,getMultipleToolbarsOption:Bf,getUiContainer:If,useFixedContainer:Ff,getToolbarMode:of,isDraggableModal:vf,isDistractionFree:Vf,isStickyToolbar:Rf,getStickyToolbarOffset:cf,getToolbarLocation:sf,isToolbarLocationBottom:Af,getToolbarGroups:nf,getMenus:zf,getMenubar:df,getToolbar:uf,getFilePickerCallback:mf,getFilePickerTypes:pf,useTypeaheadUrls:hf,getAnchorTop:ff,getAnchorBottom:bf,getFilePickerValidatorHandler:gf,useStatusBar:yf,useElementPath:xf,useBranding:wf,getResize:Sf,getPasteAsText:kf});const Pf="[data-mce-autocompleter]",Nf=e=>Qa(e,Pf);var Lf;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(Lf||(Lf={}));var Wf=Lf;const Uf="tox-menu-nav__js",jf="tox-collection__item",Gf={normal:Uf,color:"tox-swatch"},$f="tox-collection__item--enabled",qf="tox-collection__item-icon",Xf="tox-collection__item-label",Kf="tox-collection__item-caret",Yf="tox-collection__item--active",Jf="tox-collection__item-container",Zf="tox-collection__item-container--row",Qf=e=>fe(Gf,e).getOr(Uf),eb=e=>"color"===e?"tox-swatches":"tox-menu",tb=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:eb(e),tieredMenu:"tox-tiered-menu"}),ob=e=>{const t=tb(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:Qf(e)}},nb=(e,t,o)=>{const n=tb(o);return{tag:"div",classes:q([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},sb=[gh.parts.items({})],rb=(e,t,o)=>{const n=tb(o);return{dom:{tag:"div",classes:q([[n.tieredMenu]])},markers:ob(o)}},ab=(e,t)=>o=>{const n=H(o,t);return P(n,(t=>({dom:e,components:t})))},ib=(e,t)=>{const o=[];let n=[];return N(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(be(e.dom,"innerHtml")||e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),P(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},lb=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[gh.parts.items({preprocess:o=>"auto"!==e&&e>1?ab({tag:"div",classes:["tox-collection__group"]},e)(o):ib(o,((e,o)=>"separator"===t[o].type))})]}),cb=e=>R(e,(e=>"icon"in e&&void 0!==e.icon)),db=e=>(console.error(Hn(e)),console.log(e),B.none()),ub=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[gh.parts.items({preprocess:e=>ib(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},mb=(e,t,o,n,s)=>{if("color"===s){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[gh.parts.items({preprocess:"auto"!==e?ab({tag:"div",classes:["tox-swatches__row"]},e):x})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s&&"auto"===n){const t=lb(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s&&1===n){const t=lb(1,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s){const t=lb(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[gh.parts.items({preprocess:ab({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:nb(t,n,s),components:sb,items:o}},gb=$n("type"),pb=$n("name"),hb=$n("label"),fb=$n("text"),bb=$n("title"),vb=$n("icon"),yb=$n("value"),xb=Xn("fetch"),wb=Xn("getSubmenuItems"),Sb=Xn("onAction"),kb=Xn("onItemAction"),Cb=us("onSetup",(()=>b)),Ob=ts("name"),_b=ts("text"),Tb=ts("icon"),Eb=ts("tooltip"),Bb=ts("label"),Mb=ts("shortcut"),Ab=os("select"),Db=ds("active",!1),Fb=ds("borderless",!1),Ib=ds("enabled",!0),Vb=ds("primary",!1),Rb=e=>rs("columns",e),zb=rs("meta",{}),Hb=us("onAction",b),Pb=e=>ls("type",e),Nb=e=>Nn("name","name",rn((()=>qr(`${e}-name`))),Tn),Lb=yn([gb,_b]),Wb=yn([Pb("autocompleteitem"),Db,Ib,zb,yb,_b,Tb]),Ub=[Ib,Eb,Tb,_b,Cb],jb=yn([gb,Sb].concat(Ub)),Gb=e=>Vn("toolbarbutton",jb,e),$b=[Db].concat(Ub),qb=yn($b.concat([gb,Sb])),Xb=e=>Vn("ToggleButton",qb,e),Kb=[us("predicate",_),cs("scope","node",["node","editor"]),cs("position","selection",["node","selection","line"])],Yb=Ub.concat([Pb("contextformbutton"),Vb,Sb,Ln("original",x)]),Jb=$b.concat([Pb("contextformbutton"),Vb,Sb,Ln("original",x)]),Zb=Ub.concat([Pb("contextformbutton")]),Qb=$b.concat([Pb("contextformtogglebutton")]),ev=Pn("type",{contextformbutton:Yb,contextformtogglebutton:Jb}),tv=yn([Pb("contextform"),us("initValue",y("")),Bb,Jn("commands",ev),Qn("launch",Pn("type",{contextformbutton:Zb,contextformtogglebutton:Qb}))].concat(Kb)),ov=yn([Pb("contexttoolbar"),$n("items")].concat(Kb)),nv=[gb,$n("src"),ts("alt"),ms("classes",[],Tn)],sv=yn(nv),rv=[gb,fb,Ob,ms("classes",["tox-collection__item-label"],Tn)],av=yn(rv),iv=fn((()=>Dn("type",{cardimage:sv,cardtext:av,cardcontainer:lv}))),lv=yn([gb,ls("direction","horizontal"),ls("align","left"),ls("valign","middle"),Jn("items",iv)]),cv=[Ib,_b,Mb,("menuitem",Nn("value","value",rn((()=>qr("menuitem-value"))),Cn())),zb];const dv=yn([gb,Bb,Jn("items",iv),Cb,Hb].concat(cv)),uv=yn([gb,Db,Tb].concat(cv)),mv=[gb,$n("fancytype"),Hb],gv=[rs("initData",{})].concat(mv),pv=[gs("initData",{},[ds("allowCustomColors",!0),ns("colors",Cn())])].concat(mv),hv=Pn("fancytype",{inserttable:gv,colorswatch:pv}),fv=yn([gb,Cb,Hb,Tb].concat(cv)),bv=yn([gb,wb,Cb,Tb].concat(cv)),vv=yn([gb,Tb,Db,Cb,Sb].concat(cv)),yv=(e,t,o)=>{const n=Mc(e.element,"."+o);if(n.length>0){const e=$(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return B.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return B.none()},xv=e=>((e,t)=>il([Dp(e,t)]))(qr("unnamed-events"),e),wv=qr("tooltip.exclusive"),Sv=qr("tooltip.show"),kv=qr("tooltip.hide"),Cv=(e,t,o)=>{e.getSystem().broadcastOn([wv],{})};var Ov=Object.freeze({__proto__:null,hideAllExclusive:Cv,setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&Ap.set(e,n)}))}}),_v=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{hd(n),e.onHide(o,n),t.clearTooltip()})),t.clearTimer()};return Cr(q([[Tr(Sv,(o=>{t.resetTimer((()=>{(o=>{if(!t.isShowing()){Cv(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:Cr("normal"===e.mode?[Tr(Fs(),(e=>{vr(o,Sv)})),Tr(As(),(e=>{vr(o,kv)}))]:[]),behaviours:il([Ap.config({})])});t.setTooltip(s),md(n,s),e.onShow(o,s),rd.position(n,s,{anchor:e.anchor(o)})}})(o)}),e.delay)})),Tr(kv,(n=>{t.resetTimer((()=>{o(n)}),e.delay)})),Tr(Ys(),((e,t)=>{const n=t;n.universal||V(n.channels,wv)&&o(e)})),Rr((e=>{o(e)}))],"normal"===e.mode?[Tr(Is(),(e=>{vr(e,Sv)})),Tr(Xs(),(e=>{vr(e,kv)})),Tr(Fs(),(e=>{vr(e,Sv)})),Tr(As(),(e=>{vr(e,kv)}))]:[Tr(fr(),((e,t)=>{vr(e,Sv)})),Tr(br(),(e=>{vr(e,kv)}))]]))}}),Tv=[Un("lazySink"),Un("tooltipDom"),rs("exclusive",!0),rs("tooltipComponents",[]),rs("delay",300),cs("mode","normal",["normal","follow-highlight"]),rs("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:y([qi,$i,Wi,ji,Ui,Gi]),onRtl:y([qi,$i,Wi,ji,Ui,Gi])}}))),hi("onHide"),hi("onShow")];const Ev=cl({fields:Tv,name:"tooltipping",active:_v,state:Object.freeze({__proto__:null,init:()=>{const e=Rl(),t=Rl(),o=()=>{e.on(clearTimeout)},n=y("not-implemented");return ua({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:o,resetTimer:(t,n)=>{o(),e.set(setTimeout(t,n))},readState:n})}}),apis:Ov}),Bv="silver.readonly",Mv=yn([("readonly",jn("readonly",En))]);const Av=(e,t)=>{const o=e.outerContainer.element;t&&(e.mothership.broadcastOn([Ad()],{target:o}),e.uiMothership.broadcastOn([Ad()],{target:o})),e.mothership.broadcastOn([Bv],{readonly:t}),e.uiMothership.broadcastOn([Bv],{readonly:t})},Dv=(e,t)=>{e.on("init",(()=>{e.mode.isReadOnly()&&Av(t,!0)})),e.on("SwitchMode",(()=>Av(t,e.mode.isReadOnly()))),jh(e)&&e.mode.set("readonly")},Fv=()=>gl.config({channels:{[Bv]:{schema:Mv,onReceive:(e,t)=>{bm.set(e,t.readonly)}}}}),Iv=e=>bm.config({disabled:e}),Vv=e=>bm.config({disabled:e,disableClass:"tox-tbtn--disabled"}),Rv=e=>bm.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),zv=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},Hv=(e,t)=>Vr((o=>{zv(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),Pv=(e,t)=>Rr((o=>zv(e,o)(t.get()))),Nv=(e,t)=>Hr(((o,n)=>{zv(e,o)(e.onAction),e.triggersSubmenu||t!==Wf.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&vr(o,tr()),n.stop())})),Lv={[Js()]:["disabling","alloy.base.behaviour","toggling","item-events"]},Wv=xe,Uv=(e,t,o,n)=>{const s=ps(b);return{type:"item",dom:t.dom,components:Wv(t.optComponents),data:e.data,eventOrder:Lv,hasSubmenu:e.triggersSubmenu,itemBehaviours:il([Dp("item-events",[Nv(e,o),Hv(e,s),Pv(e,s)]),(r=()=>!e.enabled||n.isDisabled(),bm.config({disabled:r,disableClass:"tox-collection__item--state-disabled"})),Fv(),Ap.config({})].concat(e.itemBehaviours))};var r},jv=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),Gv=e=>{const t=Nh.os.isMacOS()||Nh.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=P(n,(e=>{const t=e.toLowerCase().trim();return be(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},$v=(e,t,o=[qf])=>Fh(e,{tag:"div",classes:o},t),qv=e=>({dom:{tag:"div",classes:[Xf]},components:[Pa(Ch.translate(e))]}),Xv=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),Kv=(e,t)=>({dom:{tag:"div",classes:[Xf]},components:[{dom:{tag:e.tag,styles:e.styles},components:[Pa(Ch.translate(t))]}]}),Yv=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[Pa(Gv(e))]}),Jv=e=>$v("checkmark",e,["tox-collection__item-checkmark"]),Zv=e=>{const t=e.map((e=>({attributes:{title:Ch.translate(e)}}))).getOr({});return{tag:"div",classes:[Uf,jf],...t}},Qv=(e,t,o,n=B.none())=>"color"===e.presets?((e,t,o)=>{const n=e.ariaLabel,s=e.value,r=e.iconContent.map((e=>((e,t,o)=>{const n=t();return Bh(e,n).or(o).getOrThunk(Th(n))})(e,t.icons,o)));return{dom:(()=>{const e=r.getOr(""),o=n.map((e=>({title:t.translate(e)}))).getOr({}),a={tag:"div",attributes:o,classes:["tox-swatch"]};return"custom"===s?{...a,tag:"button",classes:[...a.classes,"tox-swatches__picker-btn"],innerHtml:e}:"remove"===s?{...a,classes:[...a.classes,"tox-swatch--remove"],innerHtml:e}:{...a,attributes:{...a.attributes,"data-mce-color":s},styles:{"background-color":s}}})(),optComponents:[]}})(e,t,n):((e,t,o,n)=>{const s={tag:"div",classes:[qf]},r=o?e.iconContent.map((e=>Fh(e,s,t.icons,n))).orThunk((()=>B.some({dom:s}))):B.none(),a=e.checkMark,i=B.from(e.meta).fold((()=>qv),(e=>be(e,"style")?S(Kv,e.style):qv)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>B.some(Xv(e,[Xf]))));return{dom:Zv(e.ariaLabel),optComponents:[r,l,e.shortcutContent.map(Yv),a,e.caret]}})(e,t,o,n),ey=(e,t)=>fe(e,"tooltipWorker").map((e=>[Ev.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:$l}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{Ev.setComponents(t,[Na({element:Fe(e)})])}))}})])).getOr([]),ty=(e,t)=>{const o=(e=>Hh.DOM.encode(e))(Ch.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},oy=(e,t)=>P(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":Zf,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[Jf,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,oy(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>V(t.cardText.highlightOn,e))),n=o?B.from(t.cardText.matchText).getOr(""):"";return Xv(ty(e.text,n),e.classes)}})),ny=Au(ah(),ih()),sy=e=>({value:e}),ry=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,ay=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,iy=e=>ry.test(e)||ay.test(e),ly=e=>{return(t=e,((e,t)=>ke(e,t,0))(t,"#")?((e,t)=>e.substring(t))(t,"#".length):t).toUpperCase();var t},cy=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},dy=e=>{const t=cy(e.red)+cy(e.green)+cy(e.blue);return sy(t)},uy=Math.min,my=Math.max,gy=Math.round,py=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,hy=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,fy=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),by=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},vy=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=my(0,uy(r,1)),a=my(0,uy(a,1)),0===r)return t=o=n=gy(255*a),fy(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=gy(255*(t+d)),o=gy(255*(o+d)),n=gy(255*(n+d)),fy(t,o,n,1)},yy=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(ry,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=ay.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return fy(o,n,s,1)},xy=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return fy(s,r,a,i)},wy=e=>{if("transparent"===e)return B.some(fy(0,0,0,0));const t=py.exec(e);if(null!==t)return B.some(xy(t[1],t[2],t[3],"1"));const o=hy.exec(e);return null!==o?B.some(xy(o[1],o[2],o[3],o[4])):B.none()},Sy=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,ky=fy(255,0,0,1),Cy=(e,t)=>e.dispatch("ResizeContent",t),Oy=(e,t,o)=>({hue:e,saturation:t,value:o}),_y=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,Oy(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,Oy(Math.round(t),Math.round(100*o),Math.round(100*n)))},Ty=e=>dy(vy(e)),Ey=e=>{return(t=e,iy(t)?B.some({value:ly(t)}):B.none()).orThunk((()=>wy(e).map(dy))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return dy(fy(s,r,a,i))}));var t};var By=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const My="tinymce-custom-colors",Ay=((e=10)=>{const t=By.getItem(My),o=r(t)?JSON.parse(t):[],n=e-(s=o).length<0?s.slice(0,e):s;var s;const a=e=>{n.splice(e,1)};return{add:t=>{I(n,t).each(a),n.unshift(t),n.length>e&&n.pop(),By.setItem(My,JSON.stringify(n))},state:()=>n.slice(0)}})(10),Dy=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:"#"+Ey(e[o]).value,type:"choiceitem"});return t},Fy=e=>t=>t.options.get(e),Iy=Fy("color_cols"),Vy=Fy("custom_colors"),Ry=Fy("color_map"),zy=e=>{Ay.add(e)},Hy="#000000",Py=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},Ny=(e,t,o,n)=>{"custom"===o?$y(e)((o=>{o.each((o=>{zy(o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),Hy):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},Ly=(e,t)=>e.concat(P(Ay.state(),(e=>({type:"choiceitem",text:e,value:e}))).concat(Py(t))),Wy=(e,t)=>o=>{o(Ly(e,t))},Uy=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},jy=(e,t,o,n,s)=>{e.ui.registry.addSplitButton(t,{tooltip:n,presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:t=>{const n=((e,t)=>{let o;return e.dom.getParents(e.selection.getStart(),(e=>{let n;(n=e.style["forecolor"===t?"color":"background-color"])&&(o=o||n)})),B.from(o)})(e,o);return n.bind((e=>wy(e).map((e=>{const o=dy(e).value;return Ce(t.toLowerCase(),o)})))).getOr(!1)},columns:Iy(e),fetch:Wy(Ry(e),Vy(e)),onAction:t=>{Ny(e,o,s.get(),b)},onItemAction:(n,r)=>{Ny(e,o,r,(o=>{s.set(o),((e,t)=>{e.dispatch("TextColorChange",t)})(e,{name:t,color:o})}))},onSetup:o=>{Uy(o,t,s.get());const n=e=>{e.name===t&&Uy(o,e.name,e.color)};return e.on("TextColorChange",n),()=>{e.off("TextColorChange",n)}}})},Gy=(e,t,o,n)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",onAction:t=>{Ny(e,o,t.value,b)}}]})},$y=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(B.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(B.none())}})},qy=(e,t,o,n,s,r,a,i)=>{const l=cb(t),c=Xy(t,o,n,"color"!==s?"normal":"color",r,a,i);return mb(e,l,c,n,s)},Xy=(e,t,o,n,s,r,a)=>xe(P(e,(i=>{return"choiceitem"===i.type?(l=i,Vn("choicemenuitem",uv,l)).fold(db,(l=>B.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Qv({presets:o,textContent:t?e.text:B.none(),htmlContent:B.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:B.none(),checkMark:t?B.some(Jv(a.icons)):B.none(),caret:B.none(),value:e.value},a,i);return nn(Uv({data:jv(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Kp.set(e,t)},isActive:()=>Kp.isOn(e),isEnabled:()=>!bm.isDisabled(e),setEnabled:t=>bm.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[]},l,r,a),{toggling:{toggleClass:$f,toggleOnExecute:!1,selected:e.active}})})(l,1===o,n,t,r(i.value),s,a,cb(e))))):B.none();var l}))),Ky=(e,t)=>{const o=ob(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group")}},Yy=qr("cell-over"),Jy=qr("cell-execute"),Zy=(e,t,o)=>{const n=o=>yr(o,Jy,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return Ua({dom:{tag:"div",attributes:{role:"button","aria-labelledby":o}},behaviours:il([Dp("insert-table-picker-cell",[Tr(Fs(),zp.focus),Tr(Js(),n),Tr(Ns(),s),Tr(Qs(),s)]),Kp.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),zp.config({onFocus:o=>yr(o,Yy,{row:e,col:t})})])})},Qy=e=>X(e,(e=>P(e,ja))),ex=(e,t)=>Pa(`${t}x${e}`),tx={inserttable:e=>{const t=qr("size-label"),o=((e,t,o)=>{const n=[];for(let t=0;t<10;t++){const o=[];for(let n=0;n<10;n++)o.push(Zy(t,n,e));n.push(o)}return n})(t),n=ex(0,0),s=kh({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:t}},components:[n],behaviours:il([Ap.config({})])});return{type:"widget",data:{value:qr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[ny.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:Qy(o).concat(s.asSpec()),behaviours:il([Dp("insert-table-picker",[Vr((e=>{Ap.set(s.get(e),[n])})),Ar(Yy,((e,t,n)=>{const{row:r,col:a}=n.event;((e,t,o,n,s)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)Kp.set(e[n][s],n<=t&&s<=o)})(o,r,a),Ap.set(s.get(e),[ex(r+1,a+1)])})),Ar(Jy,((t,o,n)=>{const{row:s,col:r}=n.event;e.onAction({numRows:s+1,numColumns:r+1}),vr(t,tr())}))]),Sp.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>Ly(t.colorinput.getColors(),o)),(e=>e.concat(Py(o))))})(e,t),n=t.colorinput.getColorCols(),s="color",r={...qy(qr("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,Wf.CLOSE_ON_EXECUTE,_,t.shared.providers),markers:ob(s),movement:Ky(n,s)};return{type:"widget",data:{value:qr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[ny.widget(gh.sketch(r))]}}},ox=e=>({type:"separator",dom:{tag:"div",classes:[jf,"tox-collection__group-heading"]},components:e.text.map(Pa).toArray()});var nx;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(nx||(nx={}));const sx=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:B.none(),icon:e.text.isSome()?B.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,Vn("menuitem",fv,i)).fold(db,(e=>B.some(((e,t,o,n=!0)=>{const s=Qv({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:B.none(),ariaLabel:e.text,caret:B.none(),checkMark:B.none(),shortcutContent:e.shortcut},o,n);return Uv({data:jv(e),getApi:e=>({isEnabled:()=>!bm.isDisabled(e),setEnabled:t=>bm.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>Vn("nestedmenuitem",bv,e))(e).fold(db,(e=>B.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,$v("chevron-down",a,[Kf])):(e=>$v("chevron-right",e,[Kf]))(o.icons);var a;const i=Qv({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:B.none(),ariaLabel:e.text,caret:B.some(r),checkMark:B.none(),shortcutContent:e.shortcut},o,n);return Uv({data:jv(e),getApi:e=>({isEnabled:()=>!bm.isDisabled(e),setEnabled:t=>bm.set(e,!t)}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>Vn("togglemenuitem",vv,e))(e).fold(db,(e=>B.some(((e,t,o,n=!0)=>{const s=Qv({iconContent:e.icon,textContent:e.text,htmlContent:B.none(),ariaLabel:e.text,checkMark:B.some(Jv(o.icons)),caret:B.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return nn(Uv({data:jv(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Kp.set(e,t)},isActive:()=>Kp.isOn(e),isEnabled:()=>!bm.isDisabled(e),setEnabled:t=>bm.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:$f,toggleOnExecute:!1,selected:e.active}})})(a(e),t,r,n))));case"separator":return(e=>Vn("separatormenuitem",Lb,e))(e).fold(db,(e=>B.some(ox(e))));case"fancymenuitem":return(e=>Vn("fancymenuitem",hv,e))(e).fold(db,(e=>((e,t)=>fe(tx,e.fancytype).map((o=>o(e,t))))(a(e),o)));default:return console.error("Unknown item in general menu",e),B.none()}var i},rx=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||cb(e);return xe(P(e,(e=>{switch(e.type){case"separator":return(n=e,Vn("Autocompleter.Separator",Lb,n)).fold(db,(e=>B.some(ox(e))));case"cardmenuitem":return(e=>Vn("cardmenuitem",dv,e))(e).fold(db,(e=>B.some(((e,t,o,n)=>{const s={dom:Zv(e.label),optComponents:[B.some({dom:{tag:"div",classes:[Jf,Zf]},components:oy(e.items,n)})]};return Uv({data:jv({text:B.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!bm.isDisabled(e),setEnabled:t=>{bm.set(e,!t),N(Mc(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(bm)&&bm.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:B.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:ey(e.meta,r),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>Vn("Autocompleter.Item",Wb,e))(e).fold(db,(e=>B.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Qv({presets:n,textContent:B.none(),htmlContent:o?e.text.map((e=>ty(e,t))):B.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:B.none(),checkMark:B.none(),caret:B.none(),value:e.value},a.providers,i,e.icon);return Uv({data:jv(e),enabled:e.enabled,getApi:y({}),onAction:t=>s(e.value,e.meta),onSetup:y(b),triggersSubmenu:!1,itemBehaviours:ey(e.meta,a)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},ax=(e,t,o,n,s)=>{const r=cb(t),a=xe(P(t,(e=>{const t=e=>sx(e,o,n,(e=>s?!be(e,"text"):r)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)})));return(s?ub:mb)(e,r,a,1,"normal")},ix=e=>yh.singleData(e.value,e),lx=(e,t)=>{const o=ps(!1),n=ps(!1),s=Ua(xh.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:il([Dp("dismissAutocompleter",[Tr(dr(),(()=>i()))])]),lazySink:t.getSink})),r=n.get,a=()=>{r()&&xh.hide(s)},i=()=>e.execCommand("mceAutocompleterClose"),l=n=>{const r=(n=>{const s=re(n,(e=>B.from(e.columns))).getOr(1);return X(n,(n=>{const r=n.items;return rx(r,n.matchText,((t,s)=>{const r=e.selection.getRng();((e,t)=>Nf(Fe(t.startContainer)).map((t=>{const o=e.createRng();return o.selectNode(t.dom),o})))(e.dom,r).each((r=>{const l={hide:()=>i(),reload:t=>{a(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};o.set(!0),n.onAction(l,r,t,s),o.set(!1)}))}),s,Wf.BUBBLE_TO_SANDBOX,t,n.highlightOn)}))})(n);r.length>0?((t,o)=>{var n;(n=Fe(e.getBody()),Za(n,Pf)).each((n=>{const r=re(t,(e=>B.from(e.columns))).getOr(1);xh.showAt(s,gh.sketch(((e,t,o,n)=>{const s=o===nx.ContentFocus?Km():Xm(),r=Ky(t,n),a=ob(n);return{dom:e.dom,components:e.components,items:e.items,value:e.value,markers:{selectedItem:a.selectedItem,item:a.item},movement:r,fakeFocus:o===nx.ContentFocus,focusManager:s,menuBehaviours:xv("auto"!==t?[]:[Vr(((e,t)=>{yv(e,4,a.item).each((({numColumns:t,numRows:o})=>{Sp.setGridSize(e,o,t)}))}))])}})(mb("autocompleter-value",!0,o,r,"normal"),r,nx.ContentFocus,"normal")),{anchor:{type:"node",root:Fe(e.getBody()),node:B.from(n)}}),xh.getContent(s).each(Tm.highlightFirst)}))})(n,r):a()};e.on("AutocompleterStart",(({lookupData:e})=>{n.set(!0),o.set(!1),l(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>l(e))),e.on("AutocompleterEnd",(()=>{a(),n.set(!1),o.set(!1)}));((e,t)=>{const o=(e,t)=>{yr(e,Rs(),{raw:t})};t.on("keydown",(t=>{const n=()=>e.getView().bind(Tm.getHighlighted);e.isActive()&&(27===t.which&&e.cancelIfNecessary(),e.isMenuOpen()?13===t.which?(n().each(xr),t.preventDefault()):40===t.which?(n().fold((()=>{e.getView().each(Tm.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==t.which&&38!==t.which&&39!==t.which||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==t.which&&38!==t.which&&40!==t.which||e.cancelIfNecessary())})),t.on("NodeChange",(t=>{e.isActive()&&!e.isProcessingAction()&&Nf(Fe(t.element)).isNone()&&e.cancelIfNecessary()}))})({cancelIfNecessary:i,isMenuOpen:()=>xh.isOpen(s),isActive:r,isProcessingAction:o.get,getView:()=>xh.getContent(s)},e)},cx=(e,t,o)=>Qa(e,t,o).isSome(),dx=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},ux=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?B.none():B.some(t.touches[0])},mx=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=Rl(),o=ps(!1),n=dx((t=>{e.triggerEvent(er(),t),o.set(!0)}),400),s=bs([{key:Os(),value:e=>(ux(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),B.none())},{key:_s(),value:e=>(n.cancel(),ux(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),B.none())},{key:Ts(),value:s=>(n.cancel(),t.get().filter((e=>je(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(Qs(),s))))}]);return{fireIfReady:(e,t)=>fe(s,t).bind((t=>t(e)))}})(o),s=P(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>Hl(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=Rl(),a=Hl(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(Ks(),e)}),0))})),i=Hl(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===Em[0]&&!V(["input","textarea"],Ve(e.target))&&!cx(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=Hl(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=Rl(),d=Hl(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(Xs(),e)}),0))}));return{unbind:()=>{N(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},gx=(e,t)=>{const o=fe(e,"target").getOr(t);return ps(o)},px=hs([{stopped:[]},{resume:["element"]},{complete:[]}]),hx=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=ps(!1),n=ps(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),px.complete())),(e=>{const o=e.descHandler;return pa(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),px.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),px.complete()):Ye(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),px.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),px.resume(n))))}))},fx=(e,t,o,n,s,r)=>hx(e,t,o,n,s,r).fold(T,(n=>fx(e,t,o,n,s,r)),_),bx=(e,t,o,n,s)=>{const r=gx(o,n);return fx(e,t,o,n,r,s)},vx=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{le(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:S.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{le(e,((e,o)=>{be(e,t)&&delete e[t]}))},filterByType:t=>fe(e,t).map((e=>ge(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>fe(e,o).bind((e=>xs(n,(t=>((e,t)=>Qr(t).bind((t=>fe(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{Qr(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return Qr(t).getOrThunk((()=>((e,t)=>{const o=qr(Yr+"uid-");return Zr(t,o),o})(0,e.element)))})(n);ve(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+Wr(s.element)+"\nCannot use it for: "+Wr(e.element)+"\nThe conflicting element is"+(dt(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>fe(t,e)}},yx=Zu({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:Zd(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[rs("components",[]),Jd("containerBehaviours",[]),rs("events",{}),rs("domModification",{}),rs("eventOrder",{})]}),xx=e=>{const t=t=>Ye(e.element).fold(T,(e=>je(t,e))),o=vx(),n=(e,n)=>o.find(t,e,n),s=mx(e.element,{triggerEvent:(e,t)=>ai(e,t.target,(o=>((e,t,o,n)=>bx(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:y("real"),triggerEvent:(e,t,o)=>{ai(e,t,(s=>bx(n,e,o,t,s)))},triggerFocus:(e,t)=>{Qr(e).fold((()=>{hl(e)}),(o=>{ai(qs(),e,(o=>(((e,t,o,n,s)=>{const r=gx(o,n);hx(e,t,o,n,r,s)})(n,qs(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:Ua,buildOrPatch:Wa,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:T},a=e=>{e.connect(r),He(e.element)||(o.register(e),N(e.components(),a),r.triggerEvent(nr(),e.element,{target:e.element}))},i=e=>{He(e.element)||(N(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{md(e,t)},c=e=>{hd(e)},d=e=>{const t=o.filter(Ys());N(t,(t=>{const o=t.descHandler;pa(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t,o)=>{const n=(e=>{const t=ps(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:_,event:e,setSource:C("Cannot set source of a broadcasted event"),getSource:C("Cannot get source of a broadcasted event")}})(t);return N(e,(e=>{const t=e.descHandler;pa(t)(n)})),n.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>$o.error(new Error('Could not find component with uid: "'+e+'" in system.'))),$o.value),h=e=>{const t=Qr(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),Ao(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},wx=y([rs("prefix","form-field"),Jd("fieldBehaviours",[nm,Yd])]),Sx=y([_u({schema:[Un("dom")],name:"label"}),_u({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[Un("text")],name:"aria-descriptor"}),Cu({factory:{sketch:e=>{const t=((e,t)=>{const o={};return le(e,((e,n)=>{V(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[Un("factory")],name:"field"})]),kx=Qu({name:"FormField",configFields:wx(),partFields:Sx(),factory:(e,t,o,n)=>{const s=Qd(e.fieldBehaviours,[nm.config({find:t=>Hu(t,e,"field")}),Yd.config({store:{mode:"manual",getValue:e=>nm.getCurrent(e).bind(Yd.getValue),setValue:(e,t)=>{nm.getCurrent(e).each((e=>{Yd.setValue(e,t)}))}}})]),r=Cr([Vr(((t,o)=>{const n=Nu(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=qr(e.prefix);n.label().each((e=>{pt(e.element,"for",o),pt(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=qr(e.prefix);pt(o.element,"id",n),pt(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>Hu(t,e,"field"),getLabel:t=>Hu(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}});var Cx=Object.freeze({__proto__:null,exhibit:(e,t)=>ga({attributes:bs([{key:t.tabAttr,value:"true"}])})}),Ox=[rs("tabAttr","data-alloy-tabstop")];const _x=cl({fields:Ox,name:"tabstopping",active:Cx});var Tx=tinymce.util.Tools.resolve("tinymce.html.Entities");const Ex=(e,t,o,n)=>{const s=Bx(e,t,o,n);return kx.sketch(s)},Bx=(e,t,o,n)=>({dom:Mx(o),components:e.toArray().concat([t]),fieldBehaviours:il(n)}),Mx=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),Ax=(e,t)=>kx.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Pa(t.translate(e))]}),Dx=qr("form-component-change"),Fx=qr("form-close"),Ix=qr("form-cancel"),Vx=qr("form-action"),Rx=qr("form-submit"),zx=qr("form-block"),Hx=qr("form-unblock"),Px=qr("form-tabchange"),Nx=qr("form-resize"),Lx=y([Zn("data"),rs("inputAttributes",{}),rs("inputStyles",{}),rs("tag","input"),rs("inputClasses",[]),hi("onSetValue"),rs("styles",{}),rs("eventOrder",{}),Jd("inputBehaviours",[Yd,zp]),rs("selectOnFocus",!0)]),Wx=e=>il([zp.config({onFocus:e.selectOnFocus?e=>{const t=e.element,o=Ma(t);t.dom.setSelectionRange(0,o.length)}:b})]),Ux=e=>({...Wx(e),...Qd(e.inputBehaviours,[Yd.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:e=>Ma(e.element),setValue:(e,t)=>{Ma(e.element)!==t&&Aa(e.element,t)}},onSetValue:e.onSetValue})])}),jx=e=>({tag:e.tag,attributes:{type:"text",...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),Gx=Zu({name:"Input",configFields:Lx(),factory:(e,t)=>({uid:e.uid,dom:jx(e),components:[],behaviours:Ux(e),eventOrder:e.eventOrder})}),$x=e=>{let t=B.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=B.some(e),N(o,r),o=[])})),{get:n,map:e=>$x((t=>{n((o=>{t(e(o))}))})),isReady:s}},qx={nu:$x,pure:e=>$x((t=>{t(e)}))},Xx=e=>{setTimeout((()=>{throw e}),0)},Kx=e=>{const t=t=>{e().then(t,Xx)};return{map:t=>Kx((()=>e().then(t))),bind:t=>Kx((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>Kx((()=>e().then((()=>t.toPromise())))),toLazy:()=>qx.nu(t),toCached:()=>{let t=null;return Kx((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},Yx=e=>Kx((()=>new Promise(e))),Jx=e=>Kx((()=>Promise.resolve(e))),Zx=["input","textarea"],Qx=e=>{const t=Ve(e);return V(Zx,t)},ew=(e,t)=>{const o=t.getRoot(e).getOr(e.element);_a(o,t.invalidClass),t.notify.each((t=>{Qx(e.element)&&pt(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{Nr(e,t.validHtml)})),t.onValid(e)}))},tw=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);Oa(s,t.invalidClass),t.notify.each((t=>{Qx(e.element)&&pt(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{Nr(e,n)})),t.onInvalid(e,n)}))},ow=(e,t,o)=>t.validator.fold((()=>Jx($o.value(!0))),(t=>t.validate(e))),nw=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),ow(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(tw(e,t,0,o),$o.error(o))),(o=>(ew(e,t),$o.value(o)))):$o.error("No longer in system"))));var sw=Object.freeze({__proto__:null,markValid:ew,markInvalid:tw,query:ow,run:nw,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return Ta(o,t.invalidClass)}}),rw=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>Cr([Tr(t.onEvent,(t=>{nw(t,e).get(x)}))].concat(t.validateOnLoad?[Vr((t=>{nw(t,e).get(b)}))]:[])))).getOr({})}),aw=[Un("invalidClass"),rs("getRoot",B.none),ss("notify",[rs("aria","alert"),rs("getContainer",B.none),rs("validHtml",""),hi("onValid"),hi("onInvalid"),hi("onValidate")]),ss("validator",[Un("validate"),rs("onEvent","input"),rs("validateOnLoad",!0)])];const iw=cl({fields:aw,name:"invalidating",active:rw,apis:sw,extra:{validation:e=>t=>{const o=Yd.getValue(t);return Jx(e(o))}}});var lw=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n)}),cw=[jn("others",In($o.value,Cn()))],dw=Object.freeze({__proto__:null,init:()=>{const e={},t=y({});return ua({readState:t,getOrCreate:(t,o,n)=>{const s=ae(o.others);if(s)return fe(e,n).getOrThunk((()=>{const s=fe(o.others,n).getOrDie("No information found for coupled component: "+n)(t),r=t.getSystem().build(s);return e[n]=r,r}));throw new Error("Cannot find coupled component: "+n+". Known coupled components: "+JSON.stringify(s,null,2))}})}});const uw=cl({fields:cw,name:"coupling",apis:lw,state:dw}),mw=y("sink"),gw=y(_u({name:mw(),overrides:y({dom:{tag:"div"},behaviours:il([rd.config({useFixed:T})]),events:Cr([Dr(Rs()),Dr(Bs()),Dr(Ns())])})}));var pw;!function(e){e[e.HighlightFirst=0]="HighlightFirst",e[e.HighlightNone=1]="HighlightNone"}(pw||(pw={}));const hw=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},fw=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=yw(n,e);return i.map((e=>e.bind((e=>B.from(yh.sketch({...r.menu(),uid:ea(""),data:e,highlightImmediately:a===pw.HighlightFirst,onOpenMenu:(e,t)=>{const n=l().getOrDie();rd.position(n,t,{anchor:o}),Md.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();rd.position(n,o,{anchor:{type:"submenu",item:t}}),Md.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();rd.position(s,t,{anchor:o}),N(n,(e=>{rd.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(zp.focus(n),Md.close(s),B.some(!0))}))))))})(e,t,hw(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{Md.isOpen(n)&&Md.close(n)}),(e=>{Md.cloak(n),Md.open(n,e),r(n)})),n)))},bw=(e,t,o,n,s,r,a)=>(Md.close(n),Jx(n)),vw=(e,t,o,n,s,r)=>{const a=uw.getCoupled(o,"sandbox");return(Md.isOpen(a)?bw:fw)(e,t,o,a,n,s,r)},yw=(e,t)=>e.getSystem().getByUid(t.uid+"-"+mw()).map((e=>()=>$o.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>$o.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),xw=e=>{Md.getState(e).each((e=>{yh.repositionMenus(e)}))},ww=(e,t,o)=>{const n=ti(),s=yw(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id,role:"listbox"}},behaviours:tu(e.sandboxBehaviours,[Yd.config({store:{mode:"memory",initialValue:t}}),Md.config({onOpen:(s,r)=>{const a=hw(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=nm.getCurrent(t).getOr(t),s=Wt(e.element);o?St(n.element,"min-width",s+"px"):((e,t)=>{Lt.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,s)=>{n.unlink(t.element),void 0!==o&&void 0!==o.onClose&&o.onClose(e,s)},isPartOf:(e,o,n)=>oi(o,n)||oi(t,n),getAttachPoint:()=>s().getOrDie()}),nm.config({find:e=>Md.getState(e).bind((e=>nm.getCurrent(e)))}),gl.config({channels:{...Vd({isExtraPart:_}),...zd({doReposition:xw})}})])}},Sw=e=>{const t=uw.getCoupled(e,"sandbox");xw(t)},kw=()=>[rs("sandboxClasses",[]),eu("sandboxBehaviours",[nm,gl,Md,Yd])],Cw=y([Un("dom"),Un("fetch"),hi("onOpen"),fi("onExecute"),rs("getHotspot",B.some),rs("getAnchorOverrides",y({})),sc(),Jd("dropdownBehaviours",[Kp,uw,Sp,zp]),Un("toggleClass"),rs("eventOrder",{}),Zn("lazySink"),rs("matchWidth",!1),rs("useMinWidth",!1),Zn("role")].concat(kw())),Ow=y([Ou({schema:[mi()],name:"menu",defaults:e=>({onExecute:e.onExecute})}),gw()]),_w=Qu({name:"Dropdown",configFields:Cw(),partFields:Ow(),factory:(e,t,o,n)=>{const s=e=>{Md.getState(e).each((e=>{yh.highlightPrimary(e)}))},r={expand:t=>{Kp.isOn(t)||vw(e,x,t,n,b,pw.HighlightNone).get(b)},open:t=>{Kp.isOn(t)||vw(e,x,t,n,b,pw.HighlightFirst).get(b)},isOpen:Kp.isOn,close:t=>{Kp.isOn(t)&&vw(e,x,t,n,b,pw.HighlightFirst).get(b)},repositionMenus:e=>{Kp.isOn(e)&&Sw(e)}},a=(e,t)=>(xr(e),B.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:Qd(e.dropdownBehaviours,[Kp.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),uw.config({others:{sandbox:t=>ww(e,t,{onOpen:()=>Kp.on(t),onClose:()=>Kp.off(t)})}}),Sp.config({mode:"special",onSpace:a,onEnter:a,onDown:(e,t)=>{if(_w.isOpen(e)){const t=uw.getCoupled(e,"sandbox");s(t)}else _w.open(e);return B.some(!0)},onEscape:(e,t)=>_w.isOpen(e)?(_w.close(e),B.some(!0)):B.none()}),zp.config({})]),events:Jp(B.some((t=>{vw(e,x,t,n,s,pw.HighlightFirst).get(b)}))),eventOrder:{...e.eventOrder,[Js()]:["disabling","toggling","alloy.base.behaviour"]},apis:r,domModification:{attributes:{"aria-haspopup":"true",...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:("type",fe(e.dom,"attributes").bind((e=>fe(e,"type")))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),Tw=cl({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>Cr([Or(js(),T)]),exhibit:()=>ga({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),Ew=qr("color-input-change"),Bw=qr("color-swatch-change"),Mw=qr("color-picker-cancel"),Aw=_u({schema:[Un("dom")],name:"label"}),Dw=e=>_u({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:Cr([Er(Os(),((t,o,n)=>e(t,n)),[t]),Er(Bs(),((t,o,n)=>e(t,n)),[t]),Er(Ms(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),Fw=Dw("top-left"),Iw=Dw("top"),Vw=Dw("top-right"),Rw=Dw("right"),zw=Dw("bottom-right"),Hw=Dw("bottom"),Pw=Dw("bottom-left");var Nw=[Aw,Dw("left"),Rw,Iw,Hw,Fw,Vw,Pw,zw,Cu({name:"thumb",defaults:y({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:Cr([Mr(Os(),e,"spectrum"),Mr(_s(),e,"spectrum"),Mr(Ts(),e,"spectrum"),Mr(Bs(),e,"spectrum"),Mr(Ms(),e,"spectrum"),Mr(Ds(),e,"spectrum")])})}),Cu({schema:[Ln("mouseIsDown",(()=>ps(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:il([Sp.config({mode:"special",onLeft:o=>t.onLeft(o,e),onRight:o=>t.onRight(o,e),onUp:o=>t.onUp(o,e),onDown:o=>t.onDown(o,e)}),zp.config({})]),events:Cr([Tr(Os(),o),Tr(_s(),o),Tr(Bs(),o),Tr(Ms(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}})];const Lw=y("slider.change.value"),Ww=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?B.some(e.touches[0]).map((e=>zt(e.clientX,e.clientY))):B.none()}{const e=t;return void 0!==e.clientX?B.some(e).map((e=>zt(e.clientX,e.clientY))):B.none()}},Uw=e=>e.model.minX,jw=e=>e.model.minY,Gw=e=>e.model.minX-1,$w=e=>e.model.minY-1,qw=e=>e.model.maxX,Xw=e=>e.model.maxY,Kw=e=>e.model.maxX+1,Yw=e=>e.model.maxY+1,Jw=(e,t,o)=>t(e)-o(e),Zw=e=>Jw(e,qw,Uw),Qw=e=>Jw(e,Xw,jw),eS=e=>Zw(e)/2,tS=e=>Qw(e)/2,oS=e=>e.stepSize,nS=e=>e.snapToGrid,sS=e=>e.snapStart,rS=e=>e.rounded,aS=(e,t)=>void 0!==e[t+"-edge"],iS=e=>aS(e,"left"),lS=e=>aS(e,"right"),cS=e=>aS(e,"top"),dS=e=>aS(e,"bottom"),uS=e=>e.model.value.get(),mS=(e,t)=>({x:e,y:t}),gS=(e,t)=>{yr(e,Lw(),{value:t})},pS=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),hS=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),fS=(e,t,o)=>Math.max(t,Math.min(o,e)),bS=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=fS(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return fS(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},vS=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},yS="top",xS="right",wS="bottom",SS="left",kS=e=>e.element.dom.getBoundingClientRect(),CS=(e,t)=>e[t],OS=e=>{const t=kS(e);return CS(t,SS)},_S=e=>{const t=kS(e);return CS(t,xS)},TS=e=>{const t=kS(e);return CS(t,yS)},ES=e=>{const t=kS(e);return CS(t,wS)},BS=e=>{const t=kS(e);return CS(t,"width")},MS=e=>{const t=kS(e);return CS(t,"height")},AS=(e,t,o)=>(e+t)/2-o,DS=(e,t)=>{const o=kS(e),n=kS(t),s=CS(o,SS),r=CS(o,xS),a=CS(n,SS);return AS(s,r,a)},FS=(e,t)=>{const o=kS(e),n=kS(t),s=CS(o,yS),r=CS(o,wS),a=CS(n,yS);return AS(s,r,a)},IS=(e,t)=>{yr(e,Lw(),{value:t})},VS=(e,t,o)=>{const n={min:Uw(t),max:qw(t),range:Zw(t),value:o,step:oS(t),snap:nS(t),snapStart:sS(t),rounded:rS(t),hasMinEdge:iS(t),hasMaxEdge:lS(t),minBound:OS(e),maxBound:_S(e),screenRange:BS(e)};return bS(n)},RS=e=>(t,o)=>((e,t,o)=>{const n=(e>0?hS:pS)(uS(o),Uw(o),qw(o),oS(o));return IS(t,n),B.some(n)})(e,t,o).map(T),zS=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=BS(e),a=n.bind((t=>B.some(DS(t,e)))).getOr(0),i=s.bind((t=>B.some(DS(t,e)))).getOr(r),l={min:Uw(t),max:qw(t),range:Zw(t),value:o,hasMinEdge:iS(t),hasMaxEdge:lS(t),minBound:OS(e),minOffset:0,maxBound:_S(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return vS(l)})(t,r,o,n,s);return OS(t)-OS(e)+a},HS=RS(-1),PS=RS(1),NS=B.none,LS=B.none,WS={"top-left":B.none(),top:B.none(),"top-right":B.none(),right:B.some(((e,t)=>{gS(e,Kw(t))})),"bottom-right":B.none(),bottom:B.none(),"bottom-left":B.none(),left:B.some(((e,t)=>{gS(e,Gw(t))}))};var US=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=VS(e,t,o);return IS(e,n),n},setToMin:(e,t)=>{const o=Uw(t);IS(e,o)},setToMax:(e,t)=>{const o=qw(t);IS(e,o)},findValueOfOffset:VS,getValueFromEvent:e=>Ww(e).map((e=>e.left)),findPositionOfValue:zS,setPositionFromValue:(e,t,o,n)=>{const s=uS(o),r=zS(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=Wt(t.element)/2;St(t.element,"left",r-a+"px")},onLeft:HS,onRight:PS,onUp:NS,onDown:LS,edgeActions:WS});const jS=(e,t)=>{yr(e,Lw(),{value:t})},GS=(e,t,o)=>{const n={min:jw(t),max:Xw(t),range:Qw(t),value:o,step:oS(t),snap:nS(t),snapStart:sS(t),rounded:rS(t),hasMinEdge:cS(t),hasMaxEdge:dS(t),minBound:TS(e),maxBound:ES(e),screenRange:MS(e)};return bS(n)},$S=e=>(t,o)=>((e,t,o)=>{const n=(e>0?hS:pS)(uS(o),jw(o),Xw(o),oS(o));return jS(t,n),B.some(n)})(e,t,o).map(T),qS=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=MS(e),a=n.bind((t=>B.some(FS(t,e)))).getOr(0),i=s.bind((t=>B.some(FS(t,e)))).getOr(r),l={min:jw(t),max:Xw(t),range:Qw(t),value:o,hasMinEdge:cS(t),hasMaxEdge:dS(t),minBound:TS(e),minOffset:0,maxBound:ES(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return vS(l)})(t,r,o,n,s);return TS(t)-TS(e)+a},XS=B.none,KS=B.none,YS=$S(-1),JS=$S(1),ZS={"top-left":B.none(),top:B.some(((e,t)=>{gS(e,$w(t))})),"top-right":B.none(),right:B.none(),"bottom-right":B.none(),bottom:B.some(((e,t)=>{gS(e,Yw(t))})),"bottom-left":B.none(),left:B.none()};var QS=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=GS(e,t,o);return jS(e,n),n},setToMin:(e,t)=>{const o=jw(t);jS(e,o)},setToMax:(e,t)=>{const o=Xw(t);jS(e,o)},findValueOfOffset:GS,getValueFromEvent:e=>Ww(e).map((e=>e.top)),findPositionOfValue:qS,setPositionFromValue:(e,t,o,n)=>{const s=uS(o),r=qS(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=It(t.element)/2;St(t.element,"top",r-a+"px")},onLeft:XS,onRight:KS,onUp:YS,onDown:JS,edgeActions:ZS});const ek=(e,t)=>{yr(e,Lw(),{value:t})},tk=(e,t)=>({x:e,y:t}),ok=(e,t)=>(o,n)=>((e,t,o,n)=>{const s=e>0?hS:pS,r=t?uS(n).x:s(uS(n).x,Uw(n),qw(n),oS(n)),a=t?s(uS(n).y,jw(n),Xw(n),oS(n)):uS(n).y;return ek(o,tk(r,a)),B.some(r)})(e,t,o,n).map(T),nk=ok(-1,!1),sk=ok(1,!1),rk=ok(-1,!0),ak=ok(1,!0),ik={"top-left":B.some(((e,t)=>{gS(e,mS(Gw(t),$w(t)))})),top:B.some(((e,t)=>{gS(e,mS(eS(t),$w(t)))})),"top-right":B.some(((e,t)=>{gS(e,mS(Kw(t),$w(t)))})),right:B.some(((e,t)=>{gS(e,mS(Kw(t),tS(t)))})),"bottom-right":B.some(((e,t)=>{gS(e,mS(Kw(t),Yw(t)))})),bottom:B.some(((e,t)=>{gS(e,mS(eS(t),Yw(t)))})),"bottom-left":B.some(((e,t)=>{gS(e,mS(Gw(t),Yw(t)))})),left:B.some(((e,t)=>{gS(e,mS(Gw(t),tS(t)))}))};var lk=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=VS(e,t,o.left),s=GS(e,t,o.top),r=tk(n,s);return ek(e,r),r},setToMin:(e,t)=>{const o=Uw(t),n=jw(t);ek(e,tk(o,n))},setToMax:(e,t)=>{const o=qw(t),n=Xw(t);ek(e,tk(o,n))},getValueFromEvent:e=>Ww(e),setPositionFromValue:(e,t,o,n)=>{const s=uS(o),r=zS(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=qS(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=Wt(t.element)/2,l=It(t.element)/2;St(t.element,"left",r-i+"px"),St(t.element,"top",a-l+"px")},onLeft:nk,onRight:sk,onUp:rk,onDown:ak,edgeActions:ik});const ck=Qu({name:"Slider",configFields:[rs("stepSize",1),rs("onChange",b),rs("onChoose",b),rs("onInit",b),rs("onDragStart",b),rs("onDragEnd",b),rs("snapToGrid",!1),rs("rounded",!0),Zn("snapStart"),jn("model",Pn("mode",{x:[rs("minX",0),rs("maxX",100),Ln("value",(e=>ps(e.mode.minX))),Un("getInitialValue"),yi("manager",US)],y:[rs("minY",0),rs("maxY",100),Ln("value",(e=>ps(e.mode.minY))),Un("getInitialValue"),yi("manager",QS)],xy:[rs("minX",0),rs("maxX",100),rs("minY",0),rs("maxY",100),Ln("value",(e=>ps({x:e.mode.minX,y:e.mode.minY}))),Un("getInitialValue"),yi("manager",lk)]})),Jd("sliderBehaviours",[Sp,Yd]),Ln("mouseIsDown",(()=>ps(!1)))],partFields:Nw,factory:(e,t,o,n)=>{const s=t=>Pu(t,e,"thumb"),r=t=>Pu(t,e,"spectrum"),a=t=>Hu(t,e,"left-edge"),i=t=>Hu(t,e,"right-edge"),l=t=>Hu(t,e,"top-edge"),c=t=>Hu(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&Hu(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)};return{uid:e.uid,dom:e.dom,components:t,behaviours:Qd(e.sliderBehaviours,[Sp.config({mode:"special",focusIn:t=>Hu(t,e,"spectrum").map(Sp.focusIn).map(T)}),Yd.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),gl.config({channels:{[Fd()]:{onReceive:p}}})]),events:Cr([Tr(Lw(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),B.some(!0)})(t,o.event.value)})),Vr(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),Tr(Os(),h),Tr(Ts(),f),Tr(Bs(),h),Tr(Ds(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),dk=qr("rgb-hex-update"),uk=qr("slider-update"),mk=qr("palette-update"),gk="form",pk=[Jd("formBehaviours",[Yd])],hk=e=>"<alloy.field."+e+">",fk=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Qd(e.formBehaviours,[Yd.config({store:{mode:"manual",getValue:t=>{const o=Lu(t,e);return ce(o,((e,t)=>e().bind((e=>{return o=nm.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+Wr(e.element)),o.fold((()=>$o.error(n)),$o.value);var o,n})).map(Yd.getValue)))},setValue:(t,o)=>{le(o,((o,n)=>{Hu(t,e,n).each((e=>{nm.getCurrent(e).each((e=>{Yd.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>Hu(t,e,o).bind(nm.getCurrent)}}),bk={getField:ca(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),Fu(gk,hk(t),o)),record:y(e)}})(),o=e(t),n=t.record(),s=P(n,(e=>Cu({name:e,pname:hk(e)})));return Xu(gk,pk,s,fk,o)}},vk=qr("valid-input"),yk=qr("invalid-input"),xk=qr("validating-input"),wk="colorcustom.rgb.",Sk=(e,t,o,n)=>{const s=(o,n)=>iw.config({invalidClass:t("invalid"),notify:{onValidate:e=>{yr(e,xk,{type:o})},onValid:e=>{yr(e,vk,{type:o,value:Yd.getValue(e)})},onInvalid:e=>{yr(e,yk,{type:o,value:Yd.getValue(e)})}},validator:{validate:t=>{const o=Yd.getValue(t),s=n(o)?$o.value(!0):$o.error(e("aria.input.invalid"));return Jx(s)},validateOnLoad:!1}}),r=(o,n,r,a,i)=>{const l=e("colorcustom.rgb.range"),c=kx.parts.label({dom:{tag:"label",attributes:{"aria-label":a}},components:[Pa(r)]}),d=kx.parts.field({data:i,factory:Gx,inputAttributes:{type:"text",..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:il([s(n,o),_x.config({})]),onSetValue:e=>{iw.isInvalid(e)&&iw.run(e).get(b)}}),u=[c,d],m="hex"!==n?[kx.parts["aria-descriptor"]({text:l})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(m)}},a=(e,t)=>{const o=t.red,n=t.green,s=t.blue;Yd.setValue(e,{red:o,green:n,blue:s})},i=kh({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),l=(e,t)=>{i.getOpt(e).each((e=>{St(e.element,"background-color","#"+t.value)}))},c=Zu({factory:()=>{const s={red:ps(B.some(255)),green:ps(B.some(255)),blue:ps(B.some(255)),hex:ps(B.some("ffffff"))},c=e=>s[e].get(),d=(e,t)=>{s[e].set(t)},u=e=>{const t=e.red,o=e.green,n=e.blue;d("red",B.some(t)),d("green",B.some(o)),d("blue",B.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?d(o.type,B.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=sy(t);d("hex",B.some(t));const s=yy(n);a(e,s),u(s),yr(e,dk,{hex:n}),l(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);d(t,B.some(n)),c("red").bind((e=>c("green").bind((t=>c("blue").map((o=>fy(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=dy(t);return bk.getField(e,"hex").each((t=>{zp.isFocused(t)||Yd.setValue(e,{hex:o.value})})),o})(e,t);yr(e,dk,{hex:o}),l(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(wk+t+".label"),description:e(wk+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return nn(bk.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",kx.sketch(r(by,"red",h.label,h.description,255))),o.field("green",kx.sketch(r(by,"green",f.label,f.description,255))),o.field("blue",kx.sketch(r(by,"blue",b.label,b.description,255))),o.field("hex",kx.sketch(r(iy,"hex",v.label,v.description,"ffffff"))),i.asSpec()],formBehaviours:il([iw.config({invalidClass:t("form-invalid")}),Dp("rgb-form-events",[Tr(vk,g),Tr(yk,m),Tr(xk,m)])])}))),{apis:{updateHex:(e,t)=>{Yd.setValue(e,{hex:t.value}),((e,t)=>{const o=yy(t);a(e,o),u(o)})(e,t),l(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return c},kk=(e,t)=>{const o=Zu({name:"ColourPicker",configFields:[Un("dom"),rs("onValidHex",b),rs("onInvalidHex",b)],factory:o=>{const n=Sk(e,t,o.onValidHex,o.onInvalidHex),s=((e,t)=>{const o=ck.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=ck.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)};return Zu({factory:e=>{const r=y({x:0,y:0}),a=il([nm.config({find:B.some}),zp.config({})]);return ck.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:r},rounded:!1,components:[o,n],onChange:(e,t,o)=>{yr(e,mk,{value:o})},onInit:(e,t,o,n)=>{s(o.element.dom,Sy(ky))},sliderBehaviours:a})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=Oy(t,100,100),r=vy(n);s(o,Sy(r))})(t,o)},setThumb:(e,t,o)=>{((e,t)=>{const o=_y(yy(t));ck.setValue(e,{x:o.saturation,y:100-o.value})})(t,o)}},extraApis:{}})})(0,t),r={paletteRgba:ps(ky),paletteHue:ps(0)},a=kh(((e,t)=>{const o=ck.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=ck.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return ck.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:y(0)},components:[o,n],sliderBehaviours:il([zp.config({})]),onChange:(e,t,o)=>{yr(e,uk,{value:o})}})})(0,t)),i=kh(s.sketch({})),l=kh(n.sketch({})),c=(e,t,o)=>{i.getOpt(e).each((e=>{s.setHue(e,o)}))},d=(e,t)=>{l.getOpt(e).each((e=>{n.updateHex(e,t)}))},u=(e,t,o)=>{a.getOpt(e).each((e=>{ck.setValue(e,(e=>100-e/360*100)(o))}))},m=(e,t)=>{i.getOpt(e).each((e=>{s.setThumb(e,t)}))},g=(e,t,o,n)=>{((e,t)=>{const o=yy(e);r.paletteRgba.set(o),r.paletteHue.set(t)})(t,o),N(n,(n=>{n(e,t,o)}))};return{uid:o.uid,dom:o.dom,components:[i.asSpec(),a.asSpec(),l.asSpec()],behaviours:il([Dp("colour-picker-events",[Tr(dk,(()=>{const e=[c,u,m];return(t,o)=>{const n=o.event.hex,s=(e=>_y(yy(e)))(n);g(t,n,s.hue,e)}})()),Tr(mk,(()=>{const e=[d];return(t,o)=>{const n=o.event.value,s=r.paletteHue.get(),a=Oy(s,n.x,100-n.y),i=Ty(a);g(t,i,s,e)}})()),Tr(uk,(()=>{const e=[c,d];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=r.paletteRgba.get(),a=_y(s),i=Oy(n,a.saturation,a.value),l=Ty(i);g(t,l,n,e)}})())]),nm.config({find:e=>l.getOpt(e)}),Sp.config({mode:"acyclic"})])}}});return o},Ck=()=>nm.config({find:B.some}),Ok=e=>nm.config({find:t=>et(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),_k=yn([rs("preprocess",x),rs("postprocess",x)]),Tk=(e,t,o)=>Yd.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),Ek=(e,t,o)=>Tk(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),Bk=(e,t)=>{const o=zn("RepresentingConfigs.memento processors",_k,t);return Yd.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=Yd.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);Yd.setValue(r,s)}}})},Mk=Ek,Ak=Tk,Dk=e=>Yd.config({store:{mode:"memory",initialValue:e}}),Fk={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var Ik=tinymce.util.Tools.resolve("tinymce.Resource"),Vk=tinymce.util.Tools.resolve("tinymce.util.Tools");const Rk=qr("alloy-fake-before-tabstop"),zk=qr("alloy-fake-after-tabstop"),Hk=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:il([zp.config({ignore:!0}),_x.config({})])}),Pk=e=>({dom:{tag:"div",classes:["tox-navobj"]},components:[Hk([Rk]),e,Hk([zk])],behaviours:il([Ok(1)])}),Nk=(e,t)=>{yr(e,Rs(),{raw:{which:9,shiftKey:t}})},Lk=(e,t)=>{const o=t.element;Ta(o,Rk)?Nk(e,!0):Ta(o,zk)&&Nk(e,!1)},Wk=e=>cx(e,["."+Rk,"."+zk].join(","),_),Uk=qr("toolbar.button.execute"),jk={[Js()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events"]},Gk=(e,t,o)=>Fh(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),$k=(e,t)=>Gk(e,t,[]),qk=(e,t)=>Gk(e,t,[Ap.config({})]),Xk=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[Pa(o.translate(e))],behaviours:il([Ap.config({})])}),Kk=qr("update-menu-text"),Yk=qr("update-menu-icon"),Jk=(e,t,o)=>{const n=ps(b),s=e.text.map((e=>kh(Xk(e,t,o.providers)))),r=e.icon.map((e=>kh(qk(e,o.providers.icons)))),a=(e,t)=>{const o=Yd.getValue(e);return zp.focus(o),yr(o,"keydown",{raw:t.event.raw}),_w.close(o),B.some(!0)},i=e.role.fold((()=>({})),(e=>({role:e}))),l=e.tooltip.fold((()=>({})),(e=>{const t=o.providers.translate(e);return{title:t,"aria-label":t}})),c=Fh("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons);return kh(_w.sketch({...e.uid?{uid:e.uid}:{},...i,dom:{tag:"button",classes:[t,`${t}--select`].concat(P(e.classes,(e=>`${t}--${e}`))),attributes:{...l}},components:Wv([r.map((e=>e.asSpec())),s.map((e=>e.asSpec())),B.some(c)]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:il([...e.dropdownBehaviours,Iv((()=>e.disabled||o.providers.isDisabled())),Fv(),Tw.config({}),Ap.config({}),Dp("dropdown-events",[Hv(e,n),Pv(e,n)]),Dp("menubutton-update-display-text",[Tr(Kk,((e,t)=>{s.bind((t=>t.getOpt(e))).each((e=>{Ap.set(e,[Pa(o.providers.translate(t.event.text))])}))})),Tr(Yk,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{Ap.set(e,[qk(t.event.icon,o.providers.icons)])}))}))])]),eventOrder:nn(jk,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:il([Sp.config({mode:"special",onLeft:a,onRight:a})]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:rb(0,e.columns,e.presets)},fetch:t=>Yx(S(e.fetch,t))})).asSpec()},Zk=e=>"separator"===e.type,Qk={type:"separator"},eC=(e,t)=>{const o=((e,t)=>{const o=j(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!Zk(e[e.length-1])?e.concat([Qk]):e:be(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&Zk(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return U(o,((e,o)=>{const n=(e=>{if(Zk(e))return e;{const t=fe(e,"value").getOrThunk((()=>qr("generated-menu-item")));return nn({value:t},e)}})(o),s=((e,t)=>(e=>be(e,"getSubmenuItems"))(e)?((e,t)=>{const o=e.getSubmenuItems(),n=eC(o,t);return{item:e,menus:nn(n.menus,fs(e.value,n.items)),expansions:nn(n.expansions,fs(e.value,e.value))}})(e,t):{item:e,menus:{},expansions:{}})(n,t);return{menus:nn(e.menus,s.menus),items:[s.item].concat(e.items),expansions:nn(e.expansions,s.expansions)}}),{menus:{},expansions:{},items:[]})},tC=(e,t,o,n)=>{const s=qr("primary-menu"),r=eC(e,o.shared.providers.menuItems());if(0===r.items.length)return B.none();const a=ax(s,r.items,t,o,n),i=ce(r.menus,((e,n)=>ax(n,e,t,o,!1))),l=nn(i,fs(s,a));return B.from(yh.tieredData(s,l,r.expansions))},oC=e=>!be(e,"items"),nC="data-value",sC=(e,t,o,n)=>P(o,(o=>oC(o)?{type:"togglemenuitem",text:o.text,value:o.value,active:o.value===n,onAction:()=>{Yd.setValue(e,o.value),yr(e,Dx,{name:t}),zp.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>sC(e,t,o.items,n)})),rC=(e,t)=>re(e,(e=>oC(e)?Se(e.value===t,e):rC(e.items,t))),aC=Zu({name:"HtmlSelect",configFields:[Un("options"),Jd("selectBehaviours",[zp,Yd]),rs("selectClasses",[]),rs("selectAttributes",{}),Zn("data")],factory:(e,t)=>{const o=P(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>fs("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:Qd(e.selectBehaviours,[zp.config({}),Yd.config({store:{mode:"manual",getValue:e=>Ma(e.element),setValue:(t,o)=>{G(e.options,(e=>e.value===o)).isSome()&&Aa(t.element,o)},...n}})])}}}),iC=y([rs("field1Name","field1"),rs("field2Name","field2"),bi("onLockedChange"),gi(["lockClass"]),rs("locked",!1),eu("coupledFieldBehaviours",[nm,Yd])]),lC=(e,t)=>Cu({factory:kx,name:e,overrides:e=>({fieldBehaviours:il([Dp("coupled-input-behaviour",[Tr(Hs(),(o=>{((e,t,o)=>Hu(e,t,o).bind(nm.getCurrent))(o,e,t).each((t=>{Hu(o,e,"lock").each((n=>{Kp.isOn(n)&&e.onLockedChange(o,t,n)}))}))}))])])})}),cC=y([lC("field1","field2"),lC("field2","field1"),Cu({factory:Sh,schema:[Un("dom")],name:"lock",overrides:e=>({buttonBehaviours:il([Kp.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),dC=Qu({name:"FormCoupledInputs",configFields:iC(),partFields:cC(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:tu(e.coupledFieldBehaviours,[nm.config({find:B.some}),Yd.config({store:{mode:"manual",getValue:t=>{const o=Uu(t,e,["field1","field2"]);return{[e.field1Name]:Yd.getValue(o.field1()),[e.field2Name]:Yd.getValue(o.field2())}},setValue:(t,o)=>{const n=Uu(t,e,["field1","field2"]);ve(o,e.field1Name)&&Yd.setValue(n.field1(),o[e.field1Name]),ve(o,e.field2Name)&&Yd.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>Hu(t,e,"field1"),getField2:t=>Hu(t,e,"field2"),getLock:t=>Hu(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),uC=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return $o.value({value:e,unit:o})}return $o.error(e)},mC=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>be(o,e);return e.unit===t?B.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?B.some(e.value):B.some(e.value/o[e.unit]*o[t]):B.none()},gC=e=>B.none(),pC=(e,t)=>{const o=e.label.map((e=>Ax(e,t))),n=[bm.config({disabled:()=>e.disabled||t.isDisabled()}),Fv(),Sp.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(vr(e,Rx),B.some(!0))}),Dp("textfield-change",[Tr(Hs(),((t,o)=>{yr(t,Dx,{name:e.name})})),Tr(Ks(),((t,o)=>{yr(t,Dx,{name:e.name})}))]),_x.config({})],s=e.validation.map((e=>iw.config({getRoot:e=>Je(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=Yd.getValue(t),n=e.validator(o);return Jx(!0===n?$o.value(o):$o.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r={...e.placeholder.fold(y({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(y({}),(e=>({inputmode:e})))},a=kx.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:r,inputClasses:[e.classname],inputBehaviours:il(q([n,s])),selectOnFocus:!1,factory:Gx}),i=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),l=[bm.config({disabled:()=>e.disabled||t.isDisabled(),onDisabled:e=>{kx.getField(e).each(bm.disable)},onEnabled:e=>{kx.getField(e).each(bm.enable)}}),Fv()];return Ex(o,a,i,l)};var hC=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return Cr([Tr(e.event,o),Rr((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[Tr(e,(()=>t.cancel()))])).getOr([])))}});const fC=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},bC=e=>{const t=ps(null);return ua({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var vC=Object.freeze({__proto__:null,throttle:bC,init:e=>e.stream.streams.state(e)}),yC=[jn("stream",Pn("mode",{throttle:[Un("delay"),rs("stopEvent",!0),yi("streams",{setup:(e,t)=>{const o=e.stream,n=fC(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:bC})]})),rs("event","input"),Zn("cancelEvent"),bi("onStream")];const xC=cl({fields:yC,name:"streaming",active:hC,state:vC}),wC=(e,t,o)=>{const n=Yd.getValue(o);Yd.setValue(t,n),kC(t)},SC=(e,t)=>{const o=e.element,n=Ma(o),s=o.dom;"number"!==ft(o,"type")&&t(s,n)},kC=e=>{SC(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},CC=y("alloy.typeahead.itemexecute"),OC=y([Zn("lazySink"),Un("fetch"),rs("minChars",5),rs("responseTime",1e3),hi("onOpen"),rs("getHotspot",B.some),rs("getAnchorOverrides",y({})),rs("layouts",B.none()),rs("eventOrder",{}),gs("model",{},[rs("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),rs("selectsOver",!0),rs("populateFromBrowse",!0)]),hi("onSetValue"),fi("onExecute"),hi("onItemExecute"),rs("inputClasses",[]),rs("inputAttributes",{}),rs("inputStyles",{}),rs("matchWidth",!0),rs("useMinWidth",!1),rs("dismissOnBlur",!0),gi(["openClass"]),Zn("initialData"),Jd("typeaheadBehaviours",[zp,Yd,xC,Sp,Kp,uw]),Ln("previewing",(()=>ps(!0)))].concat(Lx()).concat(kw())),_C=y([Ou({schema:[mi()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlight:(t,o)=>{e.previewing.get()?t.getSystem().getByUid(e.uid).each((n=>{((e,t,o)=>{if(e.selectsOver){const n=Yd.getValue(t),s=e.getDisplayText(n),r=Yd.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?B.some((()=>{wC(0,t,o),((e,t)=>{SC(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):B.none()}return B.none()})(e.model,n,o).fold((()=>Tm.dehighlight(t,o)),(e=>e()))})):t.getSystem().getByUid(e.uid).each((t=>{e.model.populateFromBrowse&&wC(e.model,t,o)})),e.previewing.set(!1)},onExecute:(t,o)=>t.getSystem().getByUid(e.uid).toOptional().map((e=>(yr(e,CC(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),t.getSystem().getByUid(e.uid).each((t=>{e.model.populateFromBrowse&&wC(e.model,t,o)}))}})})]),TC=Qu({name:"Typeahead",configFields:OC(),partFields:_C(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=uw.getCoupled(t,"sandbox");if(Md.isOpen(r))nm.getCurrent(r).each((e=>{Tm.getHighlighted(e).fold((()=>{s(e)}),(()=>{kr(r,e.element,"keydown",o)}))}));else{const o=e=>{nm.getCurrent(e).each(s)};fw(e,a(t),t,r,n,o,pw.HighlightFirst).get(b)}},r=Wx(e),a=e=>t=>t.map((t=>{const o=he(t.menus),n=X(o,(e=>W(e.items,(e=>"item"===e.type))));return Yd.getState(e).update(P(n,(e=>e.data))),t})),i=[zp.config({}),Yd.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>Ma(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{Aa(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>fs("initialValue",e))).getOr({})}}),xC.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=uw.getCoupled(t,"sandbox");if(zp.isFocused(t)&&Ma(t.element).length>=e.minChars){const o=nm.getCurrent(s).bind((e=>Tm.getHighlighted(e).map(Yd.getValue)));e.previewing.set(!0);const r=t=>{nm.getCurrent(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Tm.highlightFirst(t)}),(e=>{Tm.highlightBy(t,(t=>Yd.getValue(t).value===e.value)),Tm.getHighlighted(t).orThunk((()=>(Tm.highlightFirst(t),B.none())))}))}))};fw(e,a(t),t,s,n,r,pw.HighlightFirst).get(b)}},cancelEvent:or()}),Sp.config({mode:"special",onDown:(e,t)=>(s(e,t,Tm.highlightFirst),B.some(!0)),onEscape:e=>{const t=uw.getCoupled(e,"sandbox");return Md.isOpen(t)?(Md.close(t),B.some(!0)):B.none()},onUp:(e,t)=>(s(e,t,Tm.highlightLast),B.some(!0)),onEnter:t=>{const o=uw.getCoupled(t,"sandbox"),n=Md.isOpen(o);if(n&&!e.previewing.get())return nm.getCurrent(o).bind((e=>Tm.getHighlighted(e))).map((e=>(yr(t,CC(),{item:e}),!0)));{const s=Yd.getValue(t);return vr(t,or()),e.onExecute(o,t,s),n&&Md.close(o),B.some(!0)}}}),Kp.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),uw.config({others:{sandbox:t=>ww(e,t,{onOpen:()=>Kp.on(t),onClose:()=>Kp.off(t)})}}),Dp("typeaheadevents",[Hr((t=>{const o=b;vw(e,a(t),t,n,o,pw.HighlightFirst).get(b)})),Tr(CC(),((t,o)=>{const n=uw.getCoupled(t,"sandbox");wC(e.model,t,o.event.item),vr(t,or()),e.onItemExecute(t,n,o.event.item,Yd.getValue(t)),Md.close(n),kC(t)}))].concat(e.dismissOnBlur?[Tr(Xs(),(e=>{const t=uw.getCoupled(e,"sandbox");vl(t.element).isNone()&&Md.close(t)}))]:[]))];return{uid:e.uid,dom:jx(nn(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...Qd(e.typeaheadBehaviours,i)},eventOrder:e.eventOrder}}}),EC=e=>({...e,toCached:()=>EC(e.toCached()),bindFuture:t=>EC(e.bind((e=>e.fold((e=>Jx($o.error(e))),(e=>t(e)))))),bindResult:t=>EC(e.map((e=>e.bind(t)))),mapResult:t=>EC(e.map((e=>e.map(t)))),mapError:t=>EC(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>EC(Yx((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n($o.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),BC=e=>EC(Yx(e)),MC=e=>({isEnabled:()=>!bm.isDisabled(e),setEnabled:t=>bm.set(e,!t),setActive:t=>{const o=e.element;t?(Oa(o,"tox-tbtn--enabled"),pt(o,"aria-pressed",!0)):(_a(o,"tox-tbtn--enabled"),yt(o,"aria-pressed"))},isActive:()=>Ta(e.element,"tox-tbtn--enabled")}),AC=(e,t,o,n)=>Jk({text:e.text,icon:e.icon,tooltip:e.tooltip,role:n,fetch:(t,n)=>{e.fetch((e=>{n(tC(e,Wf.CLOSE_ON_EXECUTE,o,!1))}))},onSetup:e.onSetup,getApi:MC,columns:1,presets:"normal",classes:[],dropdownBehaviours:[_x.config({})]},t,o.shared),DC=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{hl(t.element),yr(t,Vx,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(P(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,onAction:n(e),onSetup:s(e)}})))}},FC=(e,t,o=[],n,s,r)=>{const a=t.fold((()=>({})),(e=>({action:e}))),i={buttonBehaviours:il([Iv((()=>!e.enabled||r.isDisabled())),Fv(),_x.config({}),Dp("button press",[_r("click"),_r("mousedown")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...a},l=nn(i,{dom:n});return nn(l,{components:s})},IC=(e,t,o,n=[])=>{const s={tag:"button",classes:["tox-tbtn"],attributes:e.tooltip.map((e=>({"aria-label":o.translate(e),title:o.translate(e)}))).getOr({})},r=e.icon.map((e=>$k(e,o.icons))),a=Wv([r]);return FC(e,t,n,s,a,o)},VC=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>$k(e,o.icons))),i=[a.getOrThunk((()=>Pa(r)))],l=[...(e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}})(e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary")),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s];return FC(e,t,n,{tag:"button",classes:l,attributes:{title:r}},i,o)},RC=(e,t,o,n=[],s=[])=>{const r=VC(e,B.some(t),o,n,s);return Sh.sketch(r)},zC=(e,t)=>o=>{"custom"===t?yr(o,Vx,{name:e,value:{}}):"submit"===t?vr(o,Rx):"cancel"===t?vr(o,Ix):console.error("Unknown button type: ",t)},HC=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,onSetup:t=>(t.setEnabled(e.enabled),b),fetch:DC(n.items,t,o)},r=kh(AC(s,"tox-tbtn",o,B.none()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=zC(e.name,t),s={...e,buttonType:B.none(),borderless:!1};return RC(s,n,o.shared.providers,[])}console.error("Unknown footer button type: ",t)},PC={type:"separator"},NC=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),LC=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),WC=(e,t)=>(e=>P(e,NC))(((e,t)=>W(t,(t=>t.type===e)))(e,t)),UC=e=>WC("header",e.targets),jC=e=>WC("anchor",e.targets),GC=e=>B.from(e.anchorTop).map((e=>LC("<top>",e))).toArray(),$C=e=>B.from(e.anchorBottom).map((e=>LC("<bottom>",e))).toArray(),qC=(e,t)=>{const o=e.toLowerCase();return W(t,(e=>{const t=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text;return Ce(t.toLowerCase(),o)||Ce(e.value.toLowerCase(),o)}))},XC=qr("aria-invalid"),KC=(e,t)=>{e.dom.checked=t},YC=e=>e.dom.checked,JC=e=>(t,o,n,s)=>fe(o,"name").fold((()=>e(o,s,B.none())),(r=>t.field(r,e(o,s,fe(n,r))))),ZC={bar:JC(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:P(e.items,t.interpreter)}))(e,t.shared))),collection:JC(((e,t,o)=>((e,t,o)=>{const n=e.label.map((e=>Ax(e,t))),s=e=>(t,o)=>{Qa(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,ft(n,"data-collection-item-value"))}))},r=s(((o,n,s,r)=>{n.stop(),t.isDisabled()||yr(o,Vx,{name:e.name,value:r})})),a=[Tr(Fs(),s(((e,t,o)=>{hl(o)}))),Tr(Ns(),r),Tr(Qs(),r),Tr(Is(),s(((e,t,o)=>{Za(e.element,"."+Yf).each((e=>{_a(e,Yf)})),Oa(o,Yf)}))),Tr(Vs(),s((e=>{Za(e.element,"."+Yf).each((e=>{_a(e,Yf)}))}))),Hr(s(((t,o,n,s)=>{yr(t,Vx,{name:e.name,value:s})})))],i=(e,t)=>P(Mc(e.element,".tox-collection__item"),t),l=kx.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:x},behaviours:il([bm.config({disabled:t.isDisabled,onDisabled:e=>{i(e,(e=>{Oa(e,"tox-collection__item--state-disabled"),pt(e,"aria-disabled",!0)}))},onEnabled:e=>{i(e,(e=>{_a(e,"tox-collection__item--state-disabled"),yt(e,"aria-disabled")}))}}),Fv(),Ap.config({}),Yd.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const s=P(n,(o=>{const n=Ch.translate(o.text),s=1===e.columns?`<div class="tox-collection__item-label">${n}</div>`:"",r=`<div class="tox-collection__item-icon">${o.icon}</div>`,a={_:" "," - ":" ","-":" "},i=n.replace(/\_| \- |\-/g,(e=>a[e]));return`<div class="tox-collection__item${t.isDisabled()?" tox-collection__item--state-disabled":""}" tabindex="-1" data-collection-item-value="${Tx.encodeAllRaw(o.value)}" title="${i}" aria-label="${i}">${r}${s}</div>`})),r="auto"!==e.columns&&e.columns>1?H(s,e.columns):[s],a=P(r,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));Nr(o.element,a.join(""))})(o,n),"auto"===e.columns&&yv(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{Sp.setGridSize(o,e,t)})),vr(o,Nx)}}),_x.config({}),Sp.config((c=e.columns,1===c?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===c?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${jf}`}})),Dp("collection-events",a)]),eventOrder:{[Js()]:["disabling","alloy.base.behaviour","collection-events"]}});var c;return Ex(n,l,["tox-form__group--collection"],[])})(e,t.shared.providers,o))),alertbanner:JC(((e,t)=>((e,t)=>yx.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[Sh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:Mh(e.icon,t.icons),attributes:{title:t.translate(e.iconTooltip)}},action:t=>{yr(t,Vx,{name:"alert-banner",value:e.url})},buttonBehaviours:il([Ah()])})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]}))(e,t.shared.providers))),input:JC(((e,t,o)=>((e,t,o)=>pC({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:B.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),textarea:JC(((e,t,o)=>((e,t,o)=>pC({name:e.name,multiline:!0,label:e.label,inputMode:B.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:B.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),label:JC(((e,t)=>((e,t)=>{return{dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"label",classes:["tox-label"]},components:[Pa(t.providers.translate(e.label))]},...P(e.items,t.interpreter)],behaviours:il([Ck(),Ap.config({}),(o=B.none(),Ek(o,Pr,Nr)),Sp.config({mode:"acyclic"})])};var o})(e,t.shared))),iframe:(k_=(e,t,o)=>((e,t,o)=>{const n=e.sandboxed,s={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...n?{sandbox:"allow-scripts allow-same-origin"}:{}},r=(e=>{const t=ps(e.getOr(""));return{getValue:e=>t.get(),setValue:(e,o)=>{t.get()!==o&&pt(e.element,"srcdoc",o),t.set(o)}}})(o),a=e.label.map((e=>Ax(e,t))),i=kx.parts.field({factory:{sketch:e=>Pk({uid:e.uid,dom:{tag:"iframe",attributes:s},behaviours:il([_x.config({}),zp.config({}),Ak(o,r.getValue,r.setValue)])})}});return Ex(a,i,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n)=>{const s=nn(t,{source:"dynamic"});return JC(k_)(e,s,o,n)}),button:JC(((e,t)=>((e,t)=>{const o=zC(e.name,"custom");return n=B.none(),s=kx.parts.field({factory:Sh,...VC(e,B.some(o),t,[Dk(""),Ck()])}),Ex(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:JC(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),B.some(!0)),s=kx.parts.field({factory:{sketch:x},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:il([Ck(),bm.config({disabled:()=>!e.enabled||t.isDisabled()}),_x.config({}),zp.config({}),Mk(o,YC,KC),Sp.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),Dp("checkbox-events",[Tr(Ps(),((t,o)=>{yr(t,Dx,{name:e.name})}))])])}),r=kx.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[Pa(t.translate(e.label))],behaviours:il([Tw.config({})])}),a=e=>Fh("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=kh({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return kx.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:il([bm.config({disabled:()=>!e.enabled||t.isDisabled(),disableClass:"tox-checkbox--disabled",onDisabled:e=>{kx.getField(e).each(bm.disable)},onEnabled:e=>{kx.getField(e).each(bm.enable)}}),Fv()])})})(e,t.shared.providers,o))),colorinput:JC(((e,t,o)=>((e,t,o,n)=>{const s=kx.parts.field({factory:Gx,inputClasses:["tox-textfield"],data:n,onSetValue:e=>iw.run(e).get(b),inputBehaviours:il([bm.config({disabled:t.providers.isDisabled}),Fv(),_x.config({}),iw.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>Je(e.element),notify:{onValid:e=>{const t=Yd.getValue(e);yr(e,Ew,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=Yd.getValue(e);if(0===t.length)return Jx($o.value(!0));{const e=Ae("span");St(e,"background-color",t);const o=Tt(e,"background-color").fold((()=>$o.error("blah")),(e=>$o.value(t)));return Jx(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>Ax(e,t.providers))),a=(e,t)=>{yr(e,Bw,{value:t})},i=kh(((e,t)=>_w.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:il([Iv(t.providers.isDisabled),Fv(),Tw.config({}),_x.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>Yx((t=>e.fetch(t))).map((n=>B.from(ix(nn(qy(qr("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,Wf.CLOSE_ON_EXECUTE,_,t.providers),{movement:Ky(e.columns,e.presets)}))))),parts:{menu:rb(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[Ui,Wi,qi],onLtr:()=>[Wi,Ui,qi]},components:[],fetch:Wy(o.getColors(),o.hasCustomColors()),columns:o.getColorCols(),presets:"color",onItemAction:(e,t)=>{i.getOpt(e).each((e=>{"custom"===t?o.colorPicker((t=>{t.fold((()=>vr(e,Mw)),(t=>{a(e,t),zy(t)}))}),"#ffffff"):a(e,"remove"===t?"":t)}))}},t));return kx.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:il([Dp("form-field-events",[Tr(Ew,((t,o)=>{i.getOpt(t).each((e=>{St(e.element,"background-color",o.event.color)})),yr(t,Dx,{name:e.name})})),Tr(Bw,((e,t)=>{kx.getField(e).each((o=>{Yd.setValue(o,t.event.value),nm.getCurrent(e).each(zp.focus)}))})),Tr(Mw,((e,t)=>{kx.getField(e).each((t=>{nm.getCurrent(e).each(zp.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:JC(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=kk((e=>t=>e.translate(Fk[t]))(t),n),r=kh(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{yr(e,Vx,{name:"hex-valid",value:!0})},onInvalidHex:e=>{yr(e,Vx,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:il([Ak(o,(e=>{const t=r.get(e);return nm.getCurrent(t).bind((e=>Yd.getValue(e).hex)).map((e=>"#"+e)).getOr("")}),((e,t)=>{const o=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t),n=r.get(e);nm.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{Yd.setValue(e,{hex:B.from(o[1]).getOr("")}),bk.getField(e,"hex").each((e=>{vr(e,Hs())}))}))})),Ck()])}})(0,t.shared.providers,o))),dropzone:JC(((e,t,o)=>((e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{N(e,(e=>{e(t,o)}))},r=(e,t)=>{if(!bm.isDisabled(e)){const o=t.event.raw;i(e,o.dataTransfer.files)}},a=(e,t)=>{const o=t.event.raw.target;i(e,o.files)},i=(o,n)=>{Yd.setValue(o,((e,t)=>{const o=Vk.explode(t.getOption("images_file_types"));return W(se(e),(e=>R(o,(t=>Oe(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(n,t)),yr(o,Dx,{name:e.name})},l=kh({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:il([Dp("input-file-events",[Dr(Ns()),Dr(Qs())])])}),c=e.label.map((e=>Ax(e,t))),d=kx.parts.field({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:il([Dk(o.getOr([])),Ck(),bm.config({}),Kp.config({toggleClass:"dragenter",toggleOnExecute:!1}),Dp("dropzone-events",[Tr("dragenter",s([n,Kp.toggle])),Tr("dragleave",s([n,Kp.toggle])),Tr("dragover",n),Tr("drop",s([n,r])),Tr(Ps(),a)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[Pa(t.translate("Drop an image here"))]},Sh.sketch({dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[Pa(t.translate("Browse for an image")),l.asSpec()],action:e=>{l.get(e).element.dom.click()},buttonBehaviours:il([_x.config({}),Iv(t.isDisabled),Fv()])})]}]})}});return Ex(c,d,["tox-form__group--stretched"],[])})(e,t.shared.providers,o))),grid:JC(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:P(e.items,t.interpreter)}))(e,t.shared))),listbox:JC(((e,t,o)=>((e,t,o)=>{const n=t.shared.providers,s=o.bind((t=>rC(e.items,t))).orThunk((()=>oe(e.items).filter(oC))),r=e.label.map((e=>Ax(e,n))),a=kx.parts.field({dom:{},factory:{sketch:o=>Jk({uid:o.uid,text:s.map((e=>e.text)),icon:B.none(),tooltip:e.label,role:B.none(),fetch:(o,n)=>{const s=sC(o,e.name,e.items,Yd.getValue(o));n(tC(s,Wf.CLOSE_ON_EXECUTE,t,!1))},onSetup:y(b),getApi:y({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[_x.config({}),Ak(s.map((e=>e.value)),(e=>ft(e.element,nC)),((t,o)=>{rC(e.items,o).each((e=>{pt(t.element,nC,e.value),yr(t,Kk,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),i={dom:{tag:"div",classes:["tox-listboxfield"]},components:[a]};return kx.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([r.toArray(),[i]]),fieldBehaviours:il([bm.config({disabled:y(!e.enabled),onDisabled:e=>{kx.getField(e).each(bm.disable)},onEnabled:e=>{kx.getField(e).each(bm.enable)}})])})})(e,t,o))),selectbox:JC(((e,t,o)=>((e,t,o)=>{const n=P(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>Ax(e,t))),r=kx.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:aC,selectBehaviours:il([bm.config({disabled:()=>!e.enabled||t.isDisabled()}),_x.config({}),Dp("selectbox-change",[Tr(Ps(),((t,o)=>{yr(t,Dx,{name:e.name})}))])])}),a=e.size>1?B.none():B.some(Fh("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:q([[r],a.toArray()])};return kx.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([s.toArray(),[i]]),fieldBehaviours:il([bm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{kx.getField(e).each(bm.disable)},onEnabled:e=>{kx.getField(e).each(bm.enable)}}),Fv()])})})(e,t.shared.providers,o))),sizeinput:JC(((e,t)=>((e,t)=>{let o=gC;const n=qr("ratio-event"),s=e=>Fh(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=dC.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:t.translate(e.label.getOr("Constrain proportions"))}},components:[s("lock"),s("unlock")],buttonBehaviours:il([bm.config({disabled:()=>!e.enabled||t.isDisabled()}),Fv(),_x.config({})])}),a=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),i=o=>kx.parts.field({factory:Gx,inputClasses:["tox-textfield"],inputBehaviours:il([bm.config({disabled:()=>!e.enabled||t.isDisabled()}),Fv(),_x.config({}),Dp("size-input-events",[Tr(Is(),((e,t)=>{yr(e,n,{isField1:o})})),Tr(Ps(),((t,o)=>{yr(t,Dx,{name:e.name})}))])]),selectOnFocus:!1}),l=e=>({dom:{tag:"label",classes:["tox-label"]},components:[Pa(t.translate(e))]}),c=dC.parts.field1(a([kx.parts.label(l("Width")),i(!0)])),d=dC.parts.field2(a([kx.parts.label(l("Height")),i(!1)]));return dC.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,d,a([l("\xa0"),r])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{uC(Yd.getValue(e)).each((e=>{o(e).each((e=>{Yd.setValue(t,(e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit})(e))}))}))},coupledFieldBehaviours:il([bm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{dC.getField1(e).bind(kx.getField).each(bm.disable),dC.getField2(e).bind(kx.getField).each(bm.disable),dC.getLock(e).each(bm.disable)},onEnabled:e=>{dC.getField1(e).bind(kx.getField).each(bm.enable),dC.getField2(e).bind(kx.getField).each(bm.enable),dC.getLock(e).each(bm.enable)}}),Fv(),Dp("size-input-events2",[Tr(n,((e,t)=>{const n=t.event.isField1,s=n?dC.getField1(e):dC.getField2(e),r=n?dC.getField2(e):dC.getField1(e),a=s.map(Yd.getValue).getOr(""),i=r.map(Yd.getValue).getOr("");o=((e,t)=>{const o=uC(e).toOptional(),n=uC(t).toOptional();return we(o,n,((e,t)=>mC(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>mC(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(gC))).getOr(gC)})(a,i)}))])])})})(e,t.shared.providers))),slider:JC(((e,t,o)=>((e,t,o)=>{const n=ck.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Pa(t.translate(e.label))]}),s=ck.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=ck.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return ck.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:y(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:il([Ck(),zp.config({})]),onChoose:(t,o,n)=>{yr(t,Dx,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:JC(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=Yd.getValue(t);o.addToHistory(n.value,e.filetype)},a=kx.parts.field({factory:TC,...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":XC,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{const n=Yd.getValue(t),s=void 0!==n.meta.text?n.meta.text:n.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=qC(s,(e=>P(e,(e=>LC(e,e))))(o.getHistory(e)));return"file"===e?(r=[n,qC(s,UC(t)),qC(s,q([GC(t),jC(t),$C(t)]))],j(r,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(PC,t)),[])):n;var r}))})(e.filetype,n,o),r=tC(s,Wf.BUBBLE_TO_SANDBOX,t,!1);return Jx(r)},getHotspot:e=>m.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(iw)&&iw.run(e).get(b)},typeaheadBehaviours:il(q([o.getValidationHandler().map((t=>iw.config({getRoot:e=>Je(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{l.getOpt(e).each((e=>{pt(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=Yd.getValue(o);return BC((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=$o.error(e.message);o(t)}else{const t=$o.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),[bm.config({disabled:()=>!e.enabled||s.isDisabled()}),_x.config({}),Dp("urlinput-events",q(["file"===e.filetype?[Tr(Hs(),(t=>{yr(t,Dx,{name:e.name})}))]:[],[Tr(Ps(),(t=>{yr(t,Dx,{name:e.name}),r(t)})),Tr(Ks(),(t=>{yr(t,Dx,{name:e.name}),r(t)}))]]))]])),eventOrder:{[Hs()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:rb(0,0,"normal")},onExecute:(e,t,o)=>{yr(t,Rx,{})},onItemExecute:(t,o,n,s)=>{r(t),yr(t,Dx,{name:e.name})}}),i=e.label.map((e=>Ax(e,s))),l=kh(((e,t,o=e,n=e)=>Fh(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",B.some(XC),"warning")),c=kh({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[l.asSpec()]}),d=o.getUrlPicker(e.filetype),u=qr("browser.url.event"),m=kh({dom:{tag:"div",classes:["tox-control-wrap"]},components:[a,c.asSpec()],behaviours:il([bm.config({disabled:()=>!e.enabled||s.isDisabled()})])}),g=kh(RC({name:e.name,icon:B.some("browse"),text:e.label.getOr(""),enabled:e.enabled,primary:!1,buttonType:B.none(),borderless:!0},(e=>vr(e,u)),s,[],["tox-browse-url"]));return kx.sketch({dom:Mx([]),components:i.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:q([[m.asSpec()],d.map((()=>g.asSpec())).toArray()])}]),fieldBehaviours:il([bm.config({disabled:()=>!e.enabled||s.isDisabled(),onDisabled:e=>{kx.getField(e).each(bm.disable),g.getOpt(e).each(bm.disable)},onEnabled:e=>{kx.getField(e).each(bm.enable),g.getOpt(e).each(bm.enable)}}),Fv(),Dp("url-input-events",[Tr(u,(t=>{nm.getCurrent(t).each((o=>{const n=Yd.getValue(o),s={fieldname:e.name,...n};d.each((n=>{n(s).get((n=>{Yd.setValue(o,n),yr(t,Dx,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:JC((e=>{const t=Rl(),o=kh({dom:{tag:e.tag}}),n=Rl();return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:il([Dp("custom-editor-events",[Vr((s=>{o.getOpt(s).each((o=>{((e=>be(e,"init"))(e)?e.init(o.element.dom):Ik.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),Ak(B.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),Ck()]),components:[o.asSpec()]}})),htmlpanel:JC((e=>"presentation"===e.presets?yx.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html}}):yx.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:il([_x.config({}),zp.config({})])}))),imagepreview:JC(((e,t,o)=>((e,t)=>{const o=ps(t.getOr({url:""})),n=kh({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=kh({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:B.from(e.zoom),cachedWidth:B.from(e.cachedWidth),cachedHeight:B.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:il([Ck(),Ak(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const t=r.cachedWidth,o=r.cachedHeight;if(u(r.zoom)){const n=((e,t,o)=>{const n=Wt(e),s=It(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const n=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})(Wt(e.element),It(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{kt(e.element,n)}))};n.getOpt(e).each((o=>{const n=o.element;var s;t.url!==ft(n,"src")&&(pt(n,"src",t.url),_a(e.element,"tox-imagepreview__loaded")),u(r.cachedWidth)||u(r.cachedHeight)||a(),(s=n,new Promise(((e,t)=>{const o=()=>{r(),e(s)},n=[Hl(s,"load",o),Hl(s,"error",(()=>{r(),t("Unable to load data from image: "+s.dom.src)}))],r=()=>N(n,(e=>e.unbind()));s.dom.complete&&o()}))).then((t=>{e.getSystem().isConnected()&&(Oa(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:JC(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:P(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:P(n,(e=>({dom:{tag:"tr"},components:P(e,o)})))})],behaviours:il([_x.config({}),zp.config({})])};var n,s})(e,t.shared.providers))),panel:JC(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:P(e.items,t.shared.interpreter)}))(e,t)))},QC={field:(e,t)=>t,record:y([])},eO=(e,t,o,n)=>{const s=nn(n,{shared:{interpreter:t=>tO(e,t,o,s)}});return tO(e,t,o,s)},tO=(e,t,o,n)=>fe(ZC,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(s=>s(e,t,o,n))),oO="layout-inset",nO=e=>e.x,sO=(e,t)=>e.x+e.width/2-t.width/2,rO=(e,t)=>e.x+e.width-t.width,aO=e=>e.y,iO=(e,t)=>e.y+e.height-t.height,lO=(e,t)=>e.y+e.height/2-t.height/2,cO=(e,t,o)=>Si(rO(e,t),iO(e,t),o.insetSouthwest(),Ti(),"southwest",Ii(e,{right:0,bottom:3}),oO),dO=(e,t,o)=>Si(nO(e),iO(e,t),o.insetSoutheast(),_i(),"southeast",Ii(e,{left:1,bottom:3}),oO),uO=(e,t,o)=>Si(rO(e,t),aO(e),o.insetNorthwest(),Oi(),"northwest",Ii(e,{right:0,top:2}),oO),mO=(e,t,o)=>Si(nO(e),aO(e),o.insetNortheast(),Ci(),"northeast",Ii(e,{left:1,top:2}),oO),gO=(e,t,o)=>Si(sO(e,t),aO(e),o.insetNorth(),Ei(),"north",Ii(e,{top:2}),oO),pO=(e,t,o)=>Si(sO(e,t),iO(e,t),o.insetSouth(),Bi(),"south",Ii(e,{bottom:3}),oO),hO=(e,t,o)=>Si(rO(e,t),lO(e,t),o.insetEast(),Ai(),"east",Ii(e,{right:0}),oO),fO=(e,t,o)=>Si(nO(e),lO(e,t),o.insetWest(),Mi(),"west",Ii(e,{left:1}),oO),bO=e=>{switch(e){case"north":return gO;case"northeast":return mO;case"northwest":return uO;case"south":return pO;case"southeast":return dO;case"southwest":return cO;case"east":return hO;case"west":return fO}},vO=(e,t,o,n,s)=>Al(n).map(bO).getOr(gO)(e,t,o,n,s),yO=e=>{switch(e){case"north":return pO;case"northeast":return dO;case"northwest":return cO;case"south":return gO;case"southeast":return mO;case"southwest":return uO;case"east":return fO;case"west":return hO}},xO=(e,t,o,n,s)=>Al(n).map(yO).getOr(gO)(e,t,o,n,s),wO={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},SO=(e,t,o)=>{const n={maxHeightFunction:$l()};return()=>o()?{type:"node",root:it(e()),node:B.from(e()),bubble:Yl(12,12,wO),layouts:{onRtl:()=>[mO],onLtr:()=>[uO]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:Yl(-12,12,wO),layouts:{onRtl:()=>[Wi],onLtr:()=>[Ui]},overrides:n}},kO=(e,t,o)=>()=>o()?{type:"node",root:it(e()),node:B.from(e()),layouts:{onRtl:()=>[gO],onLtr:()=>[gO]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[qi],onLtr:()=>[qi]}},CO=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng();return B.some(kc.range(Fe(t.startContainer),t.startOffset,Fe(t.endContainer),t.endOffset))}}),OO=e=>t=>({type:"node",root:e(),node:t}),_O=(e,t,o)=>{const n=Ff(e),s=()=>Fe(e.getBody()),r=()=>Fe(e.getContentAreaContainer()),a=()=>n||!o();return{inlineDialog:SO(r,t,a),banner:kO(r,t,a),cursor:CO(e,s),node:OO(s)}},TO=e=>(t,o)=>{$y(e)(t,o)},EO=e=>()=>Vy(e),BO=e=>()=>Ry(e),MO=e=>()=>Iy(e),AO=e=>({colorPicker:TO(e),hasCustomColors:EO(e),getColors:BO(e),getColorCols:MO(e)}),DO=e=>()=>vf(e),FO=e=>({isDraggableModal:DO(e)}),IO=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],VO=e=>j(e,((e,t)=>{if(be(t,"items")){const o=VO(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(be(t,"inline")||(e=>be(e,"block"))(t)||(e=>be(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),RO=e=>Jh(e).map((t=>{const o=((e,t)=>{const o=VO(t),n=t=>{N(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return Zh(e)?IO.concat(o):o})).getOr(IO),zO=(e,t,o)=>{const n={type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)};return nn(e,n)},HO=(e,t,o,n)=>{const s=t=>P(t,(t=>{const a=ae(t);if(ve(t,"items")){const e=s(t.items);return nn((e=>nn(e,{type:"submenu"}))(t),{getStyleItems:y(e)})}return ve(t,"format")?(e=>zO(e,o,n))(t):1===a.length&&V(a,"title")?nn(t,{type:"separator"}):(t=>{const s=r(t.name)?t.name:qr(t.title),a=`custom-${s}`,i={type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)},l=nn(t,i);return e.formatter.register(s,l),l})(t)}));return s(t)},PO=e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?B.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):B.none()},n=e=>{const t=e.items;return void 0!==t&&t.length>0?X(t,n):[e.format]},s=ps([]),r=ps([]),a=ps([]),i=ps([]),l=ps(!1);return e.on("PreInit",(a=>{const i=RO(e),l=HO(e,i,t,o);s.set(l),r.set(X(l,n))})),e.on("addStyleModifications",(s=>{const r=HO(e,s.items,t,o);a.set(r),l.set(s.replace),i.set(X(r,n))})),{getData:()=>{const e=l.get()?[]:s.get(),t=a.get();return e.concat(t)},getFlattenedKeys:()=>{const e=l.get()?[]:r.get(),t=i.get();return e.concat(t)}}},NO=Vk.trim,LO=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},WO=LO("true"),UO=LO("false"),jO=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),GO=e=>e.innerText||e.textContent,$O=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&XO(e),qO=e=>e&&/^(H[1-6])$/.test(e.nodeName),XO=e=>(e=>{for(;e=e.parentNode;){const t=e.contentEditable;if(t&&"inherit"!==t)return WO(e)}return!1})(e)&&!UO(e),KO=e=>qO(e)&&XO(e),YO=e=>{const t=(e=>e.id?e.id:qr("h"))(e);return jO("header",GO(e),"#"+t,(e=>qO(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=t}))},JO=e=>{const t=e.id||e.name,o=GO(e);return jO("anchor",o||"#"+t,"#"+t,0,b)},ZO=e=>NO(e.title).length>0,QO=e=>{const t=(e=>{const t=P(Mc(Fe(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return W((e=>P(W(e,KO),YO))(t).concat((e=>P(W(e,$O),JO))(t)),ZO)},e_="tinymce-url-history",t_=e=>r(e)&&/^https?/.test(e),o_=e=>a(e)&&pe(e,(e=>{return!(l(t=e)&&t.length<=5&&K(t,t_));var t})).isNone(),n_=()=>{const e=By.getItem(e_);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+e_+" was not valid JSON",e),{};throw e}return o_(t)?t:(console.log("Local storage "+e_+" was not valid format",t),{})},s_=e=>{const t=n_();return fe(t,e).getOr([])},r_=(e,t)=>{if(!t_(e))return;const o=n_(),n=fe(o,t).getOr([]),s=W(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!o_(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));By.setItem(e_,JSON.stringify(e))})(o)},a_=e=>!!e,i_=e=>ce(Vk.makeMap(e,/[, ]/),a_),l_=e=>B.from(mf(e)),c_=e=>B.from(e).filter(r).getOrUndefined(),d_=e=>({getHistory:s_,addToHistory:r_,getLinkInformation:()=>(e=>hf(e)?B.some({targets:QO(e.getBody()),anchorTop:c_(ff(e)),anchorBottom:c_(bf(e))}):B.none())(e),getValidationHandler:()=>(e=>B.from(gf(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=B.from(pf(e)).filter(a_).map(i_);return l_(e).fold(_,(e=>t.fold(T,(e=>ae(e).length>0&&e))))})(e);return d(o)?o?l_(e):B.none():o[t]?l_(e):B.none()})(e,t).map((o=>n=>Yx((s=>{const i={filetype:t,fieldname:n.fieldname,...B.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),u_=$u,m_=Bu,g_=y([rs("shell",!1),Un("makeItem"),rs("setupItem",b),eu("listBehaviours",[Ap])]),p_=_u({name:"items",overrides:()=>({behaviours:il([Ap.config({})])})}),h_=y([p_]),f_=Qu({name:y("CustomList")(),configFields:g_(),partFields:h_(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Ap.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Qd(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?B.some(n):Hu(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=Ap.contents(n),r=o.length,a=r-s.length,i=a>0?z(a,(()=>e.makeItem())):[],l=s.slice(r);N(l,(e=>Ap.remove(n,e))),N(i,(e=>Ap.append(n,e)));const c=Ap.contents(n);N(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),b_=y([Un("dom"),rs("shell",!0),Jd("toolbarBehaviours",[Ap])]),v_=y([_u({name:"groups",overrides:()=>({behaviours:il([Ap.config({})])})})]),y_=Qu({name:"Toolbar",configFields:b_(),partFields:v_(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Ap.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Qd(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?B.some(n):Hu(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{Ap.set(e,o)}))}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),x_=b,w_=_,S_=y([]);var k_,C_=Object.freeze({__proto__:null,setup:x_,isDocked:w_,getBehaviours:S_});const O_=e=>(ye(Tt(e,"position"),"fixed")?B.none():Ze(e)).orThunk((()=>{const t=Ae("span");return Ye(e).bind((e=>{Eo(e,t);const o=Ze(t);return Ao(t),o}))})),__=e=>O_(e).map(Pt).getOrThunk((()=>zt(0,0))),T_=hs([{static:[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),E_=(e,t)=>{const o=e.element;Oa(o,t.transitionClass),_a(o,t.fadeOutClass),Oa(o,t.fadeInClass),t.onShow(e)},B_=(e,t)=>{const o=e.element;Oa(o,t.transitionClass),_a(o,t.fadeInClass),Oa(o,t.fadeOutClass),t.onHide(e)},M_=(e,t,o)=>K(e,(e=>{switch(e){case"bottom":return((e,t)=>e.bottom<=t.bottom)(t,o);case"top":return((e,t)=>e.y>=t.y)(t,o)}})),A_=(e,t)=>t.getInitialPos().map((t=>No(t.bounds.x,t.bounds.y,Wt(e),It(e)))),D_=(e,t,o)=>o.getInitialPos().bind((n=>{switch(o.clearInitialPos(),n.position){case"static":return B.some(T_.static());case"absolute":const o=O_(e).map(Lo).getOrThunk((()=>Lo(ut())));return B.some(T_.absolute(xl("absolute",fe(n.style,"left").map((e=>t.x-o.x)),fe(n.style,"top").map((e=>t.y-o.y)),fe(n.style,"right").map((e=>o.right-t.right)),fe(n.style,"bottom").map((e=>o.bottom-t.bottom)))));default:return B.none()}})),F_=(e,t,o)=>{const n=e.element;return ye(Tt(n,"position"),"fixed")?((e,t,o)=>A_(e,o).filter((e=>M_(o.getModes(),e,t))).bind((t=>D_(e,t,o))))(n,t,o):((e,t,o)=>{const n=Lo(e);if(M_(o.getModes(),n,t))return B.none();{((e,t,o)=>{o.setInitialPos({style:Et(e),position:Ot(e,"position")||"static",bounds:t})})(e,n,o);const s=Uo(),r=n.x-s.x,a=t.y-s.y,i=s.bottom-t.bottom,l=n.y<=t.y;return B.some(T_.fixed(xl("fixed",B.some(r),l?B.some(a):B.none(),B.none(),l?B.none():B.some(i))))}})(n,t,o)},I_=(e,t,o)=>{o.setDocked(!1),N(["left","right","top","bottom","position"],(t=>Mt(e.element,t))),t.onUndocked(e)},V_=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),wl(e.element,n),(s?t.onDocked:t.onUndocked)(e)},R_=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(Ea(e.element,[t.fadeOutClass]),t.onHide(e)):(a?E_:B_)(e,t))}))}))},z_=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);o.isDocked()&&R_(e,t,o,n),F_(e,n,o).each((s=>{s.fold((()=>I_(e,t,o)),(n=>V_(e,t,o,n)),(s=>{R_(e,t,o,n,!0),V_(e,t,o,s)}))}))})(e,t,o)},H_=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1),((e,t)=>{const o=e.element;return A_(o,t).bind((e=>D_(o,e,t)))})(e,o).each((n=>{n.fold((()=>I_(e,t,o)),(n=>V_(e,t,o,n)),b)})),o.setVisible(!0),t.contextual.each((t=>{Ba(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),z_(e,t,o)})(e,t,o)};var P_=Object.freeze({__proto__:null,refresh:z_,reset:H_,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n)}),N_=Object.freeze({__proto__:null,events:(e,t)=>Cr([Ir(Ws(),((o,n)=>{e.contextual.each((e=>{Ta(o.element,e.transitionClass)&&(Ba(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),Tr(ar(),((o,n)=>{z_(o,e,t)})),Tr(ir(),((o,n)=>{H_(o,e,t)}))])}),L_=[ss("contextual",[$n("fadeInClass"),$n("fadeOutClass"),$n("transitionClass"),Xn("lazyContext"),hi("onShow"),hi("onShown"),hi("onHide"),hi("onHidden")]),us("lazyViewport",Uo),ms("modes",["top","bottom"],Tn),hi("onDocked"),hi("onUndocked")];const W_=cl({fields:L_,name:"docking",active:N_,apis:P_,state:Object.freeze({__proto__:null,init:e=>{const t=ps(!1),o=ps(!0),n=Rl(),s=ps(e.modes);return ua({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),U_=y(qr("toolbar-height-change")),j_={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},G_="tox-tinymce--toolbar-sticky-on",$_="tox-tinymce--toolbar-sticky-off",q_=(e,t)=>V(W_.getModes(e),t),X_=e=>{const t=e.element;Ye(t).each((o=>{const n="padding-"+W_.getModes(e)[0];if(W_.isDocked(e)){const e=Wt(o);St(t,"width",e+"px"),St(o,n,(e=>Vt(e)+(parseInt(Ot(e,"margin-top"),10)||0)+(parseInt(Ot(e,"margin-bottom"),10)||0))(t)+"px")}else Mt(t,"width"),Mt(o,n)}))},K_=(e,t)=>{t?(_a(e,j_.fadeOutClass),Ea(e,[j_.transitionClass,j_.fadeInClass])):(_a(e,j_.fadeInClass),Ea(e,[j_.fadeOutClass,j_.transitionClass]))},Y_=(e,t)=>{const o=Fe(e.getContainer());t?(Oa(o,G_),_a(o,$_)):(Oa(o,$_),_a(o,G_))},J_=(e,t)=>{const o=Rl(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||X_(t),Y_(e,W_.isDocked(t)),t.getSystem().broadcastOn([Dd()],{}),n().each((e=>e.getSystem().broadcastOn([Dd()],{})))},a=e.inline?[]:[gl.config({channels:{[U_()]:{onReceive:X_}}})];return[zp.config({}),W_.config({contextual:{lazyContext:t=>{const o=Vt(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer(),s=Lo(Fe(n)),r=s.height-o,a=s.y+(q_(t,"top")?0:o);return B.some(No(s.x,a,s.width,r))},onShow:()=>{s((e=>K_(e,!0)))},onShown:e=>{s((e=>Ba(e,[j_.transitionClass,j_.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=$e(t);bl(o).filter((e=>!je(t,e))).filter((t=>je(t,Fe(o.dom.body))||Ge(e,t))).each((()=>hl(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>vl(e).orThunk((()=>t().toOptional().bind((e=>vl(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>K_(e,!1)))},onHidden:()=>{s((e=>Ba(e,[j_.transitionClass])))},...j_},lazyViewport:t=>{const o=Uo(),n=cf(e),s=o.y+(q_(t,"top")?n:0),r=o.height-(q_(t,"bottom")?n:0);return No(o.x,s,o.width,r)},modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var Z_=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each(W_.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(X_)})),e.on("SkinLoaded",(()=>{o().each((e=>{W_.isDocked(e)?W_.reset(e):W_.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each(W_.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{W_.refresh(t);const o=t.element;fg(o)&&((e,t)=>{const o=$e(t),n=o.dom.defaultView.innerHeight,s=Do(o),r=Fe(e.elm),a=Wo(r),i=It(r),l=a.y,c=l+i,d=Pt(t),u=It(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)Fo(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;Fo(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{Y_(e,!1)}))},isDocked:e=>e().map(W_.isDocked).getOr(!1),getBehaviours:J_});const Q_=yn([gb,jn("items",wn([kn([pb,Jn("items",Tn)]),Tn]))].concat(Ub)),eT=[ts("text"),ts("tooltip"),ts("icon"),Xn("fetch"),us("onSetup",(()=>b))],tT=yn([gb,...eT]),oT=e=>Vn("menubutton",tT,e),nT=yn([gb,Eb,Tb,_b,Ab,xb,Cb,cs("presets","normal",["normal","color","listpreview"]),Rb(1),Sb,kb]);var sT=Zu({factory:(e,t)=>{const o={focus:Sp.focusIn,setMenus:(e,o)=>{const n=P(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())}},n=oT(o).mapError((e=>Hn(e))).getOrDie();return AC(n,"tox-mbtn",t.backstage,B.some("menuitem"))}));Ap.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:il([Ap.config({}),Dp("menubar-events",[Vr((t=>{e.onSetup(t)})),Tr(Fs(),((e,t)=>{Za(e.element,".tox-mbtn--active").each((o=>{Qa(t.event.target,".tox-mbtn").each((t=>{je(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{_w.expand(e),_w.close(o),zp.focus(e)}))}))}))}))})),Tr(mr(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{_w.isOpen(o)&&(_w.expand(e),_w.close(o))}))}))}))]),Sp.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),B.some(!0))}),_x.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[Un("dom"),Un("uid"),Un("onEscape"),Un("backstage"),rs("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const rT=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),aT=e=>e.dimension.property,iT=(e,t)=>e.dimension.getDimension(t),lT=(e,t)=>{const o=rT(e,t);Ba(o,[t.shrinkingClass,t.growingClass])},cT=(e,t)=>{_a(e.element,t.openClass),Oa(e.element,t.closedClass),St(e.element,aT(t),"0px"),At(e.element)},dT=(e,t)=>{_a(e.element,t.closedClass),Oa(e.element,t.openClass),Mt(e.element,aT(t))},uT=(e,t,o,n)=>{o.setCollapsed(),St(e.element,aT(t),iT(t,e.element)),At(e.element),lT(e,t),cT(e,t),t.onStartShrink(e),t.onShrunk(e)},mT=(e,t,o,n)=>{const s=n.getOrThunk((()=>iT(t,e.element)));o.setCollapsed(),St(e.element,aT(t),s),At(e.element);const r=rT(e,t);_a(r,t.growingClass),Oa(r,t.shrinkingClass),cT(e,t),t.onStartShrink(e)},gT=(e,t,o)=>{const n=iT(t,e.element);("0px"===n?uT:mT)(e,t,o,B.some(n))},pT=(e,t,o)=>{const n=rT(e,t),s=Ta(n,t.shrinkingClass),r=iT(t,e.element);dT(e,t);const a=iT(t,e.element);(s?()=>{St(e.element,aT(t),r),At(e.element)}:()=>{cT(e,t)})(),_a(n,t.shrinkingClass),Oa(n,t.growingClass),dT(e,t),St(e.element,aT(t),a),o.setExpanded(),t.onStartGrow(e)},hT=(e,t,o)=>{const n=rT(e,t);return!0===Ta(n,t.growingClass)},fT=(e,t,o)=>{const n=rT(e,t);return!0===Ta(n,t.shrinkingClass)};var bT=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){Mt(e.element,aT(t));const o=iT(t,e.element);St(e.element,aT(t),o)}},grow:(e,t,o)=>{o.isExpanded()||pT(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&gT(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&uT(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:hT,isShrinking:fT,isTransitioning:(e,t,o)=>hT(e,t)||fT(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?gT:pT)(e,t,o)},disableTransitions:lT}),vT=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return ga(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:fs(t.dimension.property,"0px")})},events:(e,t)=>Cr([Ir(Ws(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(lT(o,e),t.isExpanded()&&Mt(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),yT=[Un("closedClass"),Un("openClass"),Un("shrinkingClass"),Un("growingClass"),Zn("getAnimationRoot"),hi("onShrunk"),hi("onStartShrink"),hi("onGrown"),hi("onStartGrow"),rs("expanded",!1),jn("dimension",Pn("property",{width:[yi("property","width"),yi("getDimension",(e=>Wt(e)+"px"))],height:[yi("property","height"),yi("getDimension",(e=>It(e)+"px"))]}))];const xT=cl({fields:yT,name:"sliding",active:vT,apis:bT,state:Object.freeze({__proto__:null,init:e=>{const t=ps(e.expanded);return ua({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:S(t.set,!1),setExpanded:S(t.set,!0),readState:()=>"expanded: "+t.get()})}})}),wT="container",ST=[Jd("slotBehaviours",[])],kT=e=>"<alloy.field."+e+">",CT=(e,t)=>{const o=t=>Wu(e),n=(t,o)=>(n,s)=>Hu(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==ft(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;St(o,"display","none"),pt(o,"aria-hidden","true"),yr(e,gr(),{name:t,visible:!1})}})),i=(l=a,(e,t)=>{N(t,(t=>l(e,t)))});var l;const c=n(((e,t)=>{if(!s(e)){const o=e.element;Mt(o,"display"),yt(o,"aria-hidden"),yr(e,gr(),{name:t,visible:!0})}})),d={getSlotNames:o,getSlot:(t,o)=>Hu(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:c};return{uid:e.uid,dom:e.dom,components:t,behaviours:Zd(e.slotBehaviours),apis:d}},OT=ce({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>ca(e))),_T={...OT,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),Fu(wT,kT(t),o)),record:y(e)}})(),o=e(t),n=t.record(),s=P(n,(e=>Cu({name:e,pname:kT(e)})));return Xu(wT,ST,s,CT,o)}},TT=yn([Tb,Eb,us("onShow",b),us("onHide",b),Cb]),ET=e=>({element:()=>e.element.dom}),BT=(e,t)=>{const o=P(ae(t),(e=>{const o=t[e],n=Rn((e=>Vn("sidebar",TT,e))(o));return{name:e,getApi:ET,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return P(o,(t=>{const n=ps(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:xv([Hv(t,n),Pv(t,n),Tr(gr(),((e,t)=>{const n=t.event,s=G(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},MT=e=>_T.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:BT(t,e),slotBehaviours:xv([Vr((e=>_T.hideAllSlots(e)))])}))),AT=e=>nm.getCurrent(e).bind((e=>xT.isGrowing(e)||xT.hasGrown(e)?nm.getCurrent(e).bind((e=>G(_T.getSlotNames(e),(t=>_T.isShowing(e,t))))):B.none())),DT=qr("FixSizeEvent"),FT=qr("AutoSizeEvent");var IT=Object.freeze({__proto__:null,block:(e,t,o,n)=>{pt(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=il([Sp.config({mode:"special",onTab:()=>B.some(!0),onShiftTab:()=>B.some(!0)}),zp.config({})]),a=n(s,r),i=s.getSystem().build(a);Ap.append(s,ja(i)),i.hasConfigured(Sp)&&t.focus&&Sp.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>Ap.remove(s,i)))},unblock:(e,t,o)=>{yt(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()}}),VT=[us("getRoot",B.none),ds("focus",!0),hi("onBlock"),hi("onUnblock")];const RT=cl({fields:VT,name:"blocking",apis:IT,state:Object.freeze({__proto__:null,init:()=>{const e=Il((e=>e.destroy()));return ua({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),zT=e=>{const t=Me(e),o=Qe(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return j(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:Pr(t)};return{tag:Ve(t),classes:s,attributes:n,...r}},HT=e=>nm.getCurrent(e).each((e=>hl(e.element))),PT=(e,t,o)=>{const n=ps(!1),s=Rl(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?oe(s.composedPath()):B.from(s.target)).map(Fe).filter(ze).exists((e=>Ta(e,"mce-pastebin"))))&&(o.preventDefault(),HT(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n="data-mce-tabindex";B.from(e.iframeElement).map(Fe).each((e=>{t?(bt(e,o).each((t=>pt(e,n,t))),pt(e,o,-1)):(yt(e,o),bt(e,n).each((t=>{pt(e,o,t),yt(e,n)})))}))})(e,o),o)RT.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:zT('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),Mt(s,"display"),yt(s,"aria-hidden"),e.hasFocus()&&HT(t);else{const o=nm.getCurrent(t).exists((e=>fl(e.element)));RT.unblock(t),St(s,"display","none"),pt(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),e.dispatch("AfterProgressState",{state:s}))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=wh.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},NT=(e,t,o)=>({within:e,extra:t,withinWidth:o}),LT=(e,t,o)=>{const n=j(e,((e,t)=>((e,t)=>{const n=o(e);return B.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(y(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=W(n,(e=>e.finish<=t)),r=U(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},WT=e=>P(e,(e=>e.element)),UT=(e,t)=>{const o=P(t,(e=>ja(e)));y_.setGroups(e,o)},jT=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=Pu(e,t,"primary"),r=uw.getCoupled(e,"overflowGroup");St(s.element,"visibility","hidden");const a=n.concat([r]),i=re(a,(e=>vl(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),UT(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=LT(t,e,o);return 0===n.extra.length?B.some(n):B.none()})(e,t,o).getOrThunk((()=>LT(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=WT(e.concat(t));return NT(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=WT(e).concat([o]);return NT(s,WT(t),n)})(r,a,n,i):((e,t,o)=>NT(WT(e),[],o))(r,0,i)})(Wt(s.element),t.builtGroups.get(),(e=>Wt(e.element)),r);0===l.extra.length?(Ap.remove(s,r),o([])):(UT(s,l.within),o(l.extra)),Mt(s.element,"visibility"),At(s.element),i.each(zp.focus)},GT=y([Jd("splitToolbarBehaviours",[uw]),Ln("builtGroups",(()=>ps([])))]),$T=y([gi(["overflowToggledClass"]),os("getOverflowBounds"),Un("lazySink"),Ln("overflowGroups",(()=>ps([])))].concat(GT())),qT=y([Cu({factory:y_,schema:b_(),name:"primary"}),Ou({schema:b_(),name:"overflow"}),Ou({name:"overflow-button"}),Ou({name:"overflow-group"})]),XT=y(((e,t)=>{((e,t)=>{const o=Lt.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);St(e,"max-width",o+"px")})(e,Math.floor(t))})),KT=y([gi(["toggledClass"]),Un("lazySink"),Xn("fetch"),os("getBounds"),ss("fireDismissalEventInstead",[rs("event",dr())]),sc()]),YT=y([Ou({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:il([Kp.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])})}),Ou({factory:y_,schema:b_(),name:"toolbar",overrides:e=>({toolbarBehaviours:il([Sp.config({mode:"cyclic",onEscape:t=>(Hu(t,e,"button").each(zp.focus),B.none())})])})})]),JT=(e,t)=>{const o=uw.getCoupled(e,"toolbarSandbox");Md.isOpen(o)?Md.close(o):Md.open(o,t.toolbar())},ZT=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();rd.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:XT()}}},s)},QT=(e,t,o,n,s)=>{y_.setGroups(t,s),ZT(e,t,o,n),Kp.on(e)},eE=Qu({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...Sh.sketch({...n.button(),action:e=>{JT(e,n)},buttonBehaviours:tu({dump:n.button().buttonBehaviours},[uw.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=ti();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:il([Sp.config({mode:"special",onEscape:e=>(Md.close(e),B.some(!0))}),Md.config({onOpen:(s,r)=>{o.fetch().get((s=>{QT(e,r,o,t.layouts,s),n.link(e.element),Sp.focusIn(r)}))},onClose:()=>{Kp.off(e),zp.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>oi(o,n)||oi(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),gl.config({channels:{...Vd({isExtraPart:_,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...zd({doReposition:()=>{Md.getState(uw.getCoupled(e,"toolbarSandbox")).each((n=>{ZT(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{Md.getState(uw.getCoupled(t,"toolbarSandbox")).each((s=>{QT(t,s,e,o.layouts,n)}))},reposition:t=>{Md.getState(uw.getCoupled(t,"toolbarSandbox")).each((n=>{ZT(t,n,e,o.layouts)}))},toggle:e=>{JT(e,n)},getToolbar:e=>Md.getState(uw.getCoupled(e,"toolbarSandbox")),isOpen:e=>Md.isOpen(uw.getCoupled(e,"toolbarSandbox"))}}),configFields:KT(),partFields:YT(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),tE=y([Un("items"),gi(["itemSelector"]),Jd("tgroupBehaviours",[Sp])]),oE=y([Tu({name:"items",unit:"item"})]),nE=Qu({name:"ToolbarGroup",configFields:tE(),partFields:oE(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Qd(e.tgroupBehaviours,[Sp.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),sE=e=>P(e,(e=>ja(e))),rE=(e,t,o)=>{jT(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{eE.setGroups(e,sE(n))}))}))},aE=Qu({name:"SplitFloatingToolbar",configFields:$T(),partFields:qT(),factory:(e,t,o,n)=>{const s=kh(eE.sketch({fetch:()=>Yx((t=>{t(sE(e.overflowGroups.get()))})),layouts:{onLtr:()=>[Ui,Wi],onRtl:()=>[Wi,Ui],onBottomLtr:()=>[Gi,ji],onBottomRtl:()=>[ji,Gi]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()}}));return{uid:e.uid,dom:e.dom,components:t,behaviours:Qd(e.splitToolbarBehaviours,[uw.config({others:{overflowGroup:()=>nE.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(P(o,t.getSystem().build)),rE(t,s,e)},refresh:t=>rE(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{eE.toggle(e)}))},isOpen:e=>s.getOpt(e).map(eE.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{eE.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(eE.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),iE=y([gi(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),hi("onOpened"),hi("onClosed")].concat(GT())),lE=y([Cu({factory:y_,schema:b_(),name:"primary"}),Cu({factory:y_,schema:b_(),name:"overflow",overrides:e=>({toolbarBehaviours:il([xT.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{Hu(t,e,"overflow-button").each((e=>{Kp.off(e),zp.focus(e)})),e.onClosed(t)},onGrown:t=>{Sp.focusIn(t),e.onOpened(t)},onStartGrow:t=>{Hu(t,e,"overflow-button").each(Kp.on)}}),Sp.config({mode:"acyclic",onEscape:t=>(Hu(t,e,"overflow-button").each(zp.focus),B.some(!0))})])})}),Ou({name:"overflow-button",overrides:e=>({buttonBehaviours:il([Kp.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])})}),Ou({name:"overflow-group"})]),cE=(e,t)=>{Hu(e,t,"overflow-button").bind((()=>Hu(e,t,"overflow"))).each((o=>{dE(e,t),xT.toggleGrow(o)}))},dE=(e,t)=>{Hu(e,t,"overflow").each((o=>{jT(e,t,(e=>{const t=P(e,(e=>ja(e)));y_.setGroups(o,t)})),Hu(e,t,"overflow-button").each((e=>{xT.hasGrown(o)&&Kp.on(e)})),xT.refresh(o)}))},uE=Qu({name:"SplitSlidingToolbar",configFields:iE(),partFields:lE(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:Qd(e.splitToolbarBehaviours,[uw.config({others:{overflowGroup:e=>nE.sketch({...n["overflow-group"](),items:[Sh.sketch({...n["overflow-button"](),action:t=>{vr(e,s)}})]})}}),Dp("toolbar-toggle-events",[Tr(s,(t=>{cE(t,e)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=P(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),dE(t,e)},refresh:t=>dE(t,e),toggle:t=>cE(t,e),isOpen:t=>((e,t)=>Hu(e,t,"overflow").map(xT.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),mE=e=>{const t=e.title.fold((()=>({})),(e=>({attributes:{title:e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"],...t},components:[nE.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:il([_x.config({}),zp.config({})])}},gE=e=>nE.sketch(mE(e)),pE=(e,t)=>{const o=Vr((t=>{const o=P(e.initGroups,gE);y_.setGroups(t,o)}));return il([Rv(e.providers.isDisabled),Fv(),Sp.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),Dp("toolbar-events",[o])])},hE=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":mE({title:B.none(),items:[]}),"overflow-button":IC({name:"more",icon:B.some("more-drawer"),enabled:!0,tooltip:B.some("More..."),primary:!1,buttonType:B.none(),borderless:!1},B.none(),e.providers)},splitToolbarBehaviours:pE(e,t)}},fE=e=>{const t=hE(e),o=aE.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return aE.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=Wo(t),n=Xe(t),s=Wo(n),r=Math.max(n.dom.scrollHeight,s.height);return No(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"}})},bE=e=>{const t=uE.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=uE.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=hE(e);return uE.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:e=>{e.getSystem().broadcastOn([U_()],{type:"opened"})},onClosed:e=>{e.getSystem().broadcastOn([U_()],{type:"closed"})}})},vE=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return y_.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===Rh.scrolling?["tox-toolbar--scrolling"]:[])},components:[y_.parts.groups({})],toolbarBehaviours:pE(e,t)})},yE=m_.optional({factory:sT,name:"menubar",schema:[Un("backstage")]}),xE=m_.optional({factory:{sketch:e=>f_.sketch({uid:e.uid,dom:e.dom,listBehaviours:il([Sp.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>vE({type:e.type,uid:qr("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),B.some(!0))}),setupItem:(e,t,o,n)=>{y_.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[Un("dom"),Un("onEscape")]}),wE=m_.optional({factory:{sketch:e=>{const t=(e=>e.type===Rh.sliding?bE:e.type===Rh.floating?fE:vE)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),B.some(!0)),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[Un("dom"),Un("onEscape"),Un("getSink")]}),SE=m_.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?J_:S_;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:il(o(t,e.sharedBackstage))}}},name:"header",schema:[Un("dom")]}),kE=m_.optional({name:"socket",schema:[Un("dom")]}),CE=m_.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:il([_x.config({}),zp.config({}),xT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{nm.getCurrent(e).each(_T.hideAllSlots),vr(e,FT)},onGrown:e=>{vr(e,FT)},onStartGrow:e=>{yr(e,DT,{width:Tt(e.element,"width").getOr("")})},onStartShrink:e=>{yr(e,DT,{width:Wt(e.element)+"px"})}}),Ap.config({}),nm.config({find:e=>{const t=Ap.contents(e);return oe(t)}})])}],behaviours:il([Ok(0),Dp("sidebar-sliding-events",[Tr(DT,((e,t)=>{St(e.element,"width",t.event.width)})),Tr(FT,((e,t)=>{Mt(e.element,"width")}))])])})},name:"sidebar",schema:[Un("dom")]}),OE=m_.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:il([Ap.config({}),RT.config({focus:!1}),nm.config({find:e=>oe(e.components())})]),components:[]})},name:"throbber",schema:[Un("dom")]});var _E=Qu({name:"OuterContainer",factory:(e,t,o)=>{const n={getSocket:t=>u_.getPart(t,e,"socket"),setSidebar:(t,o)=>{u_.getPart(t,e,"sidebar").each((e=>((e,t)=>{nm.getCurrent(e).each((e=>Ap.set(e,[MT(t)])))})(e,o)))},toggleSidebar:(t,o)=>{u_.getPart(t,e,"sidebar").each((e=>((e,t)=>{nm.getCurrent(e).each((e=>{nm.getCurrent(e).each((o=>{xT.hasGrown(e)?_T.isShowing(o,t)?xT.shrink(e):(_T.hideAllSlots(o),_T.showSlot(o,t)):(_T.hideAllSlots(o),_T.showSlot(o,t),xT.grow(e))}))}))})(e,o)))},whichSidebar:t=>u_.getPart(t,e,"sidebar").bind(AT).getOrNull(),getHeader:t=>u_.getPart(t,e,"header"),getToolbar:t=>u_.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{u_.getPart(t,e,"toolbar").each((e=>{e.getApis().setGroups(e,o)}))},setToolbars:(t,o)=>{u_.getPart(t,e,"multiple-toolbar").each((e=>{f_.setItems(e,o)}))},refreshToolbar:t=>{u_.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{u_.getPart(t,e,"toolbar").each((e=>{var t,o;o=t=>t(e),null!=(t=e.getApis().toggle)?B.some(o(t)):B.none()}))},isToolbarDrawerToggled:t=>u_.getPart(t,e,"toolbar").bind((e=>B.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>u_.getPart(t,e,"throbber"),focusToolbar:t=>{u_.getPart(t,e,"toolbar").orThunk((()=>u_.getPart(t,e,"multiple-toolbar"))).each((e=>{Sp.focusIn(e)}))},setMenubar:(t,o)=>{u_.getPart(t,e,"menubar").each((e=>{sT.setMenus(e,o)}))},focusMenubar:t=>{u_.getPart(t,e,"menubar").each((e=>{sT.focus(e)}))}};return{uid:e.uid,dom:e.dom,components:t,apis:n,behaviours:e.behaviours}},configFields:[Un("dom"),Un("behaviours")],partFields:[SE,yE,wE,xE,kE,CE,OE],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o)=>{e.setSidebar(t,o)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{const n=P(o,(e=>gE(e)));e.setToolbar(t,n)},setToolbars:(e,t,o)=>{const n=P(o,(e=>P(e,gE)));e.setToolbars(t,n)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)}}});const TE={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},EE=e=>"string"==typeof e?e.split(" "):e,BE=(e,t)=>{const o={...TE,...t.menus},n=ae(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?EE("file edit view insert format tools table help"):EE(!1===t.menubar?"":t.menubar),r=W(s,(e=>{const o=be(TE,e);return n?o||fe(t.menus,e).exists((e=>be(e,"items"))):o})),a=P(r,(n=>{const s=o[n];return((e,t,o)=>{const n=tf(o).split(/[ ,]/);return{text:e.title,getItems:()=>X(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||R(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:EE(s.items)},t,e)}));return W(a,(e=>e.getItems().length>0&&R(e.getItems(),(e=>"separator"!==e.type))))},ME=e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}},AE=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),DE=(e,t)=>AE(e,t+"/skin.min.css",e.ui.styleSheetLoader),FE=(e,t)=>{var o;return o=Fe(e.getElement()),lt(o).isSome()?AE(e,t+"/skin.shadowdom.min.css",Hh.DOM.styleSheetLoader):Promise.resolve()},IE=(e,t)=>{const o=_f(t);o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+".min.css"),!Cf(t)&&r(o)?Promise.all([DE(t,o),FE(t,o)]).then(ME(t),((e,t)=>()=>((e,t)=>e.dispatch("SkinLoadError",t))(e,{message:"Skin could not be loaded"}))(t)):ME(t)()},VE=S(IE,!1),RE=S(IE,!0),zE=(e,t)=>o=>{const n=Vl(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}},HE=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},PE=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},NE=(e,t)=>()=>e.execCommand(t),LE=(e,t,o)=>{const n=(e,n,r,a)=>{const i=t.shared.providers.translate(e.title);if("separator"===e.type)return B.some({type:"separator",text:i});if("submenu"===e.type){const t=X(e.getStyleItems(),(e=>s(e,n,a)));return 0===n&&t.length<=0?B.none():B.some({type:"nestedmenuitem",text:i,enabled:t.length>0,getSubmenuItems:()=>X(e.getStyleItems(),(e=>s(e,n,a)))})}return B.some({type:"togglemenuitem",text:i,icon:e.icon,active:e.isSelected(a),enabled:!r,onAction:o.onAction(e),...e.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},s=(e,t,s)=>{const r="formatter"===e.type&&o.isInvalid(e);return 0===t?r?[]:n(e,t,!1,s).toArray():n(e,t,r,s).toArray()},r=e=>{const t=o.getCurrentValue(),n=o.shouldHide?0:1;return X(e,(e=>s(e,n,t)))};return{validateItems:r,getFetch:(e,t)=>(o,n)=>{const s=t(),a=r(s);n(tC(a,Wf.CLOSE_ON_EXECUTE,e,!1))}}},WE=(e,t,o)=>{const n=o.dataset,s="basic"===n.type?()=>P(n.data,(e=>zO(e,o.isSelectedFor,o.getPreviewFor))):n.getData;return{items:LE(0,t,o),getStyleItems:s}},UE=(e,t,o)=>{const{items:n,getStyleItems:s}=WE(0,t,o),r=HE(e,"NodeChange",(e=>{const t=e.getComponent();o.updateText(t)}));return Jk({text:o.icon.isSome()?B.none():o.text,icon:o.icon,tooltip:B.from(o.tooltip),role:B.none(),fetch:n.getFetch(t,s),onSetup:r,getApi:e=>({getComponent:y(e)}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",t.shared)};var jE;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(jE||(jE={}));const GE=(e,t,o)=>{const n=(s=((e,t)=>t===jE.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),P(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},$E=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],qE=e=>{const t={type:"basic",data:$E};return{tooltip:"Align",text:B.none(),icon:B.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:B.none,getPreviewFor:e=>B.none,onAction:t=>()=>G($E,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=G($E,(t=>e.formatter.match(t.format))).fold(y("left"),(e=>e.title.toLowerCase()));yr(t,Yk,{icon:`align-${o}`})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},XE=(e,t)=>{const o=t(),n=P(o,(e=>e.format));return B.from(e.formatter.closest(n)).bind((e=>G(o,(t=>t.format===e)))).orThunk((()=>Se(e.formatter.match("p"),{title:"Paragraph",format:"p"})))},KE=e=>{const t="Paragraph",o=GE(e,"block_formats",jE.SemiColon);return{tooltip:"Blocks",text:B.some(t),icon:B.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:B.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return B.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))})},onAction:PE(e),updateText:n=>{const s=XE(e,(()=>o.data)).fold(y(t),(e=>e.title));yr(n,Kk,{text:s})},dataset:o,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},YE=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],JE=e=>{const t=e.split(/\s*,\s*/);return P(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},ZE=e=>{const t="System Font",o=()=>{const o=e=>e?JE(e)[0]:"",s=e.queryCommandValue("FontName"),r=n.data,a=s?s.toLowerCase():"",i=G(r,(e=>{const t=e.format;return t.toLowerCase()===a||o(t).toLowerCase()===o(a).toLowerCase()})).orThunk((()=>Se((e=>0===e.indexOf("-apple-system")&&(()=>{const t=JE(e.toLowerCase());return K(YE,(e=>t.indexOf(e.toLowerCase())>-1))})())(a),{title:t,format:a})));return{matchOpt:i,font:s}},n=GE(e,"font_family_formats",jE.SemiColon);return{tooltip:"Fonts",text:B.some(t),icon:B.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=o();return e},getPreviewFor:e=>()=>B.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:e=>{const{matchOpt:t,font:n}=o(),s=t.fold(y(n),(e=>e.title));yr(e,Kk,{text:s})},dataset:n,shouldHide:!1,isInvalid:_}},QE={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},eB={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},tB=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":fe(eB,e).getOr(e),oB=e=>fe(QE,e).getOr(""),nB=e=>{const t=()=>{let t=B.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=tB(s,e),r=oB(n);t=G(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=y(B.none),n=GE(e,"font_size_formats",jE.Space);return{tooltip:"Font sizes",text:B.some("12pt"),icon:B.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:e=>{const{matchOpt:o,size:n}=t(),s=o.fold(y(n),(e=>e.title));yr(e,Kk,{text:s})},dataset:n,shouldHide:!1,isInvalid:_}},sB=(e,t)=>{const o="Paragraph";return{tooltip:"Formats",text:B.some(o),icon:B.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:B.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?B.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):B.none()},onAction:PE(e),updateText:t=>{const n=e=>{const t=e.items;return void 0!==t&&t.length>0?X(t,n):[{title:e.title,format:e.format}]},s=X(RO(e),n),r=XE(e,y(s)).fold(y(o),(e=>e.title));yr(t,Kk,{text:r})},shouldHide:Qh(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}};var rB=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?Cp:kp)(o,r)}))};return Cr([Tr(Ys(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;V(s.channels,n)&&o(t,s.data)}})),Vr(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),aB=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),iB=[Un("channel"),Zn("renderComponents"),Zn("updateState"),Zn("initialData"),ds("reuseDom",!0)];const lB=cl({fields:iB,name:"reflecting",active:rB,apis:aB,state:Object.freeze({__proto__:null,init:()=>{const e=ps(B.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(B.none())}}})}),cB=y([Un("toggleClass"),Un("fetch"),bi("onExecute"),rs("getHotspot",B.some),rs("getAnchorOverrides",y({})),sc(),bi("onItemExecute"),Zn("lazySink"),Un("dom"),hi("onOpen"),Jd("splitDropdownBehaviours",[uw,Sp,zp]),rs("matchWidth",!1),rs("useMinWidth",!1),rs("eventOrder",{}),Zn("role")].concat(kw())),dB=Cu({factory:Sh,schema:[Un("dom")],name:"arrow",defaults:()=>({buttonBehaviours:il([zp.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(xr)},buttonBehaviours:il([Kp.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),uB=Cu({factory:Sh,schema:[Un("dom")],name:"button",defaults:()=>({buttonBehaviours:il([zp.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),mB=y([dB,uB,_u({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[Un("text")],name:"aria-descriptor"}),Ou({schema:[mi()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),gw()]),gB=Qu({name:"SplitDropdown",configFields:cB(),partFields:mB(),factory:(e,t,o,n)=>{const s=e=>{nm.getCurrent(e).each((e=>{Tm.highlightFirst(e),Sp.focusIn(e)}))},r=t=>{vw(e,x,t,n,s,pw.HighlightFirst).get(b)},a=t=>{const o=Pu(t,e,"button");return xr(o),B.some(!0)},i={...Cr([Vr(((t,o)=>{Hu(t,e,"aria-descriptor").each((e=>{const o=qr("aria");pt(e.element,"id",o),pt(t.element,"aria-describedby",o)}))}))]),...Jp(B.some(r))},l={repositionMenus:e=>{Kp.isOn(e)&&Sw(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[Js()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:Qd(e.splitDropdownBehaviours,[uw.config({others:{sandbox:t=>{const o=Pu(t,e,"arrow");return ww(e,t,{onOpen:()=>{Kp.on(o),Kp.on(t)},onClose:()=>{Kp.off(o),Kp.off(t)}})}}}),Sp.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(r(e),B.some(!0))}),zp.config({}),Kp.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),pB=e=>({isEnabled:()=>!bm.isDisabled(e),setEnabled:t=>bm.set(e,!t)}),hB=e=>({setActive:t=>{Kp.set(e,t)},isActive:()=>Kp.isOn(e),isEnabled:()=>!bm.isDisabled(e),setEnabled:t=>bm.set(e,!t)}),fB=(e,t)=>e.map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),bB=qr("focus-button"),vB=(e,t,o,n,s,r)=>({dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:fB(o,r)},components:Wv([e.map((e=>$k(e,r.icons))),t.map((e=>Xk(e,"tox-tbtn",r)))]),eventOrder:{[Bs()]:["focusing","alloy.base.behaviour","common-button-display-events"]},buttonBehaviours:il([Rv(r.isDisabled),Fv(),Dp("common-button-display-events",[Tr(Bs(),((e,t)=>{t.event.prevent(),vr(e,bB)}))])].concat(n.map((o=>lB.config({channel:o,initialData:{icon:e,text:t},renderComponents:(e,t)=>Wv([e.icon.map((e=>$k(e,r.icons))),e.text.map((e=>Xk(e,"tox-tbtn",r)))])}))).toArray()).concat(s.getOr([])))}),yB=(e,t,o)=>{const n=ps(b),s=vB(e.icon,e.text,e.tooltip,B.none(),B.none(),o);return Sh.sketch({dom:s.dom,components:s.components,eventOrder:jk,buttonBehaviours:il([Dp("toolbar-button-events",[(r={onAction:e.onAction,getApi:t.getApi},Hr(((e,t)=>{zv(r,e)((t=>{yr(e,Uk,{buttonApi:t}),r.onAction(t)}))}))),Hv(t,n),Pv(t,n)]),Rv((()=>!e.enabled||o.isDisabled())),Fv()].concat(t.toolbarButtonBehaviours))});var r},xB=(e,t,o)=>yB(e,{toolbarButtonBehaviours:[].concat(o.length>0?[Dp("toolbarButtonWith",o)]:[]),getApi:pB,onSetup:e.onSetup},t),wB=(e,t,o)=>nn(yB(e,{toolbarButtonBehaviours:[Ap.config({}),Kp.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[Dp("toolbarToggleButtonWith",o)]:[]),getApi:hB,onSetup:e.onSetup},t)),SB=(e,t,o)=>n=>Yx((e=>t.fetch(e))).map((s=>B.from(ix(nn(qy(qr("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,Wf.CLOSE_ON_EXECUTE,t.select.getOr(_),o),{movement:Ky(t.columns,t.presets),menuBehaviours:xv("auto"!==t.columns?[]:[Vr(((e,o)=>{yv(e,4,Qf(t.presets)).each((({numRows:t,numColumns:o})=>{Sp.setGridSize(e,t,o)}))}))])}))))),kB=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],CB=(e,t)=>(o,n,s)=>{const r=e(o).mapError((e=>Hn(e))).getOrDie();return t(r,n,s)},OB={button:CB(Gb,((e,t)=>{return o=e,n=t.backstage.shared.providers,xB(o,n,[]);var o,n})),togglebutton:CB(Xb,((e,t)=>{return o=e,n=t.backstage.shared.providers,wB(o,n,[]);var o,n})),menubutton:CB(oT,((e,t)=>AC(e,"tox-tbtn",t.backstage,B.none()))),splitbutton:CB((e=>Vn("SplitButton",nT,e)),((e,t)=>((e,t)=>{const o=qr("channel-update-split-dropdown-display"),n=e=>({isEnabled:()=>!bm.isDisabled(e),setEnabled:t=>bm.set(e,!t),setIconFill:(t,o)=>{Za(e.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each((e=>{pt(e,"fill",o)}))},setActive:t=>{pt(e.element,"aria-pressed",t),Za(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>Kp.set(e,t)))}))},isActive:()=>Za(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists(Kp.isOn)))}),s=ps(b),r={getApi:n,onSetup:e.onSetup};return gB.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...fB(e.tooltip,t.providers)}},onExecute:t=>{e.onAction(n(t))},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:il([Vv(t.providers.isDisabled),Fv(),Dp("split-dropdown-events",[Tr(bB,zp.focus),Hv(r,s),Pv(r,s)]),Tw.config({})]),eventOrder:{[lr()]:["alloy.base.behaviour","split-dropdown-events"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:SB(n,e,t.providers),parts:{menu:rb(0,e.columns,e.presets)},components:[gB.parts.button(vB(e.icon,e.text,B.none(),B.some(o),B.some([Kp.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),gB.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:Mh("chevron-down",t.providers.icons)},buttonBehaviours:il([Vv(t.providers.isDisabled),Fv(),Ah()])}),gB.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.backstage.shared))),grouptoolbarbutton:CB((e=>Vn("GroupToolbarButton",Q_,e)),((e,t,o)=>{const n=o.ui.registry.getAll().buttons,s={[oc]:t.backstage.shared.header.isPositionedAtTop()?tc.TopToBottom:tc.BottomToTop};if(of(o)===Rh.floating)return((e,t,o,n)=>{const s=t.shared;return eE.sketch({lazySink:s.getSink,fetch:()=>Yx((t=>{t(P(o(e.items),gE))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:vB(e.icon,e.text,e.tooltip,B.none(),B.none(),s.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t.backstage,(e=>TB(o,{buttons:n,toolbar:e,allowToolbarGroups:!1},t,B.none())),s);throw new Error("Toolbar groups are only supported when using floating toolbar mode")})),styleSelectButton:(e,t)=>((e,t)=>{const o={type:"advanced",...t.styles};return UE(e,t,sB(e,o))})(e,t.backstage),fontsizeSelectButton:(e,t)=>((e,t)=>UE(e,t,nB(e)))(e,t.backstage),fontSelectButton:(e,t)=>((e,t)=>UE(e,t,ZE(e)))(e,t.backstage),formatButton:(e,t)=>((e,t)=>UE(e,t,KE(e)))(e,t.backstage),alignMenuButton:(e,t)=>((e,t)=>UE(e,t,qE(e)))(e,t.backstage)},_B={styles:OB.styleSelectButton,fontsize:OB.fontsizeSelectButton,fontfamily:OB.fontSelectButton,blocks:OB.formatButton,align:OB.alignMenuButton},TB=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=P(kB,(t=>{const o=W(t.items,(t=>be(e,t)||be(_B,t)));return{name:t.name,items:o}}));return W(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return P(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>be(e,"name")&&be(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=P(s,(s=>{const r=X(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>fe(t,o.toLowerCase()).orThunk((()=>r.bind((e=>re(e,(e=>fe(t,e+o.toLowerCase()))))))).fold((()=>fe(_B,o.toLowerCase()).map((t=>t(e,s))).orThunk((()=>B.none()))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o)=>fe(OB,e.type).fold((()=>(console.error("skipping button defined by",e),B.none())),(n=>B.some(n(e,t,o)))))(t,s,e):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),B.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).toArray()));return{title:B.from(e.translate(s.name)),items:r}}));return W(a,(e=>e.items.length>0))},EB=(e,t,o,n)=>{const s=t.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return TB(e,s,{backstage:n},B.none())}));_E.setToolbars(s,t)}else _E.setToolbar(s,TB(e,o,{backstage:n},B.none()))},BB=wo(),MB=BB.os.isiOS()&&BB.os.version.major<=12;var AB=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const r=ps(0),a=t.outerContainer;VE(e);const i=Fe(s.targetNode),l=it(at(i));((e,t)=>{vd(e,t,_o)})(i,t.mothership),bd(l,t.uiMothership),e.on("PostRender",(()=>{EB(e,t,o,n),r.set(e.getWin().innerWidth),_E.setMenubar(a,BE(e,o)),_E.setSidebar(a,o.sidebar),((e,t)=>{const o=e.dom;let n=e.getWin();const s=e.getDoc().documentElement,r=ps(zt(n.innerWidth,n.innerHeight)),a=ps(zt(s.offsetWidth,s.offsetHeight)),i=()=>{const t=r.get();t.left===n.innerWidth&&t.top===n.innerHeight||(r.set(zt(n.innerWidth,n.innerHeight)),Cy(e))},l=()=>{const t=e.getDoc().documentElement,o=a.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(a.set(zt(t.offsetWidth,t.offsetHeight)),Cy(e))},c=t=>((e,t)=>e.dispatch("ScrollContent",t))(e,t);o.bind(n,"resize",i),o.bind(n,"scroll",c);const d=Pl(Fe(e.getBody()),"load",l),u=t.uiMothership.element;e.on("hide",(()=>{St(u,"display","none")})),e.on("show",(()=>{Mt(u,"display")})),e.on("NodeChange",l),e.on("remove",(()=>{d.unbind(),o.unbind(n,"resize",i),o.unbind(n,"scroll",c),n=null}))})(e,t)}));const d=_E.getSocket(a).getOrDie("Could not find expected socket element");if(MB){kt(d.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=((e,t)=>{let o=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null)},throttle:(...t)=>{c(o)&&(o=setTimeout((()=>{o=null,e.apply(null,t)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=Hl(d.element,"scroll",t.throttle);e.on("remove",o.unbind)}Dv(e,t),e.addCommand("ToggleSidebar",((t,o)=>{_E.toggleSidebar(a,o),e.dispatch("ToggleSidebar")})),e.addQueryValueHandler("ToggleSidebar",(()=>_E.whichSidebar(a)));const u=of(e);u!==Rh.sliding&&u!==Rh.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==r.get()&&(_E.refreshToolbar(t.outerContainer),r.set(o))}));const m={setEnabled:e=>{Av(t,!e)},isEnabled:()=>!bm.isDisabled(a)};return{iframeContainer:d.element.dom,editorContainer:a.element.dom,api:m}}});const DB=e=>/^[0-9\.]+(|px)$/i.test(""+e)?B.some(parseInt(""+e,10)):B.none(),FB=e=>h(e)?e+"px":e,IB=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},VB=e=>{const t=$h(e),o=qh(e),n=Kh(e);return DB(t).map((e=>IB(e,o,n)))},{ToolbarLocation:RB,ToolbarMode:zB}=Hf,HB=(e,t)=>{const o=Lo(e);return{pos:t?o.y:o.bottom,bounds:o}};var PB=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mothership:r,uiMothership:a,outerContainer:i}=t,l=ps(null),c=Fe(s.targetNode),d=((e,t,o,n,s)=>{const{uiMothership:r,outerContainer:a}=o,i=Hh.DOM,l=Ff(e),c=Rf(e),d=Kh(e).or(VB(e)),u=n.shared.header,m=u.isPositionedAtTop,g=of(e),p=g===zB.sliding||g===zB.floating,h=ps(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(y(0),(e=>e.components().length>1?It(e.components()[1].element):0)):0,v=()=>{r.broadcastOn([Dd()],{})},x=(e=!1)=>{if(f()){if(l||(()=>{const e=d.getOrThunk((()=>{const e=DB(Ot(ut(),"margin-left")).getOr(0);return Wt(ut())-Pt(t).left+e}));St(s.get().element,"max-width",e+"px")})(),p&&_E.refreshToolbar(a),l||(()=>{const e=_E.getToolbar(a),o=b(e),n=Lo(t),r=m()?Math.max(n.y-It(s.get().element)+o,0):n.bottom;kt(a.element,{position:"absolute",top:Math.round(r)+"px",left:Math.round(n.x)+"px"})})(),c){const t=s.get();e?W_.reset(t):W_.refresh(t)}v()}},w=(o=!0)=>{if(l||!c||!f())return;const n=u.getDockingMode(),r=(o=>{switch(sf(e)){case RB.auto:const e=_E.getToolbar(a),n=b(e),s=It(o.element)-n,r=Lo(t);if(r.y>s)return"top";{const e=Xe(t),o=Math.max(e.dom.scrollHeight,It(e));return r.bottom<o-s||Uo().bottom<r.bottom-s?"bottom":"top"}case RB.bottom:return"bottom";case RB.top:default:return"top"}})(s.get());r!==n&&((e=>{const t=s.get();W_.setModes(t,[e]),u.setDockingMode(e);const o=m()?tc.TopToBottom:tc.BottomToTop;pt(t.element,oc,o)})(r),o&&x(!0))};return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),St(a.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),Mt(r.element,"display"),w(!1),x()},hide:()=>{h.set(!1),o.outerContainer&&(St(a.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus")),St(r.element,"display","none")},update:x,updateMode:w,repositionPopups:v}})(e,c,t,n,l),u=lf(e);RE(e);const m=()=>{if(l.get())return void d.show();l.set(_E.getHeader(i).getOrDie());const s=If(e);bd(s,r),bd(s,a),EB(e,t,o,n),_E.setMenubar(i,BE(e,o)),d.show(),((e,t,o,n)=>{const s=ps(HB(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=HB(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&Cy(e,n),o.isVisible()&&(i!==r?o.update(!0):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update(!0))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))})),e.on("ScrollWindow",(()=>o.updateMode()));const a=Vl();a.set(Pl(Fe(e.getBody()),"load",r)),e.on("remove",(()=>{a.clear()}))})(e,c,d,u),e.nodeChanged()},g=()=>wh.setEditorTimeout(e,m,0);e.on("show",m),e.on("hide",d.hide),u||(e.on("focus",g),e.on("blur",d.hide)),e.on("init",(()=>{(e.hasFocus()||u)&&g()})),Dv(e,t);const p={show:()=>{m()},hide:()=>{d.hide()},setEnabled:e=>{Av(t,!e)},isEnabled:()=>!bm.isDisabled(i)};return{editorContainer:i.element.dom,api:p}}});const NB="contexttoolbar-hide",LB=(e,t)=>Tr(Uk,((o,n)=>{const s=(e=>({hide:()=>vr(e,tr()),getValue:()=>Yd.getValue(e)}))(e.get(o));t.onAction(s,n.event.buttonApi)})),WB=(e,t)=>{const o=e.label.fold((()=>({})),(e=>({"aria-label":e}))),n=kh(Gx.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:e.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:il([Sp.config({mode:"special",onEnter:e=>s.findPrimary(e).map((e=>(xr(e),!0))),onLeft:(e,t)=>(t.cut(),B.none()),onRight:(e,t)=>(t.cut(),B.none())})])})),s=((e,t,o)=>{const n=P(t,(t=>kh(((e,t,o)=>{const n={backstage:{shared:{providers:o}}};return"contextformtogglebutton"===t.type?((e,t,o)=>{const{primary:n,...s}=t.original,r=Rn(Xb({...s,type:"togglebutton",onAction:b}));return wB(r,o.backstage.shared.providers,[LB(e,t)])})(e,t,n):((e,t,o)=>{const{primary:n,...s}=t.original,r=Rn(Gb({...s,type:"button",onAction:b}));return xB(r,o.backstage.shared.providers,[LB(e,t)])})(e,t,n)})(e,t,o))));return{asSpecs:()=>P(n,(e=>e.asSpec())),findPrimary:e=>re(t,((t,o)=>t.primary?B.from(n[o]).bind((t=>t.getOpt(e))).filter(k(bm.isDisabled)):B.none()))}})(n,e.commands,t);return[{title:B.none(),items:[n.asSpec()]},{title:B.none(),items:s.asSpecs()}]},UB=(e,t,o=.01)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,jB=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=ot(Fe(e.startContainer),e.startOffset).element;return(He(o)?Ye(o):B.some(o)).filter(ze).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=Do();return No(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=Wo(Fe(e.getBody()));return No(o.x+t.left,o.y+t.top,t.width,t.height)}},GB=(e,t,o,n=0)=>{const s=Vo(window),r=Lo(Fe(e.getContentAreaContainer())),a=Of(e)||Ef(e)||Mf(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return No(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=Fe(e.getContainer()),i=Za(a,".tox-editor-header").getOr(a),l=Lo(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?Lo(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return No(i,c,l,d-c)}},$B={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},qB={maxHeightFunction:$l(),maxWidthFunction:XT()},XB=e=>"node"===e,KB=(e,t,o,n,s)=>{const r=jB(e),a=n.lastElement().exists((e=>je(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=ot(Fe(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&je(n.element,t)})(e,o)?a?vO:gO:a?((e,o,s)=>{const a=Tt(e,"position");St(e,"position",o);const i=UB(r,Lo(t))&&!n.isReposition()?xO:vO;return a.each((t=>St(e,"position",t))),i})(t,n.getMode()):("fixed"===n.getMode()?s.y+Do().top:s.y)+(It(t)+12)<=r.y?gO:pO},YB=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({...KB(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>XB(n)?[s(e)]:[];return t?{onLtr:e=>[qi,Wi,Ui,ji,Gi,$i].concat(r(e)),onRtl:e=>[qi,Ui,Wi,Gi,ji,$i].concat(r(e))}:{onLtr:e=>[$i,qi,ji,Wi,Gi,Ui].concat(r(e)),onRtl:e=>[$i,qi,Gi,Ui,ji,Wi].concat(r(e))}},JB=(e,t)=>{const o=W(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=L(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},ZB=(e,t)=>{const o={},n=[],s=[],r={},a={},i=ae(e);return N(i,(i=>{const l=e[i];"contextform"===l.type?((e,i)=>{const l=Rn(Vn("ContextForm",tv,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,t)=>{var o;(o=t,Vn("ContextToolbar",ov,o)).each((o=>{"editor"===t.scope?s.push(o):n.push(o),a[e]=o}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},QB=qr("forward-slide"),eM=qr("backward-slide"),tM=qr("change-slide-event"),oM="tox-pop--resizing",nM="tox-pop--transition",sM=(e,t,o,n)=>{const s=n.backstage,r=s.shared,a=wo().deviceType.isTouch,i=Rl(),l=Rl(),c=Rl(),d=Ua((e=>{const t=ps([]);return xh.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),xh.getContent(e).each((e=>{Mt(e.element,"visibility")})),_a(e.element,oM),Mt(e.element,"width")},inlineBehaviours:il([Dp("context-toolbar-events",[Ir(Ws(),((e,t)=>{"width"===t.event.raw.propertyName&&(_a(e.element,oM),Mt(e.element,"width"))})),Tr(tM,((e,t)=>{const o=e.element;Mt(o,"width");const n=Wt(o);xh.setContent(e,t.event.contents),Oa(o,oM);const s=Wt(o);St(o,"width",n+"px"),xh.getContent(e).each((e=>{t.event.focus.bind((e=>(hl(e),vl(o)))).orThunk((()=>(Sp.focusIn(e),bl(at(o)))))})),setTimeout((()=>{St(e.element,"width",s+"px")}),0)})),Tr(QB,((e,o)=>{xh.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:bl(at(e.element))}]))})),yr(e,tM,{contents:o.event.forwardContents,focus:B.none()})})),Tr(eM,((e,o)=>{ne(t.get()).each((o=>{t.set(t.get().slice(0,t.get().length-1)),yr(e,tM,{contents:ja(o.bar),focus:o.focus})}))}))]),Sp.config({mode:"special",onEscape:o=>ne(t.get()).fold((()=>e.onEscape()),(e=>(vr(o,eM),B.some(!0))))})]),lazySink:()=>$o.value(e.sink)})})({sink:o,onEscape:()=>(e.focus(),B.some(!0))})),u=()=>{const t=c.get().getOr("node"),o=XB(t)?1:0;return GB(e,r,t,o)},m=()=>!(e.removed||a()&&s.isContextMenuOpen()),g=()=>{if(m()){const t=u(),o=ye(c.get(),"node")?((e,t)=>t.filter(dt).map(Wo).getOrThunk((()=>jB(e))))(e,i.get()):jB(e);return t.height<=0||!UB(o,t)}return!0},p=()=>{i.clear(),l.clear(),c.clear(),xh.hide(d)},h=()=>{if(xh.isOpen(d)){const e=d.element;Mt(e,"display"),g()?St(e,"display","none"):(l.set(0),xh.reposition(d))}},f=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:il([Sp.config({mode:"acyclic"}),Dp("pop-dialog-wrap-events",[Vr((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>Sp.focusIn(t)))})),Rr((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),v=jt((()=>ZB(t,(e=>{const t=y([e]);yr(d,QB,{forwardContents:f(t)})})))),y=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...v().formNavigators},a=of(e)===Rh.scrolling?Rh.scrolling:Rh.default,i=q(P(t,(t=>"contexttoolbar"===t.type?((t,o)=>TB(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n,B.some(["form:"])))(s,t):((e,t)=>WB(e,t))(t,r.providers))));return vE({type:a,uid:qr("context-toolbar"),initGroups:i,onEscape:B.none,cyclicKeying:!0,providers:r.providers})},x=(t,n)=>{if(w.cancel(),!m())return;const s=y(t),p=t[0].position,h=((t,n)=>{const s="node"===t?r.anchors.node(n):r.anchors.cursor(),c=((e,t,o,n)=>"line"===t?{bubble:Yl(12,0,$B),layouts:{onLtr:()=>[Xi],onRtl:()=>[Ki]},overrides:qB}:{bubble:Yl(0,12,$B,1/12),layouts:YB(e,o,n,t),overrides:qB})(e,t,a(),{lastElement:i.get,isReposition:()=>ye(l.get(),0),getMode:()=>rd.getMode(o)});return nn(s,c)})(p,n);c.set(p),l.set(1);const b=d.element;Mt(b,"display"),(e=>ye(we(e,i.get(),je),!0))(n)||(_a(b,nM),rd.reset(o,d)),xh.showWithinBounds(d,f(s),{anchor:h,transition:{classes:[nM],mode:"placement"}},(()=>B.some(u()))),n.fold(i.clear,i.set),g()&&St(b,"display","none")},w=fC((()=>{e.hasFocus()&&!e.removed&&(Ta(d.element,nM)?w.throttle():((e,t)=>{const o=Fe(t.getBody()),n=e=>je(e,o),s=Fe(t.selection.getNode());return(e=>!n(e)&&!Ge(o,e))(s)?B.none():((e,t,o)=>{const n=JB(e,t);if(n.contextForms.length>0)return B.some({elem:e,toolbars:[n.contextForms[0]]});{const t=JB(e,o);if(t.contextForms.length>0)return B.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>R(e,(e=>e.position===t)),o=t=>W(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=P(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return B.some({elem:e,toolbars:o})}return B.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?B.none():ys(t,(e=>{if(ze(e)){const{contextToolbars:t,contextForms:n}=JB(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>G(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>W(e,(e=>e.position===t))))}})(t);return s.length>0?B.some({elem:e,toolbars:s}):B.none()}return B.none()}),e))(n,s,e)))})(v(),e).fold(p,(e=>{x(e.toolbars,B.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",p),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",h),e.on("click keyup focus SetContent",w.throttle),e.on(NB,p),e.on("contexttoolbar-show",(t=>{const o=v();fe(o.lookupTable,t.toolbarKey).each((o=>{x([o],Se(t.target!==e,t.target)),xh.getContent(d).each(Sp.focusIn)}))})),e.on("focusout",(t=>{wh.setEditorTimeout(e,(()=>{vl(o.element).isNone()&&vl(d.element).isNone()&&p()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&p()})),e.on("AfterProgressState",(t=>{t.state?p():e.hasFocus()&&w.throttle()})),e.on("NodeChange",(e=>{vl(d.element).fold(w.throttle,b)}))}))},rM={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},aM=(()=>{const e="[0-9]+",t="[eE][+-]?[0-9]+",o=e=>`(?:${e})?`,n=["Infinity","[0-9]+\\."+o(e)+o(t),"\\.[0-9]+"+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),iM=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=Rl();return P(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(ye(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},lM={name:"lineheight",text:"Line height",icon:"line-height",getOptions:Tf,hash:e=>((e,t)=>((e,t)=>B.from(aM.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>R(t,(t=>R(rM[t],(t=>e===t)))))(n,t)?B.some({value:o,unit:n}):B.none()})))(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:x,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>B.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t)},cM=e=>HE(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent"))})),dM=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),()=>e.off("PastePlainTextToggle",n)},uM=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},mM=e=>{(e=>{(e=>{Vk.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:zE(e,t.name),onAction:uM(e,t.name)})}));for(let t=1;t<=6;t++){const o="h"+t;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:zE(e,o),onAction:uM(e,o)})}})(e),(e=>{Vk.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"print",text:"Print",action:"mcePrint",icon:"print"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:NE(e,t.action)})}))})(e),(e=>{Vk.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:NE(e,t.action),onSetup:zE(e,t.name)})}))})(e)})(e),(e=>{Vk.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:NE(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:uM(e,"code")})})(e)},gM=(e,t)=>HE(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),pM=e=>HE(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),hM=(e,t)=>{(e=>{N([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:NE(e,t.cmd),onSetup:zE(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onAction:NE(e,"JustifyNone")})})(e),mM(e),((e,t)=>{((e,t)=>{const o=WE(0,t,qE(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=WE(0,t,ZE(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=WE(0,t,sB(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=WE(0,t,KE(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=WE(0,t,nB(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:gM(e,"hasUndo"),onAction:NE(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:gM(e,"hasRedo"),onAction:NE(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:gM(e,"hasUndo"),onAction:NE(e,"undo")}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:gM(e,"hasRedo"),onAction:NE(e,"redo")})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},null,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=ps(Hy),o=ps(Hy);jy(e,"forecolor","forecolor","Text color",t),jy(e,"backcolor","hilitecolor","Background color",o),Gy(e,"forecolor","forecolor","Text color"),Gy(e,"backcolor","hilitecolor","Background color")})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:NE(e,"mceToggleVisualAid")})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:pM(e),onAction:NE(e,"mceToggleVisualAid")})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:cM(e),onAction:NE(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:NE(e,"indent")})})(e)})(e),(e=>{iM(e,lM),(e=>B.from(ef(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:y(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:t.customCode}).unbind,getCurrent:e=>{const t=Fe(e.selection.getNode());return xs(t,(e=>B.some(e).filter(ze).bind((e=>bt(e,"lang").map((t=>({code:t,customCode:bt(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=Vl();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),o.clear}}))))(e).each((t=>iM(e,t)))})(e),(e=>{const t=ps(kf(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:dM(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:dM(e,t)})})(e)},fM=e=>r(e)?e.split(/[ ,]/):e,bM=e=>t=>t.options.get(e),vM=bM("contextmenu_never_use_native"),yM=bM("contextmenu_avoid_overlap"),xM=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:W(o,(e=>be(t,e)))},wM=(e,t)=>({type:"makeshift",x:e,y:t}),SM=e=>"longpress"===e.type||0===e.type.indexOf("touch"),kM=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(SM(e)){const t=e.touches[0];return wM(t.pageX,t.pageY)}return wM(e.pageX,e.pageY)})(t):((e,t)=>{const o=Hh.DOM.getPos(e);return((e,t,o)=>wM(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(SM(e)){const t=e.touches[0];return wM(t.clientX,t.clientY)}return wM(e.clientX,e.clientY)})(t)):CM(e),CM=e=>({type:"selection",root:Fe(e.selection.getNode())}),OM=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:B.some(Fe(e.selection.getNode())),root:Fe(e.getBody())}))(e);case"point":return kM(e,t);case"selection":return CM(e)}},_M=(e,t,o,n,s,r)=>{const a=o(),i=OM(e,t,r);tC(a,Wf.CLOSE_ON_EXECUTE,n,!1).map((e=>{t.preventDefault(),xh.showMenuAt(s,{anchor:i},{menu:{markers:ob("normal")},data:e})}))},TM={onLtr:()=>[qi,Wi,Ui,ji,Gi,$i,gO,pO,mO,dO,uO,cO],onRtl:()=>[qi,Ui,Wi,Gi,ji,$i,gO,pO,uO,cO,mO,dO]},EM={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},BM=(e,t,o,n,s,r)=>{const a=wo(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=OM(e,t,o);return{bubble:Yl(0,"point"===o?12:0,EM),layouts:TM,overrides:{maxWidthFunction:XT(),maxHeightFunction:$l()},...n}})(e,t,r);tC(o,Wf.CLOSE_ON_EXECUTE,n,!0).map((o=>{t.preventDefault(),xh.showMenuWithinBounds(s,{anchor:i},{menu:{markers:ob("normal"),highlightImmediately:a},data:o,type:"horizontal"},(()=>B.some(GB(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(NB)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{wh.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return Fc(e.getWin(),kc.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},MM=e=>r(e)?"|"===e:"separator"===e.type,AM={type:"separator"},DM=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return AM;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:P(t,DM)}};default:return{type:"menuitem",...t(e),onAction:(o=e.onAction,()=>o())}}var o},FM=(e,t)=>{if(0===t.length)return e;const o=ne(e).filter((e=>!MM(e))).fold((()=>[]),(e=>[AM]));return e.concat(o).concat(t).concat([AM])},IM=(e,t)=>"longpress"!==t.type&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),VM=(e,t)=>IM(e,t)?e.selection.getStart(!0):t.target,RM=(e,t,o)=>{const n=wo().deviceType.isTouch,s=Ua(xh.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:il([Dp("dismissContextMenu",[Tr(dr(),((t,o)=>{Md.close(t),e.focus()}))])])})),a=e=>xh.hide(s),i=t=>{if(vM(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!vM(e))(e,t)||(e=>0===xM(e).length)(e))return;const a=((e,t)=>{const o=yM(e),n=IM(e,t)?"selection":"point";if(Te(o)){const s=VM(e,t);return cx(Fe(s),o)?"node":n}return n})(e,t);(n()?BM:_M)(e,t,(()=>{const o=VM(e,t),n=e.ui.registry.getAll(),s=xM(e);return((e,t,o)=>{const n=j(t,((t,n)=>fe(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n))return FM(t,n.split(" "));if(n.length>0){const e=P(n,DM);return FM(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&MM(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},zM=hs([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),HM=e=>t=>t.translate(-e.left,-e.top),PM=e=>t=>t.translate(e.left,e.top),NM=e=>(t,o)=>j(e,((e,t)=>t(e)),zt(t,o)),LM=(e,t,o)=>e.fold(NM([PM(o),HM(t)]),NM([HM(t)]),NM([])),WM=(e,t,o)=>e.fold(NM([PM(o)]),NM([]),NM([PM(t)])),UM=(e,t,o)=>e.fold(NM([]),NM([HM(o)]),NM([PM(t),HM(o)])),jM=(e,t,o)=>{const n=e.fold(((e,t)=>({position:B.some("absolute"),left:B.some(e+"px"),top:B.some(t+"px")})),((e,t)=>({position:B.some("absolute"),left:B.some(e-o.left+"px"),top:B.some(t-o.top+"px")})),((e,t)=>({position:B.some("fixed"),left:B.some(e+"px"),top:B.some(t+"px")})));return{right:B.none(),bottom:B.none(),...n}},GM=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s(UM,$M),s(WM,qM),s(LM,XM))},$M=zM.offset,qM=zM.absolute,XM=zM.fixed,KM=(e,t)=>{const o=ft(e,t);return u(o)?NaN:parseInt(o,10)},YM=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=KM(o,t.leftAttr),s=KM(o,t.topAttr);return isNaN(n)||isNaN(s)?B.none():B.some(zt(n,s))})(e,t).fold((()=>o),(e=>XM(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?ZM(e,t,a,s,r):QM(e,t,a,s,r),l=LM(a,s,r);return((e,t,o)=>{const n=e.element;pt(n,t.leftAttr,o.left+"px"),pt(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:XM(l.left,l.top),extra:B.none()})),(e=>({coord:e.output,extra:e.extra})))},JM=(e,t,o,n)=>re(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=WM(e,s,r),i=WM(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?B.some({output:GM(e.output,t,o,n),extra:e.extra}):B.none()})),ZM=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return JM(r,o,n,s).orThunk((()=>{const e=j(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=WM(e,s,r),i=WM(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return zt(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:B.some(a),snap:B.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:B.some(a),snap:B.some(t)}:e))}),{deltas:B.none(),snap:B.none()});return e.snap.map((e=>({output:GM(e.output,o,n,s),extra:e.extra})))}))},QM=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return JM(r,o,n,s)};var eA=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=$e(e.element),o=Do(t),r=__(s),a=((e,t,o)=>({coord:GM(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=jM(a.coord,0,r);Ct(s,i)}}});const tA="data-initial-z-index",oA=(e,t)=>{e.getSystem().addToGui(t),(e=>{Ye(e.element).filter(ze).each((t=>{Tt(t,"z-index").each((e=>{pt(t,tA,e)})),St(t,"z-index",Ot(e.element,"z-index"))}))})(t)},nA=e=>{(e=>{Ye(e.element).filter(ze).each((e=>{bt(e,tA).fold((()=>Mt(e,"z-index")),(t=>St(e,"z-index",t))),yt(e,tA)}))})(e),e.getSystem().removeFromGui(e)},sA=(e,t,o)=>e.getSystem().build(yx.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var rA=ss("snaps",[Un("getSnapPoints"),hi("onSensor"),Un("leftAttr"),Un("topAttr"),rs("lazyViewport",Uo),rs("mustSnap",!1)]);const aA=[rs("useFixed",_),Un("blockerClass"),rs("getTarget",x),rs("onDrag",b),rs("repositionTarget",!0),rs("onDrop",b),us("getBounds",Uo),rA],iA=(e,t)=>({bounds:e.getBounds(),height:Vt(t.element),width:Ut(t.element)}),lA=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>iA(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=$e(e.element),a=Do(r),i=__(s),l=(e=>{return(t=Tt(e,"left"),o=Tt(e,"top"),n=Tt(e,"position"),s=(e,t,o)=>("fixed"===o?XM:$M)(parseInt(e,10),parseInt(t,10)),t.isSome()&&o.isSome()&&n.isSome()?B.some(s(t.getOrDie(),o.getOrDie(),n.getOrDie())):B.none()).getOrThunk((()=>{const t=Pt(e);return qM(t.left,t.top)}));var t,o,n,s})(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=WM(t,o,n),i=Fi(a.left,r.x,r.x+r.width-s.width),l=Fi(a.top,r.y,r.y+r.height-s.height),c=qM(i,l);return t.fold((()=>{const e=UM(c,o,n);return $M(e.left,e.top)}),y(c),(()=>{const e=LM(c,o,n);return XM(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>$M(e+a,t+i)),((e,t)=>qM(e+a,t+i)),((e,t)=>XM(e+a,t+i))));var t,a,i;const l=LM(e,n,s);return XM(l.left,l.top)}),(t=>{const a=YM(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=jM(c,0,i);Ct(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},cA=(e,t,o,n)=>{t.each(nA),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;yt(o,t.leftAttr),yt(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},dA=e=>(t,o)=>{const n=e=>{o.setStartData(iA(t,e))};return Cr([Tr(ar(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var uA=Object.freeze({__proto__:null,getData:e=>B.from(zt(e.x,e.y)),getDelta:(e,t)=>zt(t.left-e.left,t.top-e.top)});const mA=(e,t,o)=>[Tr(Bs(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>cA(n,B.some(l),e,t),a=dx(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),lA(n,e,t,uA,o)}},l=sA(n,e.blockerClass,(e=>Cr([Tr(Bs(),e.forceDrop),Tr(Ds(),e.drop),Tr(Ms(),((t,o)=>{e.move(o.event)})),Tr(As(),e.delayDrop)]))(i));o(n),oA(n,l)}))],gA=[...aA,yi("dragger",{handlers:dA(mA)})];var pA=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return B.some(zt(t.clientX,t.clientY))})(t):B.none()},getDelta:(e,t)=>zt(t.left-e.left,t.top-e.top)});const hA=(e,t,o)=>{const n=Rl(),s=o=>{cA(o,n.get(),e,t),n.clear()};return[Tr(Os(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{lA(r,e,t,pA,o)}},c=sA(r,e.blockerClass,(e=>Cr([Tr(Os(),e.forceDrop),Tr(Ts(),e.drop),Tr(Es(),e.drop),Tr(_s(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),oA(r,c)})),Tr(_s(),((o,n)=>{n.stop(),lA(o,e,t,pA,n.event)})),Tr(Ts(),((e,t)=>{t.stop(),s(e)})),Tr(Es(),s)]},fA=gA,bA=[...aA,yi("dragger",{handlers:dA(hA)})],vA=[...aA,yi("dragger",{handlers:dA(((e,t,o)=>[...mA(e,t,o),...hA(e,t,o)]))})];var yA=Object.freeze({__proto__:null,mouse:fA,touch:bA,mouseOrTouch:vA}),xA=Object.freeze({__proto__:null,init:()=>{let e=B.none(),t=B.none();const o=y({});return ua({readState:o,reset:()=>{e=B.none(),t=B.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=B.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=B.some(e)}})}});const wA=ul({branchKey:"mode",branches:yA,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:B.from(e.extra)})},state:xA,apis:eA}),SA=(e,t,o,n,s,r)=>e.fold((()=>wA.snap({sensor:qM(o-20,n-20),range:zt(s,r),output:qM(B.some(o),B.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return wA.snap({sensor:qM(s,r),range:zt(40,40),output:qM(B.some(o-a.width/2),B.some(n-a.height/2)),extra:{td:t}})})),kA=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>je(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),CA=e=>kh(Sh.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:il([wA.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),Tw.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),OA=(e,t)=>{const o=ps([]),n=ps([]),s=ps(!1),r=Rl(),a=Rl(),i=e=>{const o=Wo(e);return SA(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=Wo(e);return SA(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=kA((()=>P(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=kA((()=>P(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=CA(c),m=CA(d),g=Ua(u.asSpec()),p=Ua(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);wA.snapTo(t,r),((t,o,n,r)=>{const a=o.dom.getBoundingClientRect();Mt(t.element,"display");const i=Ke(Fe(e.getBody())).dom.innerHeight,l=a[s]<0,c=((e,t)=>e[s]>t)(a,i);(l||c)&&St(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");wo().deviceType.isTouch()&&(e.on("TableSelectionChange",(e=>{s.get()||(md(t,g),md(t,p),s.set(!0)),r.set(e.start),a.set(e.finish),e.otherCells.each((t=>{o.set(t.upOrLeftCells),n.set(t.downOrRightCells),f(e.start),b(e.finish)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(hd(g),hd(p),s.set(!1)),r.clear(),a.clear()})))},_A=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:il([Sp.config({mode:"flow",selector:"div[role=button]"}),bm.config({disabled:o.isDisabled}),Fv(),_x.config({}),Ap.config({}),Dp("elementPathEvents",[Vr(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>Sp.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=e.dispatch("ResolveName",{name:r.nodeName.toLowerCase(),target:r});if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?j(r,((t,n,r)=>{const a=((t,n,s)=>Sh.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s,"aria-level":s+1}},components:[Pa(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:il([Iv(o.isDisabled),Fv()])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[Pa(` ${s} `)]},a])}),[]):[];Ap.set(t,a)}))}))])]),components:[]}};var TA;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(TA||(TA={}));const EA=(e,t,o)=>{const n=Fe(e.getContainer()),s=((e,t,o,n,s)=>{const r={};return r.height=IB(n+t.top,Xh(e),Yh(e)),o===TA.Both&&(r.width=IB(s+t.left,qh(e),Kh(e))),r})(e,t,o,It(n),Wt(n));le(s,((e,t)=>St(n,t,FB(e)))),(e=>{e.dispatch("ResizeEditor")})(e)},BA=(e,t,o,n)=>{const s=zt(20*o,20*n);return EA(e,s,t),B.some(!0)},MA=(e,t)=>({dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const o=(()=>{const o=[];return xf(e)&&o.push(_A(e,{},t)),e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>Ap.set(e,[Pa(t.translate(["{0} "+n,o[n]]))]);return Sh.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:il([Iv(t.isDisabled),Fv(),_x.config({}),Ap.config({}),Yd.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Dp("wordcount-events",[Hr((e=>{const t=Yd.getValue(e),n="words"===t.mode?"characters":"words";Yd.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),Vr((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=Yd.getValue(t);Yd.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[Js()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),wf(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=editor_referral&utm_medium=poweredby&utm_source=tinymce&utm_content=v6",rel:"noopener",target:"_blank","aria-label":Ch.translate(["Powered by {0}","Tiny"])},innerHtml:'<svg width="50px" height="16px" viewBox="0 0 50 16" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M10.143 0c2.608.015 5.186 2.178 5.186 5.331 0 0 .077 3.812-.084 4.87-.361 2.41-2.164 4.074-4.65 4.496-1.453.284-2.523.49-3.212.623-.373.071-.634.122-.785.152-.184.038-.997.145-1.35.145-2.732 0-5.21-2.04-5.248-5.33 0 0 0-3.514.03-4.442.093-2.4 1.758-4.342 4.926-4.963 0 0 3.875-.752 4.036-.782.368-.07.775-.1 1.15-.1Zm1.826 2.8L5.83 3.989v2.393l-2.455.475v5.968l6.137-1.189V9.243l2.456-.476V2.8ZM5.83 6.382l3.682-.713v3.574l-3.682.713V6.382Zm27.173-1.64-.084-1.066h-2.226v9.132h2.456V7.743c-.008-1.151.998-2.064 2.149-2.072 1.15-.008 1.987.92 1.995 2.072v5.065h2.455V7.359c-.015-2.18-1.657-3.929-3.837-3.913a3.993 3.993 0 0 0-2.908 1.296Zm-6.3-4.266L29.16 0v2.387l-2.456.475V.476Zm0 3.2v9.132h2.456V3.676h-2.456Zm18.179 11.787L49.11 3.676H46.58l-1.612 4.527-.46 1.382-.384-1.382-1.611-4.527H39.98l3.3 9.132L42.15 16l2.732-.537ZM22.867 9.738c0 .752.568 1.075.921 1.075.353 0 .668-.047.998-.154l.537 1.765c-.23.154-.92.537-2.225.537-1.305 0-2.655-.997-2.686-2.686a136.877 136.877 0 0 1 0-4.374H18.8V3.676h1.612v-1.98l2.455-.476v2.456h2.302V5.9h-2.302v3.837Z"/>\n</svg>\n'.trim()},behaviours:il([zp.config({})])}]}),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:o}]:[]})(),n=((e,t)=>{const o=(e=>{const t=Sf(e);return!1===t?TA.None:"both"===t?TA.Both:TA.Vertical})(e);return o===TA.None?B.none():B.some(Fh("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize")},behaviours:[wA.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>EA(e,s,o),blockerClass:"tox-blocker"}),Sp.config({mode:"special",onLeft:()=>BA(e,o,-1,0),onRight:()=>BA(e,o,1,0),onUp:()=>BA(e,o,0,-1),onDown:()=>BA(e,o,0,1)}),_x.config({}),zp.config({})]},t.icons))})(e,t);return o.concat(n.toArray())})()}),AA=e=>e.get().getOrDie("UI has not been rendered"),DA=e=>{const t=e.inline,o=t?PB:AB,n=Rf(e)?Z_:C_,s=Rl(),r=Rl(),a=Rl(),i=Rl(),l=wo().deviceType.isTouch()?["tox-platform-touch"]:[],c=Af(e),d=of(e),u=kh({dom:{tag:"div",classes:["tox-anchorbar"]}}),m=()=>r.get().bind(_E.getHeader),g=()=>$o.fromOption(s.get(),"UI has not been rendered"),p=()=>r.get().bind((e=>_E.getToolbar(e))).getOrDie("Could not find more toolbar element"),h=()=>r.get().bind((e=>_E.getThrobber(e))).getOrDie("Could not find throbber element"),f=((e,t,o)=>{const n=ps(!1),s=(e=>{const t=ps(Af(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),r={shared:{providers:{icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:Ch.translate,isDisabled:()=>t.mode.isReadOnly()||!t.ui.isEnabled(),getOption:t.options.get},interpreter:e=>((e,t,o)=>tO(QC,e,{},o))(e,0,r),anchors:_O(t,o,s.isPositionedAtTop),header:s,getSink:e},urlinput:d_(t),styles:PO(t),colorinput:AO(t),dialog:FO(t),isContextMenuOpen:()=>n.get(),setContextMenuState:e=>n.set(e)};return r})(g,e,(()=>r.get().bind((e=>u.getOpt(e))).getOrDie("Could not find a anchor bar element"))),b=t=>{const o=FB((e=>{const t=(e=>{const t=Gh(e),o=Xh(e),n=Yh(e);return DB(t).map((e=>IB(e,o,n)))})(e);return t.getOr(Gh(e))})(e)),n=FB((e=>VB(e).getOr($h(e)))(e));return e.inline||(Bt("div","width",n)&&St(t.element,"width",n),Bt("div","height",o)?St(t.element,"height",o):St(t.element,"height","400px")),o};return{getMothership:()=>AA(a),getUiMothership:()=>AA(i),backstage:f,renderUI:()=>{const{mothership:v,outerContainer:y}=(()=>{const o=(()=>{const t={attributes:{[oc]:c?tc.BottomToTop:tc.TopToBottom}},o=_E.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:f,onEscape:()=>{e.focus()}}),n=_E.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:g,providers:f.shared.providers,onEscape:()=>{e.focus()},type:d,lazyToolbar:p,lazyHeader:()=>m().getOrDie("Could not find header element"),...t}),s=_E.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:f.shared.providers,onEscape:()=>{e.focus()},type:d}),r=Mf(e),a=Ef(e),i=Of(e);return _E.parts.header({dom:{tag:"div",classes:["tox-editor-header"],...t},components:q([i?[o]:[],r?[s]:a?[n]:[],Ff(e)?[]:[u.asSpec()]]),sticky:Rf(e),editor:e,sharedBackstage:f.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[_E.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),_E.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=_E.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:f}),i=yf(e)&&!t?B.some(MA(e,f.shared.providers)):B.none(),h=q([c?[]:[o],t?[]:[n],c?[o]:[]]),b=q([[{dom:{tag:"div",classes:["tox-editor-container"]},components:h}],t?[]:i.toArray(),[s]]),v=Vf(e),y={role:"application",...Ch.isRtl()?{dir:"rtl"}:{},...v?{"aria-hidden":"true"}:{}},x=Ua(_E.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(t?["tox-tinymce-inline"]:[]).concat(c?["tox-tinymce--toolbar-bottom"]:[]).concat(l),styles:{visibility:"hidden",...v?{opacity:"0",border:"0"}:{}},attributes:y},components:b,behaviours:il([Fv(),bm.config({disableClass:"tox-tinymce--disabled"}),Sp.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),w=xx(x);return r.set(x),a.set(w),{mothership:w,outerContainer:x}})(),{uiMothership:x,sink:w}=(()=>{const t=If(e),o=je(ut(),t)&&"grid"===Ot(t,"display"),r={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(l),attributes:{...Ch.isRtl()?{dir:"rtl"}:{}}},behaviours:il([rd.config({useFixed:()=>n.isDocked(m)})])},a={dom:{styles:{width:document.body.clientWidth+"px"}},events:Cr([Tr(ir(),(e=>{St(e.element,"width",document.body.clientWidth+"px")}))])},c=Ua(nn(r,o?a:{})),d=xx(c);return s.set(c),i.set(d),{sink:c,uiMothership:d}})();ce(nf(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:S,menuItems:k,contextToolbars:C,sidebars:O}=e.ui.registry.getAll(),_=Bf(e),T={menuItems:k,menus:zf(e),menubar:df(e),toolbar:_.getOrThunk((()=>uf(e))),allowToolbarGroups:d===Rh.floating,buttons:S,sidebar:O};(t=>{e.addShortcut("alt+F9","focus menubar",(()=>{_E.focusMenubar(t)})),e.addShortcut("alt+F10","focus toolbar",(()=>{_E.focusToolbar(t)})),e.addCommand("ToggleToolbarDrawer",(()=>{_E.toggleToolbarDrawer(t)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>_E.isToolbarDrawerToggled(t)))})(y),((e,t,o)=>{const n=(e,n)=>{N([t,o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{N([t,o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(Ad(),{target:e.target}),a=Ro(),i=Hl(a,"touchstart",r),l=Hl(a,"touchmove",(e=>n(sr(),e))),c=Hl(a,"touchend",(e=>n(rr(),e))),d=Hl(a,"mousedown",r),u=Hl(a,"mouseup",(e=>{0===e.raw.button&&s(Fd(),{target:e.target})})),m=e=>s(Ad(),{target:Fe(e.target)}),g=e=>{0===e.button&&s(Fd(),{target:Fe(e.target)})},p=()=>{N(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(ar(),Nl(e)),f=e=>{s(Dd(),{}),n(ir(),Nl(e))},b=()=>s(Dd(),{}),v=t=>{t.state&&s(Ad(),{target:Fe(e.getContainer())})},y=e=>{s(Ad(),{target:Fe(e.relatedTarget.getContainer())})};e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",b),e.on("AfterProgressState",v),e.on("DismissPopups",y)})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",b),e.off("AfterProgressState",v),e.off("DismissPopups",y),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind()})),e.on("detach",(()=>{yd(t),yd(o),t.destroy(),o.destroy()}))})(e,v,x),n.setup(e,f.shared,m),hM(e,f),RM(e,g,f),(e=>{const{sidebars:t}=e.ui.registry.getAll();N(ae(t),(o=>{const n=t[o],s=()=>ye(B.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}}})}))})(e),PT(e,h,f.shared),sM(e,C,w,{backstage:f}),OA(e,w);const E={mothership:v,uiMothership:x,outerContainer:y,sink:w},M={targetNode:e.getElement(),height:b(y)};return o.render(e,E,T,f,M)}}},FA=y([Un("lazySink"),Zn("dragBlockClass"),us("getBounds",Uo),rs("useTabstopAt",T),rs("eventOrder",{}),Jd("modalBehaviours",[Sp]),fi("onExecute"),vi("onEscape")]),IA={sketch:x},VA=y([_u({name:"draghandle",overrides:(e,t)=>({behaviours:il([wA.config({mode:"mouse",getTarget:e=>Ya(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),Cu({schema:[Un("dom")],name:"title"}),Cu({factory:IA,schema:[Un("dom")],name:"close"}),Cu({factory:IA,schema:[Un("dom")],name:"body"}),_u({factory:IA,schema:[Un("dom")],name:"footer"}),Ou({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[rs("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),rs("components",[])],name:"blocker"})]),RA=Qu({name:"ModalDialog",configFields:FA(),partFields:VA(),factory:(e,t,o,n)=>{const s=Rl(),r=qr("modal-events"),a={...e.eventOrder,[lr()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])};return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([ja(t)]),behaviours:il([zp.config({}),Dp("dialog-blocker-events",[Ir(Is(),(()=>{Sp.focusIn(t)}))])])});md(o,a),Sp.focusIn(t)},hide:e=>{s.clear(),Ye(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{hd(e)}))}))},getBody:t=>Pu(t,e,"body"),getFooter:t=>Pu(t,e,"footer"),setIdle:e=>{RT.unblock(e)},setBusy:(e,t)=>{RT.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Qd(e.modalBehaviours,[Ap.config({}),Sp.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt}),RT.config({getRoot:s.get}),Dp(r,[Vr((t=>{((e,t)=>{const o=bt(e,"id").fold((()=>{const e=qr("dialog-label");return pt(t,"id",e),e}),x);pt(e,"aria-labelledby",o)})(t.element,Pu(t,e,"title").element),((e,t)=>{const o=B.from(ft(e,"id")).fold((()=>{const e=qr("dialog-describe");return pt(t,"id",e),e}),x);pt(e,"aria-describedby",o)})(t.element,Pu(t,e,"body").element)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),zA=yn([gb,pb].concat(cv)),HA=En,PA=[Nb("button"),Tb,cs("align","end",["start","end"]),Vb,Ib],NA=[...PA,fb],LA=[qn("type",["submit","cancel","custom"]),...NA],WA=[qn("type",["menu"]),_b,Eb,Tb,Jn("items",zA),...PA],UA=Pn("type",{submit:LA,cancel:LA,custom:LA,menu:WA}),jA=[gb,fb,qn("level",["info","warn","error","success"]),vb,rs("url","")],GA=yn(jA),$A=[gb,fb,Ib,Nb("button"),Tb,Fb,Qn("buttonType",Wn(["primary","secondary","toolbar"])),Vb],qA=yn($A),XA=[gb,pb],KA=XA.concat([Bb]),YA=XA.concat([hb,Ib]),JA=yn(YA),ZA=En,QA=KA.concat([Rb("auto")]),eD=yn(QA),tD=kn([yb,fb,vb]),oD=yn(KA),nD=Tn,sD=yn(KA),rD=Tn,aD=XA.concat([ls("tag","textarea"),$n("scriptId"),$n("scriptUrl"),as("settings",void 0,An)]),iD=XA.concat([ls("tag","textarea"),Xn("init")]),lD=Fn((e=>Vn("customeditor.old",vn(iD),e).orThunk((()=>Vn("customeditor.new",vn(aD),e))))),cD=Tn,dD=yn(KA),uD=xn(gn),mD=e=>[gb,Gn("columns"),e],gD=[gb,$n("html"),cs("presets","presentation",["presentation","document"])],pD=yn(gD),hD=KA.concat([ds("sandboxed",!0)]),fD=yn(hD),bD=Tn,vD=yn(XA.concat([ts("height")])),yD=yn([$n("url"),es("zoom"),es("cachedWidth"),es("cachedHeight")]),xD=KA.concat([ts("inputMode"),ts("placeholder"),ds("maximized",!1),Ib]),wD=yn(xD),SD=Tn,kD=e=>[gb,hb,e],CD=[fb,yb],OD=[fb,Jn("items",((e,t)=>{const o=jt(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,(()=>_D)))],_D=wn([yn(CD),yn(OD)]),TD=KA.concat([Jn("items",_D),Ib]),ED=yn(TD),BD=Tn,MD=KA.concat([Yn("items",[fb,yb]),is("size",1),Ib]),AD=yn(MD),DD=Tn,FD=KA.concat([ds("constrain",!0),Ib]),ID=yn(FD),VD=yn([$n("width"),$n("height")]),RD=XA.concat([hb,is("min",0),is("max",0)]),zD=yn(RD),HD=_n,PD=[gb,Jn("header",Tn),Jn("cells",xn(Tn))],ND=yn(PD),LD=KA.concat([ts("placeholder"),ds("maximized",!1),Ib]),WD=yn(LD),UD=Tn,jD=KA.concat([cs("filetype","file",["image","media","file"]),Ib]),GD=yn(jD),$D=yn([yb,zb]),qD=e=>Nn("items","items",{tag:"required",process:{}},xn(Fn((t=>Vn(`Checking item of ${e}`,XD,t).fold((e=>$o.error(Hn(e))),(e=>$o.value(e))))))),XD=fn((()=>{return Dn("type",{alertbanner:GA,bar:yn((e=qD("bar"),[gb,e])),button:qA,checkbox:JA,colorinput:oD,colorpicker:sD,dropzone:dD,grid:yn(mD(qD("grid"))),iframe:fD,input:wD,listbox:ED,selectbox:AD,sizeinput:ID,slider:zD,textarea:WD,urlinput:GD,customeditor:lD,htmlpanel:pD,imagepreview:vD,collection:eD,label:yn(kD(qD("label"))),table:ND,panel:YD});var e})),KD=[gb,rs("classes",[]),Jn("items",XD)],YD=yn(KD),JD=[Nb("tab"),bb,Jn("items",XD)],ZD=[gb,Yn("tabs",JD)],QD=yn(ZD),eF=NA,tF=UA,oF=yn([$n("title"),jn("body",Dn("type",{panel:YD,tabpanel:QD})),ls("size","normal"),Jn("buttons",tF),rs("initialData",{}),us("onAction",b),us("onChange",b),us("onSubmit",b),us("onClose",b),us("onCancel",b),us("onTabChange",b)]),nF=yn([qn("type",["cancel","custom"]),...eF]),sF=yn([$n("title"),$n("url"),es("height"),es("width"),ns("buttons",nF),us("onAction",b),us("onCancel",b),us("onClose",b),us("onMessage",b)]),rF=e=>a(e)?[e].concat(X(he(e),rF)):l(e)?X(e,rF):[],aF=e=>r(e.type)&&r(e.name),iF={checkbox:ZA,colorinput:nD,colorpicker:rD,dropzone:uD,input:SD,iframe:bD,imagepreview:yD,selectbox:DD,sizeinput:VD,slider:HD,listbox:BD,size:VD,textarea:UD,urlinput:$D,customeditor:cD,collection:tD,togglemenuitem:HA},lF=e=>{const t=(e=>W(rF(e),aF))(e),o=X(t,(e=>(e=>B.from(iF[e.type]))(e).fold((()=>[]),(t=>[jn(e.name,t)]))));return yn(o)},cF=e=>({internalDialog:Rn(Vn("dialog",oF,e)),dataValidator:lF(e),initialData:e.initialData}),dF={open:(e,t)=>{const o=cF(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(Rn(Vn("dialog",sF,t))),redial:e=>cF(e)},uF=e=>{const t=[],o={};return le(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?$o.error(t):$o.value(o)},mF=(e,t,o)=>{const n=kh(bk.sketch((n=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:P(e.items,(e=>eO(n,e,t,o)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[n.asSpec()]}],behaviours:il([Sp.config({mode:"acyclic",useTabstopAt:k(Wk)}),(s=n,nm.config({find:s.getOpt})),Bk(n,{postprocess:e=>uF(e).fold((e=>(console.error(e),{})),x)})])};var s},gF=Zu({name:"TabButton",configFields:[rs("uid",void 0),Un("value"),Nn("dom","dom",ln((()=>({attributes:{role:"tab",id:qr("aria"),"aria-selected":"false"}}))),Cn()),Zn("action"),rs("domModification",{}),Jd("tabButtonBehaviours",[zp,Sp,Yd]),Un("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:Jp(e.action),behaviours:Qd(e.tabButtonBehaviours,[zp.config({}),Sp.config({mode:"execution",useSpace:!0,useEnter:!0}),Yd.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),pF=y([Un("tabs"),Un("dom"),rs("clickToDismiss",!1),Jd("tabbarBehaviours",[Tm,Sp]),gi(["tabClass","selectedClass"])]),hF=Tu({factory:gF,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Tm.dehighlight(e,t),yr(e,hr(),{tabbar:e,button:t})},o=(e,t)=>{Tm.highlight(e,t),yr(e,pr(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Tm.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),fF=y([hF]),bF=Qu({name:"Tabbar",configFields:pF(),partFields:fF(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Qd(e.tabbarBehaviours,[Tm.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{pt(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{pt(t.element,"aria-selected","false")}}),Sp.config({mode:"flow",getInitial:e=>Tm.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),vF=Zu({name:"Tabview",configFields:[Jd("tabviewBehaviours",[Ap])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:Qd(e.tabviewBehaviours,[Ap.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),yF=y([rs("selectFirst",!0),hi("onChangeTab"),hi("onDismissTab"),rs("tabs",[]),Jd("tabSectionBehaviours",[])]),xF=Cu({factory:bF,schema:[Un("dom"),Kn("markers",[Un("tabClass"),Un("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),wF=Cu({factory:vF,name:"tabview"}),SF=y([xF,wF]),kF=Qu({name:"TabSection",configFields:yF(),partFields:SF(),factory:(e,t,o,n)=>{const s=(t,o)=>{Hu(t,e,"tabbar").each((e=>{o(e).each(xr)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:Zd(e.tabSectionBehaviours),events:Cr(q([e.selectFirst?[Vr(((e,t)=>{s(e,Tm.getFirst)}))]:[],[Tr(pr(),((t,o)=>{(t=>{const o=Yd.getValue(t);Hu(t,e,"tabview").each((n=>{G(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();bt(t.element,"id").each((e=>{pt(n.element,"aria-labelledby",e)})),Ap.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),Tr(hr(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>Hu(t,e,"tabview").map((e=>Ap.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Tm.getCandidates(e);return G(o,(e=>Yd.getValue(e)===t)).filter((t=>!Tm.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),CF=(e,t)=>{St(e,"height",t+"px"),St(e,"flex-basis",t+"px")},OF=(e,t,o)=>{Ya(e,'[role="dialog"]').each((e=>{Za(e,'[role="tablist"]').each((n=>{o.get().map((o=>(St(t,"height","0"),St(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=Xe(e).dom,s=Ya(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===Ot(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=It(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+Wt(o)?Math.max(It(o),a):a,l=parseInt(Ot(e,"margin-top"),10)||0,c=parseInt(Ot(e,"margin-bottom"),10)||0;return r-(It(e)+l+c-i)})(e,t,n))))).each((e=>{CF(t,e)}))}))}))},_F=e=>Za(e,'[role="tabpanel"]'),TF="send-data-to-section",EF="send-data-to-view",BF=(e,t,o)=>{const n=ps({}),s=e=>{const t=Yd.getValue(e),o=uF(t).getOr({}),s=n.get(),r=nn(s,o);n.set(r)},r=e=>{const t=n.get();Yd.setValue(e,t)},a=ps(null),i=P(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[Pa(o.shared.providers.translate(e.title))],view:()=>[bk.sketch((n=>({dom:{tag:"div",classes:["tox-form"]},components:P(e.items,(e=>eO(n,e,t,o))),formBehaviours:il([Sp.config({mode:"acyclic",useTabstopAt:k(Wk)}),Dp("TabView.form.events",[Vr(r),Rr(s)]),gl.config({channels:bs([{key:TF,value:{onReceive:s}},{key:EF,value:{onReceive:r}}])})])})))]}))),l=(e=>{const t=(()=>{const t=Rl(),o=[Vr((o=>{const n=o.element;_F(n).each((s=>{St(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>P(e,((n,s)=>{Ap.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return Ap.set(o,[]),r.height})))(e,s,o),r=(e=>oe(ee(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),OF(n,s,t),Mt(s,"visibility"),((e,t)=>{oe(e).each((e=>kF.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{OF(n,s,t)}))}))})),Tr(ir(),(e=>{const o=e.element;_F(o).each((e=>{OF(o,e,t)}))})),Tr(Nx,((e,o)=>{const n=e.element;_F(n).each((e=>{const o=bl(at(e));St(e,"visibility","hidden");const s=Tt(e,"height").map((e=>parseInt(e,10)));Mt(e,"height"),Mt(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),OF(n,e,t)):s.each((t=>{CF(e,t)})),Mt(e,"visibility"),o.each(hl)}))}))];return{extraEvents:o,selectFirst:!1}})();return{smartTabHeight:t,naiveTabHeight:{extraEvents:[],selectFirst:!0}}})(i).smartTabHeight;return kF.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=Yd.getValue(t);yr(e,Px,{name:n,oldName:a.get()}),a.set(n)},tabs:i,components:[kF.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[bF.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:il([_x.config({})])}),kF.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:l.selectFirst,tabSectionBehaviours:il([Dp("tabpanel",l.extraEvents),Sp.config({mode:"acyclic"}),nm.config({find:e=>oe(kF.getViewItems(e))}),Ak(B.none(),(e=>(e.getSystem().broadcastOn([TF],{}),n.get())),((e,t)=>{n.set(t),e.getSystem().broadcastOn([EF],{})}))])})},MF=qr("update-dialog"),AF=qr("update-title"),DF=qr("update-body"),FF=qr("update-footer"),IF=qr("body-send-message"),VF=(e,t,o,n,s)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:il([Ok(0),lB.config({channel:`${DF}-${t}`,updateState:(e,t)=>B.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[BF(t,e.initialData,n)]:[mF(t,e.initialData,n)]},initialData:e})])}),RF=Nh.deviceType.isTouch(),zF=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),HF=(e,t)=>RA.parts.close(Sh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:il([_x.config({})])})),PF=()=>RA.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),NF=(e,t)=>RA.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:zT(`<p>${t.translate(e)}</p>`)}]}]}),LF=e=>RA.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),WF=(e,t)=>[yx.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),yx.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],UF=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return RA.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),B.some(!0)),useTabstopAt:e=>!Wk(e),dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:zT(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:RF?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:il([zp.config({}),Dp("dialog-events",e.dialogEvents.concat([Ir(Is(),((e,t)=>{Sp.focusIn(e)}))])),Dp("scroll-lock",[Vr((()=>{Oa(ut(),s)})),Rr((()=>{_a(ut(),s)}))]),...e.extraBehaviours]),eventOrder:{[Js()]:["dialog-events"],[lr()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[cr()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},jF=e=>Sh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),title:e.translate("Close")}},components:[Fh("close",{tag:"div",classes:["tox-icon"]},e.icons)],action:e=>{vr(e,Ix)}}),GF=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:il([lB.config({channel:`${AF}-${t}`,initialData:e,renderComponents:e=>[Pa(n.translate(e.title))]})])}),$F=()=>({dom:zT('<div class="tox-dialog__draghandle"></div>')}),qF=(e,t,o)=>((e,t,o)=>{const n=RA.parts.title(GF(e,t,B.none(),o)),s=RA.parts.draghandle($F()),r=RA.parts.close(jF(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return yx.sketch({dom:zT('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),XF=(e,t,o)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:t,components:[{dom:zT('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),KF=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{RA.setBusy(e(),((e,n)=>XF(o.message,n,t)))},onUnblock:()=>{RA.setIdle(e())}}),YF=(e,t,o,n)=>Ua(UF({...e,lazySink:n.shared.getSink,extraBehaviours:[lB.config({channel:`${MF}-${e.id}`,updateState:(e,t)=>B.some(t),initialData:t}),Dk({}),...e.extraBehaviours],onEscape:e=>{vr(e,Ix)},dialogEvents:o,eventOrder:{[Ys()]:[lB.name(),gl.name()],[lr()]:["scroll-lock",lB.name(),"messages","dialog-events","alloy.base.behaviour"],[cr()]:["alloy.base.behaviour","dialog-events","messages",lB.name(),"scroll-lock"]}})),JF=e=>P(e,(e=>"menu"===e.type?(e=>{const t=P(e.items,(e=>({...e,storage:ps(!1)})));return{...e,items:t}})(e):e)),ZF=e=>j(e,((e,t)=>"menu"===t.type?j(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),QF=(e,t)=>[Ar(Is(),Lk),e(Fx,((e,o)=>{t.onClose(),o.onClose()})),e(Ix,((e,t,o,n)=>{t.onCancel(e),vr(n,Fx)})),Tr(Hx,((e,o)=>t.onUnblock())),Tr(zx,((e,o)=>t.onBlock(o.event)))],eI=(e,t,o)=>{const n=(t,o)=>Tr(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{lB.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...QF(n,t),n(Rx,((e,t)=>t.onSubmit(e))),n(Dx,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(Vx,((e,t,n,s)=>{const r=()=>Sp.focusIn(s),a=e=>vt(e,"disabled")||bt(e,"aria-disabled").exists((e=>"true"===e)),i=at(s.element),l=bl(i);t.onAction(e,{name:n.name,value:n.value}),bl(i).fold(r,(e=>{a(e)||l.exists((t=>Ge(e,t)&&a(t)))?r():o().toOptional().filter((t=>!Ge(t.element,e))).each(r)}))})),n(Px,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),Rr((t=>{const o=e();Yd.setValue(t,o.getData())}))]},tI=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=L(o,(e=>"start"===e.align)),s=(e,t)=>yx.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:P(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},oI=(e,t,o)=>({dom:zT('<div class="tox-dialog__footer"></div>'),components:[],behaviours:il([lB.config({channel:`${FF}-${t}`,initialData:e,updateState:(e,t)=>{const n=P(t.buttons,(e=>{const t=kh(((e,t)=>HC(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return B.some({lookupByName:t=>((e,t,o)=>G(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:tI})])}),nI=(e,t,o)=>RA.parts.footer(oI(e,t,o)),sI=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=nm.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return bk.getField(o,t).orThunk((()=>{const o=e.getFooter();return lB.getState(o).get().bind((e=>e.lookupByName(t)))}))}return B.none()},rI=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...Yd.getValue(n),...ce(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=nn(r,t),i=((e,t)=>{const o=e.getRoot();return lB.getState(o).get().map((e=>Rn(Vn("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();Yd.setValue(l,i),le(o,((e,t)=>{be(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{sI(e,t).each(o?bm.enable:bm.disable)},focus:t=>{sI(e,t).each(zp.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{yr(t,zx,{message:e})}))},unblock:()=>{n((e=>{vr(e,Hx)}))},showTab:t=>{n((o=>{const n=e.getBody();lB.getState(n).get().exists((e=>e.isTabPanel()))&&nm.getCurrent(n).each((e=>{kF.showTab(e,t)}))}))},redial:o=>{n((n=>{const r=e.getId(),a=t(o);n.getSystem().broadcastOn([`${MF}-${r}`],a),n.getSystem().broadcastOn([`${AF}-${r}`],a.internalDialog),n.getSystem().broadcastOn([`${DF}-${r}`],a.internalDialog),n.getSystem().broadcastOn([`${FF}-${r}`],a.internalDialog),s.setData(a.initialData)}))},close:()=>{n((e=>{vr(e,Fx)}))}};return s};var aI=tinymce.util.Tools.resolve("tinymce.util.URI");const iI=["insertContent","setContent","execCommand","close","block","unblock"],lI=e=>a(e)&&-1!==iI.indexOf(e.mceAction),cI=(e,t,o,n)=>{const s=qr("dialog"),i=qF(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[Pk({dom:{tag:"iframe",attributes:{src:e.url}},behaviours:il([_x.config({}),zp.config({})])})]}],behaviours:il([Sp.config({mode:"acyclic",useTabstopAt:k(Wk)})])};return RA.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?B.none():B.some(nI({buttons:e},s,n)))),u=((e,t)=>{const o=(t,o)=>Tr(t,((t,s)=>{n(t,((n,r)=>{o(e(),n,s.event,t)}))})),n=(e,t)=>{lB.getState(e).get().each((o=>{t(o,e)}))};return[...QF(o,t),o(Vx,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})((()=>y),KF((()=>v),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},g=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],p=new aI(e.url,{base_uri:new aI(window.location.href)}),h=`${p.protocol}://${p.host}${p.port?":"+p.port:""}`,f=Vl(),b=[Dp("messages",[Vr((()=>{const t=Hl(Fe(window),"message",(t=>{if(p.isSameOrigin(new aI(t.raw.origin))){const n=t.raw.data;lI(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,y,n):(e=>!lI(e)&&a(e)&&be(e,"mceAction"))(n)&&e.onMessage(y,n)}}));f.set(t)})),Rr(f.clear)]),gl.config({channels:{[IF]:{onReceive:(e,t)=>{Za(e.element,"iframe").each((e=>{e.dom.contentWindow.postMessage(t,h)}))}}}})],v=YF({id:s,header:i,body:l,footer:c,extraClasses:g,extraBehaviours:b,extraStyles:m},e,u,n),y=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{yr(t,zx,{message:e})}))},unblock:()=>{t((e=>{vr(e,Hx)}))},close:()=>{t((e=>{vr(e,Fx)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([IF],e)}))}}})(v);return{dialog:v,instanceApi:y}},dI=(e,t,o)=>t&&o?[]:[W_.config({contextual:{lazyContext:()=>B.some(Lo(Fe(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})],uI=e=>{const t=e.backstage,o=e.editor,n=Rf(o),s=(e=>{const t=e.backstage.shared;return{open:(o,n)=>{const s=()=>{RA.hide(l),n()},r=kh(HC({name:"close-alert",text:"OK",primary:!0,align:"end",enabled:!0,icon:B.none()},"cancel",e.backstage)),a=PF(),i=HF(s,t.providers),l=Ua(UF({lazySink:()=>t.getSink(),header:zF(a,i),body:NF(o,t.providers),footer:B.some(LF(WF([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Tr(Ix,s)],eventOrder:{}}));RA.show(l);const c=r.get(l);zp.focus(c)}}})(e),r=(e=>{const t=e.backstage.shared;return{open:(o,n)=>{const s=e=>{RA.hide(c),n(e)},r=kh(HC({name:"yes",text:"Yes",primary:!0,align:"end",enabled:!0,icon:B.none()},"submit",e.backstage)),a=HC({name:"no",text:"No",primary:!1,align:"end",enabled:!0,icon:B.none()},"cancel",e.backstage),i=PF(),l=HF((()=>s(!1)),t.providers),c=Ua(UF({lazySink:()=>t.getSink(),header:zF(i,l),body:NF(o,t.providers),footer:B.some(LF(WF([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Tr(Ix,(()=>s(!1))),Tr(Rx,(()=>s(!0)))],eventOrder:{}}));RA.show(c);const d=r.get(c);zp.focus(d)}}})(e),a=(e,o)=>dF.open(((e,n,s)=>{const r=n,a=((e,t,o)=>{const n=qr("dialog"),s=e.internalDialog,r=qF(s.title,n,o),a=((e,t,o)=>{const n=VF(e,t,B.none(),o,!1);return RA.parts.body(n)})({body:s.body,initialData:s.initialData},n,o),i=JF(s.buttons),l=ZF(i),c=nI({buttons:i},n,o),d=eI((()=>h),KF((()=>g),o.shared.providers,t),o.shared.getSink),u=(e=>{switch(e){case"large":return["tox-dialog--width-lg"];case"medium":return["tox-dialog--width-md"];default:return[]}})(s.size),m={id:n,header:r,body:a,footer:B.some(c),extraClasses:u,extraBehaviours:[],extraStyles:{}},g=YF(m,e,d,o),p={getId:y(n),getRoot:y(g),getBody:()=>RA.getBody(g),getFooter:()=>RA.getFooter(g),getFormWrapper:()=>{const e=RA.getBody(g);return nm.getCurrent(e).getOr(e)}},h=rI(p,t.redial,l);return{dialog:g,instanceApi:h}})({dataValidator:s,initialData:r,internalDialog:e},{redial:dF.redial,closeWindow:()=>{RA.hide(a.dialog),o(a.instanceApi)}},t);return RA.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),e),i=(e,s,r,a)=>dF.open(((e,i,l)=>{const c=Rn(Vn("data",l,i)),d=Rl(),u=t.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{xh.reposition(e),W_.refresh(e)})),g=((e,t,o,n)=>{const s=qr("dialog"),r=qr("dialog-label"),a=qr("dialog-content"),i=e.internalDialog,l=kh(((e,t,o,n)=>yx.sketch({dom:zT('<div class="tox-dialog__header"></div>'),components:[GF(e,t,B.some(o),n),$F(),jF(n)],containerBehaviours:il([wA.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>Qa(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))({title:i.title,draggable:!0},s,r,o.shared.providers)),c=kh(((e,t,o,n,s)=>VF(e,t,B.some(o),n,s))({body:i.body,initialData:i.initialData},s,a,o,n)),d=JF(i.buttons),u=ZF(d),m=kh(((e,t,o)=>oI(e,t,o))({buttons:d},s,o)),g=eI((()=>h),{onBlock:e=>{RT.block(p,((t,n)=>XF(e.message,n,o.shared.providers)))},onUnblock:()=>{RT.unblock(p)},onClose:()=>t.closeWindow()},o.shared.getSink),p=Ua({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:{role:"dialog","aria-labelledby":r,"aria-describedby":a}},eventOrder:{[Ys()]:[lB.name(),gl.name()],[Js()]:["execute-on-form"],[lr()]:["reflecting","execute-on-form"]},behaviours:il([Sp.config({mode:"cyclic",onEscape:e=>(vr(e,Fx),B.some(!0)),useTabstopAt:e=>!Wk(e)&&("button"!==Ve(e)||"disabled"!==ft(e,"disabled"))}),lB.config({channel:`${MF}-${s}`,updateState:(e,t)=>B.some(t),initialData:e}),zp.config({}),Dp("execute-on-form",g.concat([Ir(Is(),((e,t)=>{Sp.focusIn(e)}))])),RT.config({getRoot:()=>B.some(p)}),Ap.config({}),Dk({})]),components:[l.asSpec(),c.asSpec(),m.asSpec()]}),h=rI({getId:y(s),getRoot:y(p),getFooter:()=>m.get(p),getBody:()=>c.get(p),getFormWrapper:()=>{const e=c.get(p);return nm.getCurrent(e).getOr(e)}},t.redial,u);return{dialog:p,instanceApi:h}})({dataValidator:l,initialData:c,internalDialog:e},{redial:dF.redial,closeWindow:()=>{d.on(xh.hide),o.off("ResizeEditor",m),d.clear(),r(g.instanceApi)}},t,a),p=Ua(xh.sketch({lazySink:t.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:il([Dp("window-manager-inline-events",[Tr(dr(),((e,t)=>{vr(g.dialog,Ix)}))]),...dI(o,n,u)]),isExtraPart:(e,t)=>(e=>cx(e,".tox-alert-dialog")||cx(e,".tox-confirm-dialog"))(t)}));return d.set(p),xh.showWithin(p,ja(g.dialog),{anchor:s},B.some(ut())),n&&u||(W_.refresh(p),o.on("ResizeEditor",m)),g.instanceApi.setData(c),Sp.focusIn(g.dialog),g.instanceApi}),e);return{open:(e,o,n)=>void 0!==o&&"toolbar"===o.inline?i(e,t.shared.anchors.inlineDialog(),n,o.ariaAttrs):void 0!==o&&"cursor"===o.inline?i(e,t.shared.anchors.cursor(),n,o.ariaAttrs):a(e,n),openUrl:(e,n)=>((e,n)=>dF.openUrl((e=>{const s=cI(e,{closeWindow:()=>{RA.hide(s.dialog),n(s.instanceApi)}},o,t);return RA.show(s.dialog),s.instanceApi}),e))(e,n),alert:(e,t)=>{s.open(e,(()=>{t()}))},close:e=>{e.close()},confirm:(e,t)=>{r.open(e,(e=>{t(e)}))}}};E.add("silver",(e=>{(e=>{Uh(e),(e=>{const t=e.options.register;var o;t("color_map",{processor:e=>f(e,r)?{value:Dy(e),valid:!0}:{valid:!1,message:"Must be an array of strings."},default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_cols",{processor:"number",default:(o=Ry(e).length,Math.max(5,Math.ceil(Math.sqrt(o))))}),t("custom_colors",{processor:"boolean",default:!0})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:fM(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)})(e);const{getUiMothership:t,backstage:o,renderUI:n}=DA(e);lx(e,o.shared);const s=uI({editor:e,backstage:o});return{renderUI:n,getWindowManagerImpl:y(s),getNotificationManagerImpl:()=>((e,t,o)=>{const n=t.backstage.shared,s=()=>{const t=Lo(Fe(e.getContentAreaContainer())),o=Uo(),n=Fi(o.x,t.x,t.right),s=Fi(o.y,t.y,t.bottom),r=Math.max(t.right,o.right),a=Math.max(t.bottom,o.bottom);return B.some(No(n,s,r-n,a-s))};return{open:(t,r)=>{const a=()=>{r(),xh.hide(l)},i=Ua(Vh.sketch({text:t.text,level:V(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:B.from(t.icon),closeButton:t.closeButton,onAction:a,iconProvider:n.providers.icons,translationProvider:n.providers.translate})),l=Ua(xh.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:n.getSink,fireDismissalEventInstead:{},...n.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}}}));o.add(l),t.timeout>0&&wh.setEditorTimeout(e,(()=>{a()}),t.timeout);const c={close:a,reposition:()=>{const t=ja(i),o={maxHeightFunction:$l()},r=e.notificationManager.getNotifications();if(r[0]===c){const e={...n.anchors.banner(),overrides:o};xh.showWithinBounds(l,t,{anchor:e},s)}else I(r,c).each((e=>{const n=r[e-1].getEl(),a={type:"node",root:ut(),node:B.some(Fe(n)),overrides:o,layouts:{onRtl:()=>[qi],onLtr:()=>[qi]}};xh.showWithinBounds(l,t,{anchor:a},s)}))},text:e=>{Vh.updateText(i,e)},settings:t,getEl:()=>i.element.dom,progressBar:{value:e=>{Vh.updateProgress(i,e)}}};return c},close:e=>{e.close()},getArgs:e=>e.settings}})(e,{backstage:o},t())}}))}();