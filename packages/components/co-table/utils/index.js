/**
 * Co-Table 组件工具类
 *
 * 提供表格组件所需的各种工具方法，包括：
 * - 数据类型判断
 * - 对象深度合并
 * - UUID生成
 * - 深度克隆
 * - 文件下载
 * - 时间格式化
 * - 对象判空
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */
export default class Utils {
	/**
	 * 精确判断JavaScript数据类型
	 * 使用Object.prototype.toString方法获取准确的类型信息
	 *
	 * @param {any} value - 需要判断类型的数据
	 * @returns {string} 具体的类型名称，如：'Array'、'Object'、'String'等
	 *
	 * @example
	 * Utils.getType([1, 2, 3])     // 返回 'Array'
	 * Utils.getType({a: 1})        // 返回 'Object'
	 * Utils.getType('hello')       // 返回 'String'
	 * Utils.getType(async () => {}) // 返回 'AsyncFunction'
	 */
	static getType(value) {
		// 获取对象的原始类型字符串，如 '[object Array]'
		const type = Object.prototype.toString.call(value);

		// 通过映射表将类型字符串转换为简洁的类型名称
		return {
			'[object Array]': 'Array',           // 数组类型
			'[object Object]': 'Object',         // 普通对象类型
			'[object String]': 'String',         // 字符串类型
			'[object Number]': 'Number',         // 数字类型
			'[object Boolean]': 'Boolean',       // 布尔类型
			'[object Function]': 'Function',     // 普通函数类型
			'[object AsyncFunction]': 'AsyncFunction', // 异步函数类型
			'[object Promise]': 'Promise',       // Promise对象类型
		}[type];
	}
	/**
	 * 深度合并两个对象（会修改第一个对象）
	 * 递归合并对象的所有层级属性，obj2的属性会覆盖obj1的同名属性
	 *
	 * @param {object} obj1 - 目标对象（会被修改）
	 * @param {object} obj2 - 源对象（提供新属性值）
	 * @returns {object} 合并后的对象（实际上是修改后的obj1）
	 *
	 * @example
	 * const target = { a: 1, b: { c: 2 } };
	 * const source = { b: { d: 3 }, e: 4 };
	 * Utils.deepMerge(target, source);
	 * // target 变为 { a: 1, b: { c: 2, d: 3 }, e: 4 }
	 */
	static deepMerge(obj1, obj2) {
		let key;
		// 遍历源对象的所有属性
		for (key in obj2) {
			// 如果目标对象的当前属性也是对象，则递归合并
			// 否则直接用源对象的属性值覆盖目标对象的属性值
			obj1[key] = obj1[key] && obj1[key].toString() === '[object Object]'
				? this.deepMerge(obj1[key], obj2[key])  // 递归合并子对象
				: (obj1[key] = obj2[key]);              // 直接赋值覆盖
		}
		return obj1;
	}

	/**
	 * 深度合并多个对象（不修改原对象）
	 * 创建新对象来存储合并结果，不会修改任何输入对象
	 *
	 * @param {...object} objects - 要合并的多个对象
	 * @returns {object} 新的合并后的对象
	 *
	 * @example
	 * const obj1 = { a: 1, b: { c: 2 } };
	 * const obj2 = { b: { d: 3 }, e: 4 };
	 * const obj3 = { f: 5 };
	 * const merged = Utils.deepMerge2(obj1, obj2, obj3);
	 * // merged 为 { a: 1, b: { c: 2, d: 3 }, e: 4, f: 5 }
	 * // obj1, obj2, obj3 保持不变
	 */
	static deepMerge2(...objects) {
		// 创建新的结果对象
		const result = {};

		// 遍历所有输入对象
		for (const obj of objects) {
			// 遍历当前对象的所有属性
			for (const key in obj) {
				if (typeof obj[key] === 'object') {
					// 如果当前属性值是对象，则递归合并
					result[key] = this.deepMerge2(result[key], obj[key]);
				} else {
					// 如果是基本类型，直接赋值
					result[key] = obj[key];
				}
			}
		}
		return result;
	}
	/**
	 * 生成简单的UUID（伪随机数）
	 * 注意：这不是标准的UUID，仅用于生成表格行的唯一标识
	 *
	 * @returns {number} 生成的伪随机数字ID
	 *
	 * @example
	 * const id = Utils.uuid(); // 可能返回 123456
	 *
	 * @todo 建议使用更标准的UUID生成算法，如crypto.randomUUID()
	 */
	static uuid = function () {
		// 通过三个随机数相加生成一个较大的随机整数
		// 范围大约在 0 到 125000 之间
		return Math.floor(Math.random() * 100000 + Math.random() * 20000 + Math.random() * 5000);
	};

	/**
	 * 深度克隆对象或数组
	 * 递归复制对象的所有层级，创建完全独立的副本
	 *
	 * @param {*} origin - 要克隆的原始数据（可以是任意类型）
	 * @returns {*} 克隆后的新数据，与原数据完全独立
	 *
	 * @example
	 * const original = { a: 1, b: { c: [1, 2, 3] } };
	 * const cloned = Utils.deepClone(original);
	 * cloned.b.c.push(4); // 不会影响 original
	 */
	static deepClone = function (origin) {
		// 如果不是对象类型或者是null，直接返回原值
		if (typeof origin !== 'object' || origin == null) {
			return origin;
		}

		// 根据原数据类型创建对应的容器（数组或对象）
		const result = Array.isArray(origin) ? [] : {};

		// 遍历原对象的所有自有属性
		for (const key in origin) {
			// 确保只处理对象自身的属性，不包括继承的属性
			if (Object.prototype.hasOwnProperty.call(origin, key)) {
				// 递归克隆每个属性值
				result[key] = this.deepClone(origin[key]);
			}
		}

		return result;
	};
	/**
	 * 通过创建临时a标签实现文件下载
	 * 支持跨域文件下载，会先获取文件内容再触发下载
	 *
	 * @param {string} fileUrl - 文件的完整URL地址
	 * @param {string} [fileName] - 可选的文件名，如果不提供则从URL中提取
	 *
	 * @example
	 * // 下载文件并指定文件名
	 * Utils.downFile('https://example.com/file.pdf', 'my-document.pdf');
	 *
	 * // 下载文件使用原始文件名
	 * Utils.downFile('https://example.com/path/to/file.pdf');
	 */
	static downFile(fileUrl, fileName) {
		// 使用fetch API获取文件内容
		fetch(fileUrl)
			.then((res) => {
				// 将响应转换为Blob对象
				return res.blob();
			})
			.then((blob) => {
				// 创建临时的a标签元素用于触发下载
				const link = document.createElement('a');

				// 为Blob创建临时URL
				link.href = URL.createObjectURL(blob);

				// 设置下载文件名
				if (fileName) {
					// 使用提供的文件名
					link.download = fileName;
				} else {
					// 从URL中提取文件名（取最后一个斜杠后的部分）
					const tempArr = fileUrl.split('/');
					link.download = tempArr[tempArr.length - 1];
				}

				// 将a标签添加到DOM中
				document.body.appendChild(link);

				// 程序化点击a标签触发下载
				link.click();

				// 下载完成后移除临时a标签
				document.body.removeChild(link);
			});
	}
	/**
	 * 检测对象是否为空对象
	 * 通过JSON序列化判断对象是否只包含空的花括号
	 *
	 * @param {object} object - 要检测的对象
	 * @returns {boolean} 如果是空对象返回true，否则返回false
	 *
	 * @example
	 * Utils.isEmpty({})           // 返回 true
	 * Utils.isEmpty({a: 1})       // 返回 false
	 * Utils.isEmpty(null)         // 返回 false (会抛出异常)
	 *
	 * @note 此方法只适用于普通对象，对于null、undefined等会抛出异常
	 */
	static isEmpty(object) {
		// 将对象序列化为JSON字符串，如果是空对象则为'{}'
		return JSON.stringify(object) === '{}';
	}
	/**
	 * 格式化时间为指定格式的字符串
	 * 支持多种输入格式：Date对象、时间戳（秒/毫秒）、日期字符串
	 *
	 * @param {Date|string|number} time - 要格式化的时间值
	 * @param {string} [pattern='yyyy-MM-dd HH:mm:ss'] - 格式化模式字符串
	 * @returns {string|null} 格式化后的日期字符串，输入无效时返回null
	 *
	 * @example
	 * // 格式化Date对象
	 * Utils.parseTime(new Date(), 'yyyy-MM-dd')  // '2024-01-15'
	 *
	 * // 格式化时间戳（秒）
	 * Utils.parseTime(1705123200, 'yyyy年MM月dd日')  // '2024年01月13日'
	 *
	 * // 格式化时间戳（毫秒）
	 * Utils.parseTime(1705123200000, 'HH:mm:ss')  // '08:00:00'
	 *
	 * // 格式化日期字符串
	 * Utils.parseTime('2024-01-15', 'MM/dd/yyyy')  // '01/15/2024'
	 *
	 * // 显示星期
	 * Utils.parseTime(new Date(), 'yyyy-MM-dd 星期a')  // '2024-01-15 星期一'
	 */
	static parseTime(time, pattern) {
		// 参数验证：如果没有参数或时间值为空，返回null
		if (arguments.length === 0 || !time) {
			return null;
		}

		// 设置默认格式模式
		const format = pattern || 'yyyy-MM-dd HH:mm:ss';
		let date;

		// 根据输入类型进行不同的处理
		if (typeof time === 'object') {
			// 如果输入是对象（通常是Date对象），直接使用
			date = time;
		} else {
			// 处理字符串和数字类型的输入
			if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
				// 如果是纯数字字符串，转换为数字
				time = parseInt(time);
			} else if (typeof time === 'string') {
				// 如果是日期字符串，将横线替换为斜杠（兼容性更好）
				time = time.replace(new RegExp(/-/gm), '/');
			}

			// 如果是10位数字（秒级时间戳），转换为毫秒级
			if (typeof time === 'number' && time.toString().length === 10) {
				time = time * 1000;
			}

			// 创建Date对象
			date = new Date(time);
		}

		// 构建格式化对象，包含各种时间单位的值
		const formatObj = {
			yyyy: date.getFullYear(),        // 四位年份
			MM: date.getMonth() + 1,         // 月份（注意：getMonth()返回0-11）
			dd: date.getDate(),              // 日期
			HH: date.getHours(),             // 小时（24小时制）
			mm: date.getMinutes(),           // 分钟
			ss: date.getSeconds(),           // 秒
			a: date.getDay(),                // 星期（0=周日，1=周一...）
		};

		// 使用正则表达式替换格式字符串中的占位符
		const time_str = format.replace(/(yyyy|MM|dd|HH|dd|mm|ss|ss|a)+/g, (result, key) => {
			let value = formatObj[key];

			// 特殊处理星期显示（getDay()返回0表示周日）
			if (key === 'a') {
				return ['日', '一', '二', '三', '四', '五', '六'][value];
			}

			// 对于小于10的数值，前面补0（如：01、02、09）
			if (result.length > 0 && value < 10) {
				value = '0' + value;
			}

			// 返回格式化后的值，如果值不存在则返回0
			return value || 0;
		});

		return time_str;
	}
}
