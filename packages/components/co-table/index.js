/**
 * Co-Table 组件库入口文件
 *
 * 这是一个功能完整的Vue表格组件库，提供以下核心功能：
 * - 数据表格展示与操作
 * - 内置搜索功能
 * - 分页支持
 * - 表单编辑
 * - 权限控制
 * - 字典数据支持
 * - 文件上传下载
 *
 * 使用方式：
 * 1. 作为Vue插件安装：Vue.use(CoTable, options)
 * 2. 直接导入组件：import CoTable from 'co-table'
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入默认配置对象
import defaultConfig from "./config.js";
// 导入工具类
import Utils from "./utils";
// 导入主表格组件
import coTable from "./table.vue";
// 导入搜索组件
import coSearch from "./search.vue";
// 导入公共样式文件
import "./styles/common.scss";

// 在全局window对象上创建storage属性，用于存储组件间共享的数据
window.storage = {};

/**
 * Vue插件安装函数
 * 负责注册组件到Vue实例，并合并用户配置
 *
 * @param {Object} Vue - Vue构造函数或Vue应用实例
 * @param {Object} [opts={}] - 用户自定义配置选项
 *
 * @example
 * // 基本安装
 * Vue.use(CoTable);
 *
 * // 带配置安装
 * Vue.use(CoTable, {
 *    getDic: (params: any) => {
 *        // @ts-ignore
 *        return getDictionary(params).then(({ dicList }) => {
 *          return formatDic(dicList);
 *        });
 *      },
 *      attrs: {
 *        'header-cell-style': { backgroundColor: 'var(--el-table-row-hover-bg-color)', color: 'var(--el-text-color-primary)', align: 'left' },
 *        border: true,
 *      },
 * });
 */
const install = (Vue, opts = {}) => {
  // 如果用户提供了配置选项，则与默认配置进行深度合并
  if (Object.keys(opts).length) {
    Utils.deepMerge(defaultConfig, opts);
  }

  // 全局注册表格组件
  Vue.component(coTable.name, coTable);
  // 全局注册搜索组件
  Vue.component(coSearch.name, coSearch);
};

/**
 * 为主表格组件添加install方法，使其可以作为Vue插件使用
 * 这是Vue插件的标准做法
 */
coTable.install = (Vue, opts = {}) => {
  install(Vue, opts);
};

/**
 * 自动安装插件
 * 如果在浏览器环境中检测到全局Vue对象，则自动安装插件
 * 这样可以通过script标签直接引入使用，无需手动调用Vue.use()
 */
if (typeof window !== "undefined" && window.Vue) {
  // 注意：这里的opts赋值语法有问题，应该直接传入{}
  install(window.Vue, (opts = {}));
}

// 导出主表格组件作为默认导出
// 用户可以通过 import CoTable from 'co-table' 的方式导入
export default coTable;
