<!--
  Co-Button 按钮组件

  功能特性：
  - 基于 Element Plus Button 组件封装
  - 支持自定义样式类名
  - 支持禁用点击功能
  - 自动阻止事件冒泡
  - 支持插槽内容自定义

  使用场景：
  - 表格操作按钮
  - 表格顶部操作按钮
  - 搜索表单按钮
  - 下拉菜单按钮项
-->
<template>
	<!--
		Element Plus 按钮组件
		- :class="item.classNew": 绑定自定义样式类名
		- v-bind="item": 绑定所有按钮配置属性
		- @click="onClick": 处理点击事件
	-->
	<el-button :class="item.classNew" v-bind="item" @click="onClick">
		<!--
			按钮内容插槽
			- 默认显示 item.name 文本
			- 支持通过插槽自定义内容
		-->
		<slot>{{ item.name }}</slot>
	</el-button>
</template>

<script>
/**
 * Co-Button 按钮组件
 *
 * 基于 Element Plus Button 组件封装的按钮组件
 * 主要用于表格操作按钮和表单按钮
 *
 * 主要功能：
 * - 按钮配置属性处理
 * - 样式类名转换
 * - 点击事件处理
 * - 事件冒泡阻止
 * - 禁用点击支持
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */
export default {
	name: 'CoButton',

	// 不继承父组件的属性，避免属性传递冲突
	inheritAttrs: false,

	/**
	 * 组件创建时的初始化逻辑
	 * 负责处理按钮配置属性，清理不需要的属性
	 */
	created() {
		// 从父组件属性中获取按钮配置对象
		const { item } = this.$attrs;

		// 处理样式类名：将 className 转换为 classNew
		// 这样做是为了避免与 Element Plus 的内部属性冲突
		item['classNew'] = item['className'];
		delete item['className'];

		// 保存处理后的按钮配置
		this.item = item;

		// 清理表格相关的配置属性，这些属性不需要传递给 el-button

		// 删除表格键名（用于按钮权限控制，不需要传递给按钮组件）
		item.tableKey && delete item.tableKey;

		// 删除权限规则（用于按钮显示控制，不需要传递给按钮组件）
		delete item.rule;

		// 删除自定义属性配置（已经在其他地方处理过，不需要传递给按钮组件）
		delete item.attributes;
	},

	methods: {
		/**
		 * 处理按钮点击事件
		 * 支持禁用点击和事件冒泡阻止
		 *
		 * @param {Event} e - 原始点击事件对象
		 */
		onClick(e) {
			// 如果设置了禁用点击属性，则不处理点击事件
			if (this.$attrs['dis-click']) return;

			// 阻止事件冒泡，避免在表格中触发行选择等事件
			e.stopPropagation();

			// 向父组件发送点击事件
			this.$emit('click');
		},
	},
};
</script>
