<!--
  Co-Select 下拉选择器组件

  功能特性：
  - 支持静态和动态选项数据
  - 支持单选和多选模式
  - 支持远程数据加载
  - 支持字典数据绑定
  - 支持选项过滤功能
  - 自动生成占位符文本

  使用场景：
  - 表格内可编辑下拉选择
  - 搜索表单选择项
  - 输入框前置/后置选择器
-->
<template>
	<!--
		Element Plus 下拉选择器组件
		- v-model: 双向绑定表单数据
		- clearable: 启用清除功能
		- placeholder: 动态生成占位符文本
		- v-bind="itemAttrs": 绑定所有配置属性
		- v-on="listeners": 绑定所有事件监听器
	-->
	<el-select
		v-model="formModel[item.prop]"
		clearable
		:placeholder="itemAttrs.placeholder || '请选择' + (itemAttrs.label || '')"
		v-bind="itemAttrs"
		v-on="listeners"
	>
		<!--
			动态渲染选项列表
			- v-for: 遍历选项数据
			- key: 使用值字段作为唯一标识
			- label: 显示文本
			- value: 选项值
			- disabled: 禁用状态
		-->
		<el-option
			v-for="opt in optionList"
			:key="opt[valueKey]"
			:label="opt[labelKey]"
			:value="opt[valueKey]"
			:disabled="itemAttrs.disabled"
		/>
	</el-select>
</template>
<script>
/**
 * Co-Select 下拉选择器组件
 *
 * 基于 Element Plus Select 组件封装的下拉选择器
 * 支持多种数据源和配置方式，适用于表格内编辑和搜索表单
 *
 * 主要功能：
 * - 静态选项数据支持
 * - 动态数据加载（函数、Promise、异步函数）
 * - 字典数据绑定
 * - 多选模式支持
 * - 选项过滤功能
 * - 远程数据加载
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入工具类
import Utils from '../../utils';
// 导入默认配置
import defaultConfig from '../../config';
// 导入公共事件处理函数
import { handleFn } from './common.js';
// 导入 Vue 3 响应式 API
import { reactive } from 'vue';

export default {
	name: 'CoSelect',

	// 不继承父组件的属性，避免属性传递冲突
	inheritAttrs: false,

	/**
	 * 组件属性定义
	 */
	props: {
		// 插槽名称（暂未使用）
		slotName: {
			type: String,
			default: '',
		},
		// 字典数据对象，用于动态加载选项
		dic: {
			type: Object,
			default: () => null,
		},
	},

	/**
	 * 组件数据
	 * @returns {Object} 组件的响应式数据
	 */
	data() {
		return {
			// 事件监听器对象
			listeners: {},
			// 选项显示文本的字段名（从默认配置获取）
			labelKey: defaultConfig.dicKeys[0],
			// 选项值的字段名（从默认配置获取）
			valueKey: defaultConfig.dicKeys[1],
			// 选项列表数据
			optionList: [],
			// 表单数据模型
			formModel: {},
		};
	},

	// 注入父级提供的 widgetItem 对象
	inject: ['widgetItem'],
	/**
	 * 组件创建时的初始化逻辑
	 * 负责设置组件属性、数据模型、选项数据和事件监听器
	 */
	created() {
		// 解构获取需要的属性和数据
		// mainProp: 主要属性名，用于前置组件时生成新的属性名
		const {
			$attrs: { data = null, item, row = data, scene, mainProp = '' },
			listeners,
		} = this;

		// 保存表单项配置对象
		this.item = item;

		// 合并表单项属性配置
		const itemAttrs = (this.itemAttrs = Object.assign(item.attrs || {}));

		// 判断是否在表格内使用
		this._inTable = scene === 'inTable';

		// 创建响应式的表单数据模型
		this.formModel = reactive(row);

		// 如果有主属性名，说明这是一个前置组件，需要生成新的属性名
		mainProp && (item.prop = `${mainProp}_prepend`);

		// 初始化字段值
		if (!this.formModel[item.prop]) {
			// 多选模式初始化为数组，单选模式初始化为空字符串
			this.formModel[item.prop] = itemAttrs.multiple ? [] : '';
		}

		// 处理多选模式的数据转换
		if (itemAttrs.multiple) {
			const itemProp = row[item.prop];
			// 如果原始数据是字符串或数字，需要转换为数组
			if (['String', 'Number'].includes(Utils.getType(itemProp))) {
				// 将逗号分隔的字符串转换为数字数组
				this.formModel[item.prop] = itemProp.toString().split(',').map(Number);
			}
		}

		// 将当前组件实例注册到 widgetItem 中
		this.widgetItem[item.prop] = this;

		// 解构获取选项相关配置
		const { optionKey, option, attrs, filter } = item;
		// 获取选项数据值（优先使用 option，其次使用 attrs.option）
		const optionVal = option || attrs.option;

		// 设置选项过滤函数
		this.filterOption = (options) => (typeof filter === 'function' ? filter(options) : options);

		// 如果指定了自定义的键名映射，更新 labelKey 和 valueKey
		if (optionKey) [this.labelKey, this.valueKey] = optionKey;

		// 监听字典数据的变化
		const watcher = this.$watch(
			() => this.dic,
			(val) => {
				// 当字典数据更新且包含所需选项时，初始化选项数据
				if (val && val[optionVal]) {
					this.initOption(item, optionKey, val[optionVal]);
					// 取消监听器，避免重复执行
					watcher();
				}
			},
			{ deep: true }  // 深度监听对象变化
		);

		// 初始化选项数据
		this.initOption(item, optionKey, optionVal);

		// 初始化事件监听器
		this.initEvent({ events: item.events, itemAttrs, $attrs: this.$attrs, listeners });
	},
	methods: {
		/**
		 * 初始化事件监听器
		 * 为组件绑定 change 事件和自定义事件处理器
		 *
		 * @param {Object} params - 参数对象
		 * @param {Object} params.events - 自定义事件配置
		 * @param {Object} params.itemAttrs - 表单项属性
		 * @param {Object} params.$attrs - 组件属性
		 * @param {Object} params.listeners - 事件监听器对象
		 */
		initEvent({ events, itemAttrs, $attrs, listeners }) {
			// 绑定默认的 change 事件处理器
			listeners['change'] = (value) => handleFn.call(this, 'change', value, this._inTable, $attrs, itemAttrs);

			// 如果配置了自定义事件，为每个事件绑定处理器
			if (events) {
				for (const evName of Object.keys(events)) {
					// 根据使用场景（表格内/搜索表单）绑定不同的事件处理器
					this._inTable
						? (listeners[evName] = () => handleFn.call(this, evName, $attrs.row[this.item.prop], this._inTable, $attrs, itemAttrs))
						: (listeners[evName] = () => handleFn.call(this, evName, $attrs.data[$attrs.item.prop], false, $attrs, itemAttrs));
				}
			}
		},

		/**
		 * 初始化选项数据
		 * 支持多种数据源类型：数组、函数、Promise、异步函数、字典文件
		 *
		 * @param {Object} item - 表单项配置对象
		 * @param {Array} optionKey - 自定义的键名映射 [labelKey, valueKey]
		 * @param {*} optionVal - 选项数据值，可以是多种类型
		 */
		initOption(item, optionKey, optionVal) {
			// 获取选项数据的类型
			const optionValueType = Utils.getType(optionVal);

			// 处理数组类型的静态选项数据
			if (optionValueType === 'Array') {
				// 如果指定了自定义键名映射，更新 labelKey 和 valueKey
				if (optionKey) [this.labelKey, this.valueKey] = optionKey;
				// 应用过滤器并设置选项列表
				this.optionList = this.filterOption(optionVal);
				return;
			}

			// 处理函数类型的动态选项数据
			if (optionValueType === 'Function') {
				// 执行函数获取结果
				const result = optionVal();
				// 判断函数返回值是否为 Promise
				if (Utils.getType(result) === 'Promise') {
					// 异步处理 Promise 结果
					result.then((data) => {
						this.optionList = this.filterOption(data);
					});
				} else {
					// 同步处理函数结果
					this.optionList = result;
				}
				return;
			}

			// 处理异步函数类型
			if (optionValueType === 'AsyncFunction') {
				// 执行异步函数并处理结果
				optionVal().then((data) => (this.optionList = this.filterOption(data)));
				return;
			}

			// 处理 Promise 类型
			if (optionValueType === 'Promise') {
				// 直接处理 Promise 结果
				optionVal.then((data) => (this.optionList = data));
				return;
			}

			// 处理字典文件类型（以 .json 结尾的字符串）
			if (optionValueType === 'String' && optionVal.substr(optionVal.length - 5, 5) === '.json') {
				// 移除 .json 后缀，获取字典键名
				const pval = optionVal.replace(/\.json$/, '');
				// 通过配置的字典获取方法加载数据
				defaultConfig.getDic(pval).then((resDic) => {
					this.optionList = this.filterOption(resDic);
				});
			}
		},

		/**
		 * 重置字段值
		 * 用于搜索表单的重置功能
		 *
		 * @param {*} [value=undefined] - 要设置的新值，默认为 undefined
		 */
		resetField(value = undefined) {
			this.formModel[this.item.prop] = value;
		},
	},
};
</script>

<!--
  组件样式定义
  主要用于调整输入框组合的边框圆角样式
-->
<style>
/* 设置全局 CSS 变量：输入框边框圆角大小 */
:root {
	--el-input-border-radius: 4px;
}

/*
  调整输入框组合中前置组件的样式
  当输入框有前置内容时，移除输入框左侧的圆角
  使前置内容和输入框看起来是一个整体
*/
.el-input-group--prepend .el-input__wrapper {
	border-top-left-radius: 0;     /* 移除左上角圆角 */
	border-bottom-left-radius: 0;  /* 移除左下角圆角 */
}
</style>
