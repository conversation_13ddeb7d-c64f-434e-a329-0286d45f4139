/**
 * Co-Table 表单组件公共逻辑处理模块
 *
 * 提供表单组件的通用事件处理逻辑，包括：
 * - 表格内表单事件处理
 * - 搜索表单事件处理
 * - 多选下拉框特殊处理
 * - 自定义事件回调处理
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入工具类
import Utils from '../../utils';

/**
 * 表单事件统一处理函数
 * 作为所有表单组件事件的总出口，统一处理 input、blur、change 等事件
 *
 * @param {string} handleName - 事件名称（如：'change'、'blur'、'input'等）
 * @param {*} value - 事件触发时的值
 * @param {boolean} [inTable=true] - 是否在表格内使用
 * @param {Object} $attrs - 组件的 $attrs 对象，包含传递的属性
 * @param {Object} itemAttrs - 表单项的属性配置对象
 *
 * @description
 * 该函数会根据不同的使用场景（表格内/搜索表单）执行不同的处理逻辑：
 * - 表格内：调用统一的 handle 函数，支持自定义事件回调
 * - 搜索表单：通过 $emit 发送 change 事件，支持对象值的展开处理
 *
 * @example
 * // 在表单组件中调用
 * handleFn.call(this, 'change', newValue, true, this.$attrs, this.itemAttrs);
 */
export const handleFn = function (handleName, value, inTable = true, $attrs, itemAttrs) {
	// 从 $attrs 中解构出需要的属性
	const { item, data = null, row = data, index, handle, formRef, type } = $attrs;

	// 获取多选配置（用于下拉框多选时的分隔符）
	const multiple = itemAttrs?.multiple;

	// 判断是否为字符串分隔的多选下拉框
	const isSelectMultiple = multiple && item.type === 'select' && Utils.getType(multiple) === 'String';

	// 表格内表单的处理逻辑
	if (inTable) {
		// 如果是多选下拉框，将数组值用分隔符连接成字符串
		if (isSelectMultiple) {
			value = value.join(multiple);
		}

		// 处理自定义事件回调
		if (item.events) {
			// 获取当前事件的处理器类型
			const handleType = Utils.getType(item.events[handleName]);

			// 如果事件处理器为 false，则阻止后续处理
			if (handleType === 'Boolean' && item.events[handleName] === false) return;

			// 如果事件处理器是函数，则执行自定义处理逻辑
			if (handleType === 'Function') {
				return item.events[handleName]({
					handle: handleName,
					type,
					prop: item.prop,
					row,
					index,
					value
				});
			}
		}

		// 调用统一的事件处理函数（注释掉的是旧的 emit 方式）
		// this.$emit('change', { prop: item.prop, value: value });
		handle({
			handle: handleName,
			type,
			prop: item.prop,
			row,
			index,
			value,
			formRef
		});
		return;
	}

	// 搜索表单的处理逻辑

	// 如果值是对象类型，需要展开处理每个属性
	if (Utils.getType(value) === 'Object') {
		for (const key in value) {
			// 为对象的每个属性发送单独的 change 事件
			this.$emit('change', { prop: key, value: value[key] });
		}
		return;
	}

	// 处理普通表单项
	if (item.prop) {
		// 特殊处理：如果是前置组件且有值，缓存该值
		if (item.prop.includes('_prepend') && value) {
			this.cacheKey = value;
		}

		// 发送 change 事件
		this.$emit('change', { prop: item.prop, value });

		// 处理自定义事件回调（搜索表单场景）
		if (item.events) {
			const handleType = Utils.getType(item.events[handleName]);

			// 如果事件处理器为 false，则阻止后续处理
			if (handleType === 'Boolean' && item.events[handleName] === false) return;

			// 如果事件处理器是函数，则执行自定义处理逻辑
			if (handleType === 'Function') {
				return item.events[handleName]({
					handle: handleName,
					type: item.type,
					prop: item.prop,
					value
				});
			}
		}
	}

	// 多选下拉框的特殊处理（搜索表单场景）
	if (isSelectMultiple) {
		// 重置字段值
		this.resetField(value);
		// 将数组值用分隔符连接后赋值给行数据
		row[item.prop] = value.join(multiple);
	}
};
