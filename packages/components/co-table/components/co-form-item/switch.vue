<!--
  Co-Switch 开关组件

  功能特性：
  - 支持异步状态切换
  - 支持切换前的确认处理
  - 支持加载状态显示
  - 阻止事件冒泡
  - 统一的事件处理机制

  使用场景：
  - 表格内状态切换
  - 表单开关选项
  - 需要确认的状态变更
-->
<template>
	<!--
		外层容器，阻止点击事件冒泡
		防止在表格中点击开关时触发行选择等事件
	-->
	<div @click.stop>
		<!--
			Element Plus 开关组件
			- v-model: 双向绑定表单数据
			- v-bind="item.attrs": 绑定所有配置属性
			- :loading: 显示加载状态
			- :before-change: 切换前的处理函数
			- @change: 切换完成后的处理函数
		-->
		<el-switch
			v-model="formModel[item.prop]"
			v-bind="item.attrs"
			:loading="loading"
			:before-change="onBeforeChange"
			@change="onChange"
		/>
	</div>
</template>

<script>
/**
 * Co-Switch 开关组件
 *
 * 基于 Element Plus Switch 组件封装的开关组件
 * 支持异步状态切换和切换前的确认处理
 *
 * 主要功能：
 * - 异步状态切换支持
 * - 切换前确认处理
 * - 加载状态管理
 * - 事件冒泡阻止
 * - 统一事件处理
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入公共事件处理函数
import { handleFn } from './common.js';

export default {
	name: 'CoSwitch',

	// 不继承父组件的属性
	inheritAttrs: false,

	// 注入父级提供的 widgetItem 对象
	inject: ['widgetItem'],

	/**
	 * 组件数据
	 * @returns {Object} 组件的响应式数据
	 */
	data() {
		return {
			// 加载状态，用于显示异步操作的加载动画
			loading: false,
		};
	},

	/**
	 * 组件创建时的初始化逻辑
	 * 负责设置组件属性和注册组件实例
	 */
	created() {
		// 从父组件属性中解构获取配置信息
		const { item, data = null, row, scene = '' } = this.$attrs;

		// 保存表单项配置
		this.item = item;

		// 保存行数据（优先使用 data，其次使用 row）
		this.row = data || row;

		// 将当前组件实例注册到 widgetItem 中
		this.widgetItem[item.prop] = this;

		// 判断是否在表格内使用
		this._inTable = scene === 'inTable';

		// 设置表单数据模型
		this.formModel = row;
	},
	methods: {
		/**
		 * 重置字段值
		 * 用于搜索表单的重置功能
		 *
		 * @param {boolean|undefined} [value=undefined] - 要设置的新值
		 */
		resetField(value = undefined) {
			this.formModel[this.item.prop] = value;
		},

		/**
		 * 开关切换前的处理函数
		 * 支持异步确认操作，如服务器验证、用户确认等
		 *
		 * @returns {boolean|Promise<boolean>} 返回 true 允许切换，false 阻止切换
		 *
		 * @description
		 * 该方法会在开关状态改变前被调用，可以用于：
		 * - 向服务器发送请求确认操作
		 * - 显示确认对话框
		 * - 执行其他异步验证逻辑
		 */
		onBeforeChange() {
			// 解构获取需要的属性和数据
			const { $attrs, item, row, _inTable } = this;

			// 检查是否配置了切换前处理函数
			const hasBeforeChange = item.attrs && item.attrs['before-change'];

			// 如果没有配置切换前处理，直接允许切换
			if (!hasBeforeChange) return true;

			// 显示加载状态
			this.loading = true;

			// 执行切换前处理函数（通常返回 Promise）
			const result = item.attrs['before-change'](row).then((res) => {
				// 异步操作完成后，隐藏加载状态
				this.loading = false;
				return res;
			});

			// 触发统一的事件处理（使用当前值，因为还未切换）
			handleFn.call(this, 'switch', row[item.prop], _inTable, $attrs, item.attrs);

			// 返回 Promise 结果，决定是否允许切换
			return result;

			// 注释掉的代码：旧的处理逻辑
			//  else {
			// 	const rowProp = !row[item.prop];
			// 	handleFn.call(this, 'switch', rowProp, _inTable, this.$attrs, item.attrs);
			// 	this.loading = false;
			// 	return true;
			// }
		},

		/**
		 * 开关切换完成后的处理函数
		 * 在开关状态成功改变后被调用
		 *
		 * @param {boolean} value - 切换后的新值
		 *
		 * @description
		 * 该方法只在没有配置 before-change 处理函数时才会触发事件处理
		 * 如果配置了 before-change，事件处理会在 onBeforeChange 中执行
		 */
		onChange(value) {
			// 解构获取需要的属性
			const { $attrs, item, _inTable } = this;

			// 只有在没有配置切换前处理函数时，才在这里处理事件
			// 这样避免了事件的重复触发
			if (!item.attrs || !item.attrs['before-change']) {
				handleFn.call(this, 'switch', value, _inTable, $attrs, item.attrs);
			}
		},
	},
};
</script>
