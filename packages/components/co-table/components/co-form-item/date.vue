<!--
  Co-Date 日期时间组件

  功能特性：
  - 支持多种日期时间选择器类型
  - 支持日期范围分割为独立字段
  - 支持前置下拉选择器
  - 自动根据类型选择合适的组件
  - 支持时间选择器和时间范围选择器

  使用场景：
  - 表格内日期时间编辑
  - 搜索表单日期筛选
  - 带前缀的日期时间选择
-->
<template>
	<!--
		外层容器，根据是否有前置组件动态添加样式类
		当有前置组件时，应用输入框组合的样式
	-->
	<div :class="item.prepend && 'el-input-group el-input--suffix el-input-group--prepend'">
		<!-- 前置组件区域：通常是下拉选择器 -->
		<div class="el-input-group__prepend" v-if="item.prepend">
			<co-select
				v-bind="{
					item: item.prepend,
					dic: $attrs.dic,
					mainProp: item.prepend.prop ? '' : item.prop
				}"
				:row="formModel"
				@change="(data) => $emit('change', { prop: data.prop, value: data.value })"
			/>
		</div>

		<!--
			动态日期时间组件
			- :is="comName": 根据配置动态选择组件类型
			- v-model: 双向绑定表单数据
			- v-bind="itemAttrs": 绑定所有配置属性
			- placeholder: 动态生成占位符文本
			- v-on="listeners": 绑定事件监听器
			- @change: 处理日期变化事件
		-->
		<component
			:is="comName"
			v-model="formModel[item.prop]"
			v-bind="itemAttrs"
			:placeholder="itemAttrs.placeholder || '请选择' + (itemAttrs.label || '')"
			v-on="listeners"
			@change="onChangeDate($event, item, splitPropData, $attrs, _inTable)"
		/>
	</div>
</template>
<script>
/**
 * Co-Date 日期时间组件
 *
 * 基于 Element Plus 日期时间选择器封装的组件
 * 支持多种日期时间类型和复杂的数据处理逻辑
 *
 * 主要功能：
 * - 自动选择合适的日期时间组件
 * - 支持日期范围分割为独立字段
 * - 支持前置下拉选择器
 * - 复杂的数据格式转换
 * - 字段重置和回显功能
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入工具类
import Utils from '../../utils';
// 导入公共事件处理函数
import { handleFn } from './common.js';
// 导入下拉选择器组件
import coSelect from './select.vue';
// 导入日期类型枚举
import { dateType } from '../../config';

export default {
	name: 'CoDate',

	// 不继承父组件的属性
	inheritAttrs: false,

	// 注册子组件
	components: {
		coSelect,
	},

	/**
	 * 组件数据初始化
	 * 根据配置动态确定使用的组件类型和属性
	 * @returns {Object} 组件的响应式数据
	 */
	data() {
		// 从父组件属性中获取配置信息
		const { item, scene } = this.$attrs;

		// 判断是否在表格内使用
		this._inTable = scene === 'inTable';

		// 合并表单项属性，默认启用清除功能
		this.itemAttrs = Object.assign(item.attrs || {}, { clearable: true });

		// 判断是否为时间选择器类型
		const isTimeType = (this.isTimeType = item.type === 'time');

		// 判断是否配置了时间选择器选项（用于区分时间选择器和时间范围选择器）
		const hasPickerOpts = isTimeType && this.itemAttrs['pickerOptions'];

		// 如果不是时间类型，将类型设置到属性中
		!isTimeType && (this.itemAttrs.type = item.type);

		return {
			// 分割字段数据存储对象
			splitPropData: {},
			// 重置计数器（用于处理分割字段的重置逻辑）
			resetNum: 0,
			// 表单项配置
			item,
			// 事件监听器对象
			listeners: {},
			// 动态组件名称：根据类型和配置选择合适的组件
			comName: isTimeType && !hasPickerOpts
				? 'el-time-picker'    // 时间选择器
				: hasPickerOpts
					? 'el-time-select'  // 时间范围选择器
					: 'el-date-picker', // 日期选择器
		};
	},

	// 注入父级提供的 widgetItem 对象
	inject: ['widgetItem'],
	/**
	 * 组件创建时的初始化逻辑
	 * 负责设置表单数据模型、事件监听器和字段注册
	 */
	created() {
		// 解构获取需要的属性和数据
		const {
			_inTable,
			listeners,
			item,
			$attrs: { data = null, row = data },
		} = this;

		// 创建响应式的表单数据模型
		this.formModel = reactive(row);

		// 判断当前组件是否为日期时间相关类型
		this.isDateType = dateType.includes(item.type);

		// 初始化事件监听器（仅在表格内使用时）
		if (_inTable && item.events) {
			// 如果配置了 blur 事件，绑定处理器
			item.events.blur && (listeners['blur'] = () => handleFn.call(this, 'blur', this.formModel[item.prop], _inTable, this.$attrs));
		}

		// 将组件实例注册到 widgetItem 中，供其他组件访问
		if (item.splitProp && !item.prepend) {
			// 如果配置了分割字段且不是前置组件，初始化分割字段
			this.formModel[item.splitProp[0]] = '';
			this.formModel[item.splitProp[1]] = '';
			// 将当前组件实例注册到两个分割字段
			this.widgetItem[item.splitProp[0]] = this;
			this.widgetItem[item.splitProp[1]] = this;
		} else {
			// 普通情况，初始化主字段
			this.formModel[item.prop] = '';
			// 将当前组件实例注册到主字段
			this.widgetItem[item.prop] = this;
		}
	},
	methods: {
		/**
		 * 重置字段值
		 * 支持复杂的数据格式处理，包括分割字段和逗号分隔的日期范围
		 *
		 * @param {string|Array|null} value - 要设置的新值
		 *
		 * @description
		 * 该方法处理多种数据格式：
		 * - 逗号分隔的日期范围字符串："2023-07-07,2023-08-01"
		 * - 单个日期字符串："2023-07-07"
		 * - 数组格式：["2023-07-07", "2023-08-01"]
		 * - 空值：null 或 undefined
		 */
		resetField(value) {
			// 获取分割字段配置和主字段名
			const splitProp = this.splitProp;
			const itemProp = this.item.prop;

			if (value) {
				// 处理字符串类型的值
				if (Utils.getType(value) === 'String') {
					// 检查是否包含逗号（日期范围分隔符）
					const hasComma = value.includes(',');
					let echoValue = value;

					if (hasComma) {
						// 处理逗号分隔的日期范围："2023-07-07,2023-08-01"
						echoValue = value.split(',');
						this.formModel[itemProp] = echoValue;

						// 如果配置了分割字段，分别设置开始和结束日期
						if (splitProp) {
							this.splitPropData[splitProp[0]] = echoValue[0];
							this.splitPropData[splitProp[1]] = echoValue[1];
						}
					} else {
						// 处理单个日期："2023-07-07"
						if (splitProp) {
							// 分割字段模式：需要处理多次调用的情况
							if (this.resetNum < 1) {
								// 第一次调用，初始化为数组
								this.formModel[itemProp] = [value];
							} else {
								// 第二次调用，添加到数组中
								this.formModel[itemProp] = [...this.formModel[itemProp], value];
							}
							// 设置分割字段数据
							this.splitPropData[splitProp[0]] = echoValue[0];
							this.splitPropData[splitProp[1]] = echoValue[1];
						} else {
							// 普通模式：直接设置值
							this.formModel[itemProp] = echoValue;
						}
					}
				}
			} else {
				// 处理空值情况
				this.formModel[itemProp] = value;

				// 如果有分割字段，也要清空分割字段的值
				if (splitProp) {
					this.splitPropData[splitProp[0]] = value;
					this.splitPropData[splitProp[1]] = value;
				}
			}

			// 处理分割字段的重置计数
			// 因为分割字段需要调用两次 resetField，所以需要记录执行次数
			if (splitProp) {
				value ? (this.resetNum += 1) : (this.resetNum = 0);
			}
		},
		/**
		 * 处理日期变化事件
		 * 根据配置处理日期数据的格式转换和分割字段的赋值
		 *
		 * @param {string|Array|null} value - 日期选择器返回的值
		 * @param {Object} item - 表单项配置对象
		 * @param {Object} data - 分割字段数据对象
		 * @param {Object} $attrs - 组件属性对象
		 * @param {boolean} inTable - 是否在表格内使用
		 *
		 * @description
		 * 该方法处理以下情况：
		 * - 日期范围选择器返回的数组值
		 * - 单个日期选择器返回的字符串值
		 * - 分割字段的数据分配
		 * - 前置组件的特殊处理
		 */
		onChangeDate(value, item, data, $attrs, inTable) {
			// 获取分割字段配置
			const hasSplit = item.splitProp;

			if (value) {
				// 判断返回值是否为数组格式
				const isArrVal = Array.isArray(value);

				// 处理分割字段且值为数组的情况（日期范围选择器）
				if (isArrVal && Utils.getType(hasSplit) === 'Array') {
					// 如果不是前置组件，将数组的两个值分别赋给分割字段
					if (!item.prepend) {
						data[hasSplit[0]] = value[0];  // 开始日期
						data[hasSplit[1]] = value[1];  // 结束日期
					}
					// 注释掉的代码：删除主字段（可能用于某些特殊场景）
					// delete data[item.prop];
				} else {
					// 处理普通情况或需要拼接的情况
					// 如果是数组且不是前置组件，使用分隔符拼接；否则直接使用原值
					data[item.prop] = isArrVal && !item.prepend ? value.join(hasSplit) : value;
				}
			} else {
				// 处理清空值的情况
				this.formModel[item.prop] = value;

				if (hasSplit) {
					// 如果有分割字段且不是前置组件，清空分割字段
					if (!item.prepend) {
						data[hasSplit[0]] = '';
						data[hasSplit[1]] = '';
					}
				} else {
					// 普通情况，清空主字段
					data[item.prop] = '';
				}
			}

			// 调用统一的事件处理函数
			// 如果有分割字段，传递整个数据对象；否则传递主字段的值
			handleFn.call(this, 'change', hasSplit ? data : data[item.prop], inTable, $attrs);
		},
	},
};
</script>

<!--
  组件样式定义
  设置日期组件的边框颜色变量
-->
<style>
/* 设置全局 CSS 变量：输入框边框颜色 */
:root {
	--el-input-border-color: var(--el-border-color);
}
</style>
