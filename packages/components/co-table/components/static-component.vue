<!--
  Static-Component 静态组件

  这是一个无模板的渲染函数组件，用于根据配置动态渲染不同类型的静态内容

  功能特性：
  - 基于渲染函数的动态组件
  - 支持多种内容类型渲染
  - 支持自定义样式和格式化
  - 支持交互事件处理
  - 策略模式的类型渲染

  支持的类型：
  - date: 日期时间显示
  - download: 下载链接
  - preview: 预览链接
  - img: 图片显示
  - enum: 枚举值显示
  - default: 默认文本显示

  使用场景：
  - 表格单元格内容渲染
  - 静态数据展示
  - 需要格式化的内容显示
-->

<script>
/**
 * Static-Component 静态组件
 *
 * 基于 Vue 3 渲染函数的动态内容渲染组件
 * 使用策略模式根据类型配置渲染不同的内容
 *
 * 主要功能：
 * - 动态类型渲染
 * - 样式和格式化支持
 * - 交互事件处理
 * - 数据格式化
 * - 空值处理
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入工具类
import Utils from "../utils";
// 导入 Vue 3 渲染函数
import { h } from "vue";

export default {
  name: "StaticComponent",

  // 不继承父组件的属性
  inheritAttrs: false,

  /**
   * 组件创建时的初始化逻辑
   * 根据传入的配置创建类型渲染模板
   */
  created() {
    // 从父组件属性中解构获取配置信息
    const { item, row, column, handle, index } = this.$attrs;

    /**
     * 根据样式配置渲染带样式的内容
     * @param {string|Object} styles - 样式配置（字符串为 class，对象为 style）
     * @param {*} data - 要渲染的数据
     * @returns {VNode} 渲染的虚拟节点
     */
    const renderByStyles = (styles, data) => {
      // 处理空值：如果数据为空且不是数字0，显示"-"
      const rdData = data !== 0 && !data ? "-" : data;

      // 根据样式类型选择渲染方式
      return !styles || typeof styles === "string"
        ? h("span", { class: styles }, rdData)    // 字符串样式使用 class
        : h("span", { style: styles }, rdData);   // 对象样式使用 style
    };

    /**
     * 获取格式化后的数据
     * 如果配置了 formatter 函数，使用 formatter 处理数据
     */
    const formatter =
      item.attrs && item.attrs.formatter
        ? item.attrs.formatter(row, column, row[item.prop], index)
        : null;

    /**
     * 事件处理函数
     * @param {string} type - 事件类型
     */
    const onHandle = (type) => handle({ type, field: item.prop, row, index });

    /**
     * 类型渲染模板对象
     * 使用策略模式，根据不同类型渲染不同的内容
     */
    this.typeTemplate = Object.freeze({
      /**
       * 日期类型渲染器
       * 将时间戳或日期字符串格式化为可读的日期时间格式
       */
      date: () =>
        h(
          "span",
          {},
          // 优先使用 formatter 结果，其次使用原始数据进行时间格式化
          formatter || row[item.prop]
            ? Utils.parseTime(row[item.prop], item.format || "yyyy-MM-dd HH:mm:ss")
            : "-"
        ),

      /**
       * 下载类型渲染器
       * 渲染可点击的下载链接
       */
      download: () =>
        h(
          "span",
          {
            style: "cursor:pointer",           // 设置鼠标指针样式
            onClick: () => onHandle("download"), // 绑定下载事件
          },
          // 渲染下载文本，优先级：formatter > item.text > 默认文本
          renderByStyles(item.styles, formatter || item.text || "点击下载")
        ),

      /**
       * 预览类型渲染器
       * 渲染可点击的预览链接
       */
      preview: () =>
        h(
          "span",
          {
            style: "cursor:pointer",          // 设置鼠标指针样式
            onClick: () => onHandle("preview"), // 绑定预览事件
          },
          // 渲染预览文本，优先级：formatter > item.text > row[item.prop] > 默认文本
          renderByStyles(item.styles, formatter || item.text || row[item.prop] || "点击查看")
        ),

      /**
       * 图片类型渲染器
       * 渲染可点击的图片，支持预览功能
       */
      img: () =>
        // 只有当图片地址存在时才渲染
        row[item.prop] &&
        h(
          "img",
          {
            src: row[item.prop],                    // 图片源地址
            style: { height: item.height },        // 设置图片高度
            onClick: () => onHandle("preview"),     // 点击图片触发预览
          },
          {
            // 图片加载时的占位符
            placeholder: () => h("div", { class: "zs-flex-center" }, "加载中"),
          }
        ),

      /**
       * 枚举类型渲染器
       * 渲染枚举值，支持颜色配置
       */
      enum: () => {
        // 获取行数据中的枚举值
        const rowProp = row[item.prop];

        // 如果枚举值不存在，显示"-"
        if (!rowProp) return h("span", null, "-");

        // 获取枚举值对应的颜色配置
        const itemColor = item.colors && item.colors[rowProp[item["valueKey"] || "value"]];

        // 根据颜色类型设置样式（颜色值或CSS类名）
        const styles =
          itemColor && itemColor.indexOf("#") > -1
            ? { style: { color: itemColor } }  // 颜色值使用 style
            : { class: itemColor };            // CSS类名使用 class

        // 渲染枚举显示文本
        return h("span", { ...styles }, rowProp[item["labelKey"] || "text"]);
      },

      /**
       * 默认类型渲染器
       * 渲染普通文本内容，支持样式配置
       */
      default: () => renderByStyles(item.styles || "", formatter || row[item.prop]),
    });
  },

  /**
   * 组件渲染函数
   * 根据配置的类型选择对应的渲染模板
   *
   * @returns {VNode} 渲染的虚拟节点
   *
   * @description
   * 该方法是 Vue 3 的渲染函数，负责：
   * 1. 根据 item.type 选择对应的渲染模板
   * 2. 如果类型不存在或未配置，使用默认渲染模板
   * 3. 返回渲染后的虚拟节点
   */
  render() {
    // 解构获取需要的配置和模板
    const {
      $attrs: { item },    // 表单项配置
      typeTemplate,        // 类型渲染模板对象
    } = this;

    // 根据类型选择渲染模板
    // 如果配置了类型且存在对应的模板，使用对应模板；否则使用默认模板
    return item.type && typeTemplate[item.type]
      ? typeTemplate[item.type]()      // 使用指定类型的渲染模板
      : typeTemplate["default"]();     // 使用默认渲染模板
  },
};
</script>
