/**
 * Co-Table 组件默认配置文件
 *
 * 此文件定义了表格组件的全局默认配置，包括：
 * - 权限配置
 * - 搜索组件配置
 * - 表格样式配置
 * - 分页配置
 * - 字典配置
 * - 文件处理配置
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 版本号配置（暂时注释）
// export const VERSION = '1.0.0';

/**
 * 表格组件默认配置对象
 * 包含所有可配置的默认值和选项
 */
const defaultConfig = {
	// 路由元信息中权限字段的键名，用于获取按钮权限列表
	metaPermisKey: 'perms',

	// 搜索组件相关配置
	search: {
		// 搜索表单的自定义样式，null表示使用默认样式
		style: null,
		// 搜索按钮的配置：类型为主要按钮，图标为搜索图标，显示文本为"搜索"
		search: { type: 'primary', icon: 'Search', name: '搜索' },
		// 重置按钮的配置：类型为默认按钮，图标为刷新图标，显示文本为"重置"
		reset: { type: 'default', icon: 'Refresh', name: '重置' },
	},

	// 是否启用全局loading加载状态，true表示默认显示加载动画
	loading: true,

	// Element Plus 表格组件的默认属性配置
	attrs: {
		// 表头单元格的默认样式：浅灰色背景，深灰色文字
		'header-cell-style': { backgroundColor: '#f5f7fa', color: '#303133' },
	},

	// 获取字典数据的方法，需要在使用时传入具体的获取函数
	getDic: null,

	// 字典数据的键名映射：[显示文本的键名, 值的键名]
	dicKeys: ['label', 'value'],

	// 分页相关配置
	page: {
		// 请求参数的字段名映射
		request: {
			// 当前页码的参数名
			current: 'current',
			// 每页条数的参数名
			size: 'size',
		},
		// 响应数据的字段名映射
		response: {
			// 当前页码的字段名
			current: 'current',
			// 总页数的字段名
			pages: 'pages',
			// 每页条数的字段名
			size: 'size',
			// 总记录数的字段名
			total: 'total',
			// 数据列表的字段名
			records: 'list',
		},
	},

	// 文件上传处理方法，需要在使用时传入具体的上传函数
	upload: null,

	// 文件下载处理方法，需要在使用时传入具体的下载函数
	downFile: null,
};

/**
 * 日期时间组件类型枚举
 * 定义了所有支持的日期时间选择器类型
 * 用于在表格和搜索组件中识别日期类型的表单项
 */
export const dateType = [
	'time',          // 时间选择器
	'timeselect',    // 时间选择器（固定选项）
	'year',          // 年份选择器
	'years',         // 多年份选择器
	'month',         // 月份选择器
	'dates',         // 多日期选择器
	'date',          // 日期选择器
	'week',          // 周选择器
	'months',        // 多月份选择器
	'datetime',      // 日期时间选择器
	'datetimerange', // 日期时间范围选择器
	'daterange',     // 日期范围选择器
	'monthrange',    // 月份范围选择器
	'yearrange'      // 年份范围选择器
];

// 导出默认配置对象
export default defaultConfig;
