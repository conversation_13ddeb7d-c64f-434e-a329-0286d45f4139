<!--
  Co-Table 主表格组件 (Composition API 版本)

  这是一个功能完整的数据表格组件，提供以下核心功能：
  - 数据展示与分页
  - 内置搜索功能
  - 表格内编辑
  - 操作按钮与权限控制
  - 字典数据支持
  - 文件上传下载
  - 行选择与批量操作
  - 自定义渲染与插槽

  使用场景：
  - 后台管理系统的数据表格
  - 需要复杂交互的数据展示
  - 带搜索和操作的数据管理界面
-->
<template>
  <!--
    表格页面容器
    - class="zs-table-page": 应用表格页面样式
    - :style: 动态设置CSS变量，用于主题色和样式定制
  -->
  <div
    class="zs-table-page"
    :style="{
      '--highlight-color': highlightColor,
      '--selection-text': `'${selectionText}'`,
      '--header-color': tableConfig.mergedAttrs?.['header-cell-style']?.color,
    }"
  >
    <!-- 搜索表单区域 -->
    <template v-if="compatibility.isSearchEnabled && !compatibility.isExternalSearchMode">
      <!--
        搜索组件
        - ref="searchRef": 搜索组件引用
        - scene="inTable": 标识在表格内使用
        - :dic: 传递字典数据（只有在字典加载完成后才传递）
        - v-model:model: 双向绑定搜索表单数据
        - :config: 搜索表单配置
        - @search: 监听搜索事件
        - @change: 监听搜索条件变化事件
      -->
      <co-search
        ref="searchRef"
        scene="inTable"
        :dic="tableDictionary.dicLoaded ? tableDictionary.dicEnumData : null"
        v-model:model="searchFormData"
        :config="compatibility.unifiedSearchConfig"
        @search="tableEvents.handleSearch"
        @change="tableEvents.handleSearchChange"
      >
        <!-- 搜索操作按钮插槽 -->
        <template #search_operation="{ handle }">
          <slot name="search_operation" :handle="handle" />
        </template>
      </co-search>
    </template>

    <!--
      表格容器
      - v-loading="tableData.loading": 显示加载状态
      - class="zs-table-container": 应用表格容器样式
    -->
    <div v-loading="tableData.loading" class="zs-table-container">
      <!-- 顶部操作按钮区域 -->
      <div v-if="tableOperation.topOperationList.length > 0" class="top-operation">
        <!--
          顶部操作按钮插槽
          - name="topOperation": 插槽名称
          - :list="topOperationList": 传递操作按钮列表
        -->
        <slot name="topOperation" :list="tableOperation.topOperationList">
          <!--
            默认操作按钮渲染
            - v-for: 遍历操作按钮列表
            - :key="item.mark": 使用标识作为唯一键
            - :item: 传递按钮配置
            - @click: 处理按钮点击事件
          -->
          <co-button
            v-for="item in tableOperation.topOperationList"
            :key="item.mark"
            :item="item"
            @click="tableOperation.handleTopOperation(item)"
          />
        </slot>
        <!-- 顶部操作文本插槽 -->
        <slot name="topOperationText"></slot>
      </div>

      <!--
        表格容器组件
        - ref="tableFormRef": 表格表单引用
        - :model: 传递表格数据模型
        - :has-form-item: 是否包含表单项
        - :config-opts: 配置选项
      -->
      <co-container
        ref="tableFormRef"
        :model="{ data: tableData.tableData }"
        :has-form-item="tableConfig.hasFormItem"
        :config-opts="{ size: $attrs.size || 'default' }"
      >
        <!--
          Element Plus 表格组件
          - ref="elTableRef": 表格组件引用
          - :data: 表格数据
          - v-bind: 绑定所有表格属性
          - @selection-change: 选择变化事件
          - @current-change: 当前行变化事件
          - @row-click: 行点击事件
        -->
        <el-table
          :ref="setElTableRef"
          :data="tableData.tableData"
          v-bind="tableConfig.mergedAttrs"
          :row-key="tableConfig.rowKeyConfig"
          :tree-props="tableConfig.treePropsConfig"
          :row-class-name="tableSelection.getRowClassName"
          :header-cell-class-name="tableConfig.getHeaderCellClassName"
          @selection-change="tableEvents.handleSelectionChange"
          @current-change="tableEvents.handleCurrentChange"
          @row-click="tableEvents.handleRowClick"
        >
          <!-- 选择列 -->
          <el-table-column
            v-if="tableConfig.selectionConfig"
            v-bind="tableConfig.selectionConfig"
            type="selection"
            :class-name="tableConfig.selectionConfig.label ? 'selection-header' : ''"
          />

          <!-- 数据列 -->
          <el-table-column
            v-for="(item, index) in tableConfig.processedHeader"
            :key="item.prop || index"
            v-bind="item"
            :align="item.align || align"
          >
            <!-- 表头插槽 -->
            <template v-if="$slots[`${item.prop}_header`]" #header="scope">
              <slot :name="`${item.prop}_header`" v-bind="scope" :item="item" />
            </template>

            <!-- 单元格内容 -->
            <template #default="{ row, column, $index }">
              <!-- 自定义插槽 -->
              <slot
                :name="item.prop"
                :row="row"
                :column="column"
                :index="$index"
                :item="item"
              >
                <!-- 表单项模式 -->
                <template v-if="tableConfig.hasFormItem && ['input', 'select', 'switch', 'textarea', 'inputNumber', ...dateType].includes(item.type)">
                  <!-- 自定义表单项插槽 -->
                  <slot
                    v-if="$slots[`${item.prop}_form`]"
                    :name="`${item.prop}_form`"
                    :row="row"
                    :column="column"
                    :index="$index"
                    :item="item"
                  />

                  <!-- 默认表单项渲染 -->
                  <el-form-item
                    v-else
                    :key="row._selected"
                    :prop="row._propPath + '.' + item.prop"
                    :rules="row._selected || isValidate ? item?.rules : []"
                    :class="{ 'table-switch-align': align === 'center' }"
                  >
                    <!-- 表单组件内容插槽 -->
                    <slot
                      :name="item.prop"
                      :row="row"
                      :column="column"
                      :index="$index"
                      :item="item"
                    >
                      <!--
                        动态表单组件
                        - :is: 根据类型动态选择组件
                        - :type: 组件类型
                        - scene="inTable": 标识在表格内使用
                        - :form-ref: 表单引用
                        - :index: 行索引
                        - :item: 列配置
                        - :dic: 字典数据
                        - :row: 行数据
                        - :handle: 事件处理函数
                      -->
                      <component
                        :is="'co-' + (tableConfig.isDateType(item.type) ? 'date' : item.type)"
                        :type="item.type"
                        scene="inTable"
                        :form-ref="tableFormRef"
                        :index="row._index"
                        :item="item"
                        :dic="tableDictionary.dicEnumData"
                        :row="row"
                        :handle="tableEvents.handleOperation"
                      />
                    </slot>
                  </el-form-item>
                </template>

                <!-- 普通显示模式 -->
                <template v-else>
                  <!-- 静态组件渲染 -->
                  <static-component
                    v-if="item.type && !['operation'].includes(item.type)"
                    :item="item"
                    :column="column"
                    :index="row._index"
                    :row="row"
                    :handle="item.type === 'download' ? tableEvents.handleUpload : tableEvents.handleOperation"
                  />

                  <!-- 默认文本显示 -->
                  <span v-else>
                    {{ tableDictionary.getDictLabel(item.dicKey, row[item.prop]) || row[item.prop] }}
                  </span>
                </template>
              </slot>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column
            v-if="!tableConfig.hiddenOperation && tableOperation.inTableOperationList.length > 0"
            v-bind="tableOperation.operationConfig"
            :label="tableOperation.operationConfig.label"
            :width="tableOperation.operationConfig.width"
            :fixed="tableOperation.operationConfig.fixed"
            :align="tableOperation.operationConfig.align"
          >
            <template #default="{ row, $index }">
              <!-- 操作按钮组 -->
              <div class="table-operation-group">
                <!-- 显示的操作按钮 -->
                <co-button
                  v-for="item in tableOperation.getRowVisibleOperations(row)"
                  :key="item.mark"
                  :item="item"
                  @click="tableOperation.handleRowOperation(item, row, $index)"
                />

                <!-- 更多操作下拉菜单 -->
                <el-dropdown
                  v-if="tableOperation.hasMoreOperations(row)"
                  :trigger="tableOperation.operationConfig.more.trigger"
                  :hide-on-click="false"
                  style="padding-left: 10px"
                  @command="tableEvents.handleOperation"
                >
                  <co-button :item="tableOperation.operationConfig.more.attrs" dis-click>
                    {{ tableOperation.operationConfig.more.text }}<i class="el-icon-arrow-down el-icon--right" />
                  </co-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-for="item in tableOperation.getMoreOperations(row)"
                        :key="item.mark"
                        :command="{ field: item.mark, btn: item, row, index: $index, id: tableConfig.tableKey }"
                      >
                        {{ item.name }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </co-container>

      <!-- 分页组件 -->
      <div v-if="tableData.showPagination && tableData.pagination" class="pagination-container">
        <el-pagination
          v-bind="tableData.pagination"
          :page-size="tableData.pagination['page-size']"
          :current-page="tableData.pagination.current"
          :total="+tableData.pagination.total"
          @size-change="tableEvents.handleSizeChange"
          @current-change="tableEvents.handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
/**
 * Co-Table 主表格组件 (Composition API 版本)
 *
 * 基于 Element Plus Table 组件封装的功能完整的数据表格
 * 集成了所有功能模块的 Composable 函数，提供统一的表格解决方案
 *
 * 主要功能：
 * - 数据展示与分页
 * - 内置搜索功能
 * - 表格内编辑
 * - 操作按钮与权限控制
 * - 字典数据支持
 * - 文件上传下载
 * - 行选择与批量操作
 * - 自定义渲染与插槽
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0.0 (Composition API)
 */

import { ref, reactive, computed, watch, onMounted, nextTick, getCurrentInstance } from 'vue';

// 导入所有 Composable 函数
import { useCompatibility, createCompatibleProps } from './composables/useCompatibility.js';
import { useTableData } from './composables/useTableData.js';
import { useTableConfig } from './composables/useTableConfig.js';
import { useTableSelection } from './composables/useTableSelection.js';
import { useTableOperation } from './composables/useTableOperation.js';
import { useTableDictionary } from './composables/useTableDictionary.js';
import { useTableValidation } from './composables/useTableValidation.js';
import { useTableEvents } from './composables/useTableEvents.js';

// 导入组件和工具
import CoSearch from './search.vue';
import CoContainer from './components/co-container/index.vue';
import CoButton from './components/co-button/index.vue';
import StaticComponent from './components/static-component/index.vue';
import coFormItem from './components/co-form-item/index.js';
import defaultConfig, { dateType } from './config.js';
import Utils from './utils';

// 组件名称和配置
defineOptions({
  name: 'CoTable',
  components: {
    CoSearch,
    CoContainer,
    CoButton,
    StaticComponent,
    ...coFormItem,
  },
  inheritAttrs: false,
});

/**
 * 组件属性定义
 * 使用兼容性助手创建支持所有模式的props
 */
const props = defineProps(createCompatibleProps());

/**
 * 组件事件定义
 */
const emit = defineEmits([
  'search',           // 搜索事件
  'reset',            // 重置事件
  'search-change',    // 搜索条件变化事件
  'operation',        // 操作事件
  'loaded',           // 加载完成事件
  'dicLoaded',        // 字典加载完成事件
  'current-change',   // 当前行变化事件
  'selection-change', // 选择变化事件
  'page-change',      // 页码变化事件
  'size-change',      // 分页大小变化事件
  'row-click',        // 行点击事件
]);

// ==================== 响应式状态 ====================

// 获取当前组件实例
const instance = getCurrentInstance();

// 搜索表单数据
const searchFormData = reactive({});

// 组件引用
const searchRef = ref(null);
const tableFormRef = ref(null);
const elTableRef = ref(null);

// ==================== 兼容性处理 ====================

/**
 * 兼容性处理
 * 检测API模式并提供统一的配置接口
 */
const compatibility = useCompatibility(props);

// ==================== 核心功能模块 ====================

/**
 * 表格数据管理
 * 处理数据获取、分页、加载状态等
 */
const tableData = useTableData(
  compatibility.unifiedTableConfig,
  props.params,
  searchFormData,
  { loading: props.loading }
);

/**
 * 表格配置管理
 * 处理表头配置、样式配置、操作列配置等
 */
const tableConfig = useTableConfig(
  props,
  instance?.attrs || {},
  { defaultConfig }
);

/**
 * 表格选择管理
 * 处理行选择、高亮显示、选中状态等
 */
const tableSelection = useTableSelection(
  props,
  tableData,
  tableConfig,
  emit,
  { elTableRef }
);

/**
 * 表格操作管理
 * 处理操作按钮、权限控制、事件分发等
 */
const tableOperation = useTableOperation(
  compatibility.unifiedTableConfig,
  tableConfig,
  emit,
  { attrs: instance?.attrs || {} }
);

/**
 * 表格字典管理
 * 处理字典数据加载、缓存、转换等
 */
const tableDictionary = useTableDictionary(
  compatibility.unifiedTableConfig,
  emit
);

/**
 * 表格验证管理
 * 处理表格内编辑的表单验证等
 */
const tableValidation = useTableValidation(
  tableData,
  tableConfig,
  tableSelection,
  props
);

/**
 * 表格事件管理
 * 处理所有事件的统一管理和分发
 */
const tableEvents = useTableEvents(
  emit,
  tableData,
  tableConfig,
  tableSelection,
  tableDictionary,
  tableValidation,
  instance?.attrs || {}
);

// ==================== 计算属性 ====================

/**
 * 高亮颜色
 */
const highlightColor = computed(() => {
  return props.highlightColor || '';
});

/**
 * 选择文本
 */
const selectionText = computed(() => {
  return tableConfig.selectionText?.value || '';
});

/**
 * 数据对齐方式
 */
const align = computed(() => {
  return props.align || 'center';
});

/**
 * 是否验证所有表单
 */
const isValidate = computed(() => {
  return props.isValidate || false;
});

// ==================== 核心方法 ====================

/**
 * 设置Element Plus表格引用
 *
 * @param {Object} ref - 表格组件引用
 */
const setElTableRef = (ref) => {
  elTableRef.value = ref;
  tableEvents.setElTableRef(ref);
};

/**
 * 设置表单引用
 *
 * @param {Object} ref - 表单组件引用
 */
const setTableFormRef = (ref) => {
  tableFormRef.value = ref;
  tableEvents.setTableFormRef(ref?.formRef?.());
};

/**
 * 初始化表格
 * 设置各种引用和触发初始化事件
 */
const initTable = async () => {
  await nextTick();

  // 设置表格引用
  if (elTableRef.value) {
    tableEvents.setElTableRef(elTableRef.value);
  }

  // 设置表单引用
  if (tableFormRef.value?.formRef) {
    tableEvents.setTableFormRef(tableFormRef.value.formRef());
  }

  // 初始化字典数据
  await tableDictionary.initDictionaries();

  // 初始化操作按钮
  tableOperation.renderOperation();

  // 触发loaded事件
  tableEvents.handleLoaded();

  // 如果有API配置，自动加载数据
  if (tableData.hasRequestApi) {
    await tableData.fetchData();
  }
};

// ==================== 生命周期 ====================

/**
 * 组件挂载后的初始化
 */
onMounted(() => {
  initTable();
});

// ==================== 监听器 ====================

// 监听表格数据变化，设置行权限
watch(
  () => tableData.tableData,
  (newData) => {
    if (Array.isArray(newData)) {
      newData.forEach((row) => {
        tableOperation.setPermission(row);
      });
    }
  },
  { deep: true }
);

// ==================== 暴露方法 ====================

/**
 * 暴露给父组件的方法和属性
 */
defineExpose({
  // 数据相关方法
  getDataList: tableData.fetchData,
  setData: tableData.setData,
  setPage: tableData.setPage,
  setParams: tableData.setParams,

  // 选择相关方法
  clearSelection: tableSelection.clearSelection,
  setSelection: tableSelection.setSelection,
  toggleRowSelection: tableSelection.toggleRowSelection,

  // 验证相关方法
  validate: tableValidation.validate,
  clearValidate: tableValidation.clearValidate,

  // 配置相关方法
  setHeader: tableConfig.setHeader,

  // 组件引用
  elTableRef,
  searchRef,
  tableFormRef,

  // 数据状态
  tableData: tableData.tableData,
  selectedData: tableSelection.selectedData,
  loading: tableData.loading,

  // 兼容性方法（保持向后兼容）
  requestData: tableData.fetchData,
  singleClick: tableSelection.handleSingleClick,
});
</script>
