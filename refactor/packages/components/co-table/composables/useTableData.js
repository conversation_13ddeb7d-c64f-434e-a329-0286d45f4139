/**
 * Co-Table 表格数据管理 Composable
 *
 * 负责表格数据的获取、处理、分页和加载状态管理
 * 这是表格组件的核心数据层，处理API请求、数据格式化、分页逻辑、加载状态等功能
 *
 * 主要功能：
 * - 数据获取与API请求
 * - 数据格式化处理
 * - 分页状态管理
 * - 加载状态控制
 * - 数据缓存与更新
 * - 参数处理与过滤
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0.0 (Composition API)
 */

import { ref, reactive, computed, watch, nextTick } from 'vue';
import Utils from '../utils';
import tableJs from '../table.js';
import defaultConfig from '../config.js';

/**
 * 表格数据管理 Composable
 * 
 * @param {Object} config - 表格配置对象
 * @param {Object} params - 额外的请求参数
 * @param {Object} searchFormData - 搜索表单数据
 * @param {Object} options - 可选配置
 * @returns {Object} 数据管理相关的响应式状态和方法
 */
export function useTableData(config, params = {}, searchFormData = {}, options = {}) {
  // ==================== 响应式状态 ====================
  
  // 表格数据列表
  const dataList = ref([]);
  
  // 加载状态
  const loading = ref(false);
  
  // 分页配置
  const pagination = reactive({
    align: 'right',
    'page-size': 10,
    current: 1,
    total: 0,
    'page-sizes': [10, 20, 50, 100],
    layout: 'total, sizes, prev, pager, next, jumper',
    background: true,
  });
  
  // 是否显示分页组件
  const showPagination = ref(true);
  
  // 搜索参数缓存
  const searchParamsCache = ref({});
  
  // 私有属性键名（用于行唯一标识）
  const childrenKey = ref('children');
  
  // ==================== 计算属性 ====================
  
  /**
   * 处理后的表格数据
   * 包含私有属性和索引信息
   */
  const tableData = computed(() => {
    return dataList.value;
  });
  
  /**
   * 分页配置合并
   * 合并默认配置和用户配置
   */
  const mergedPagination = computed(() => {
    const configPagination = config.value?.pagination;
    
    if (Utils.getType(configPagination) === 'Object') {
      const merged = { ...pagination, ...configPagination };
      
      // 确保page-sizes包含当前page-size
      if (!configPagination['page-sizes'] && !merged['page-sizes'].includes(merged['page-size'])) {
        merged['page-sizes'].push(merged['page-size']);
      }
      
      return merged;
    }
    
    // 如果是布尔值，控制分页显示
    if (typeof configPagination === 'boolean') {
      showPagination.value = configPagination;
      return configPagination ? pagination : false;
    }
    
    return pagination;
  });
  
  /**
   * 是否有请求API配置
   */
  const hasRequestApi = computed(() => {
    return config.value?.request && config.value.request.apiName;
  });
  
  // ==================== 核心方法 ====================
  
  /**
   * 获取表格数据
   * 主要的数据请求方法，处理API调用、参数处理、数据格式化等
   * 
   * @param {Object} customParams - 自定义请求参数
   * @param {Function} apiName - 自定义API方法
   * @returns {Promise} 请求Promise
   */
  const fetchData = async (customParams = {}, apiName = null) => {
    const currentConfig = config.value;
    
    // 检查配置有效性
    if (!currentConfig?.request || !currentConfig.request.apiName) {
      console.warn('[Co-Table] 缺少request配置或apiName');
      return;
    }
    
    // 有自定义apiName时覆盖原配置
    if (apiName) {
      currentConfig.request.apiName = apiName;
    }
    
    // 解构请求配置
    const {
      apiName: requestApi,
      params: reqParams = {},
      headers = {},
      formatData = null,
      safeNullParams = false,
    } = currentConfig.request;
    
    if (!requestApi) {
      console.warn('[Co-Table] apiName不能为空');
      return;
    }
    
    // 获取分页配置
    const { request, response } = currentConfig.page
      ? Utils.deepMerge2(defaultConfig.page, currentConfig.page)
      : defaultConfig.page;
    
    // 合并请求参数
    const queryData = Object.assign({}, reqParams, searchFormData, params, customParams);
    
    // 过滤空值参数
    tableJs.filterNullParams(queryData, safeNullParams, Utils.getType(safeNullParams));
    
    // 缓存搜索参数
    searchParamsCache.value = queryData;
    
    // 设置加载状态
    const shouldShowLoading = options.loading !== false && (options.loading || defaultConfig.loading);
    if (shouldShowLoading) {
      loading.value = true;
    }
    
    try {
      // 构建请求参数
      const requestParams = {
        ...queryData,
      };
      
      // 添加分页参数
      if (showPagination.value && mergedPagination.value) {
        requestParams[request.size] = mergedPagination.value['page-size'];
        requestParams[request.current] = mergedPagination.value.current;
      }
      
      // 执行API请求
      const apiResult = requestApi(requestParams, headers);
      
      // 如果返回false，表示自定义处理
      if (apiResult === false) {
        return;
      }
      
      // 处理响应数据
      const { data } = await apiResult;
      let responseDataList = data[response.records];
      responseDataList = Array.isArray(data) || !responseDataList ? data : responseDataList;
      
      const dataIsObject = Utils.getType(responseDataList) === 'Object';
      
      // 处理空数据情况
      if ((dataIsObject && !responseDataList[response.records]) || !responseDataList) {
        dataList.value = [];
        return;
      }
      
      // 处理对象类型数据
      if (dataIsObject) {
        if (formatData) {
          dataList.value = formatData(responseDataList);
        }
        return;
      }
      
      // 数据格式化处理
      let processedData = responseDataList;
      if (formatData) {
        if (['AsyncFunction', 'Promise'].includes(Utils.getType(formatData))) {
          processedData = await formatData(responseDataList);
        } else {
          processedData = formatData(responseDataList);
        }
      }
      
      // 更新数据和分页信息
      dataList.value = processedData;
      
      if (mergedPagination.value) {
        pagination.total = data[response.total] || processedData.length;
      }
      
      // 设置私有属性
      setPrivateKey({ data: processedData });
      
      return processedData;
      
    } catch (error) {
      console.error('[Co-Table] 数据请求失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 设置表格数据
   * 直接设置表格数据，支持追加模式
   * 
   * @param {Array} data - 要设置的数据数组
   * @param {String} type - 设置类型：'push'追加到末尾，'unshift'追加到开头，默认替换
   * @returns {Object} 返回设置方法对象
   */
  const setData = (data, type = '') => {
    if (!Array.isArray(data)) {
      throw new Error('[Co-Table] setData: 参数必须是数组类型');
    }
    
    if (type && ['push', 'unshift'].includes(type)) {
      dataList.value[type](...data);
    } else {
      dataList.value = data;
    }
    
    // 设置私有属性
    setPrivateKey({ data: dataList.value });
    
    return {
      setData,
      setPage,
      setParams,
    };
  };
  
  /**
   * 设置分页参数
   * 
   * @param {Object} pageData - 分页数据对象
   * @returns {Object} 返回设置方法对象
   */
  const setPage = (pageData) => {
    Object.assign(pagination, pageData);
    
    return {
      setData,
      setPage,
      setParams,
    };
  };
  
  /**
   * 设置请求参数
   * 
   * @param {Object} newParams - 新的参数对象
   * @returns {Object} 返回设置方法对象
   */
  const setParams = (newParams) => {
    Object.assign(params, newParams);
    
    return {
      setData,
      setPage,
      setParams,
    };
  };
  
  /**
   * 为数据设置私有属性
   * 添加行索引、唯一标识等私有属性
   * 
   * @param {Object} options - 配置选项
   * @param {Array} options.data - 数据数组
   * @param {String} options.childrenKey - 子节点键名
   */
  const setPrivateKey = ({ data, childrenKey: customChildrenKey }) => {
    const keyName = customChildrenKey || childrenKey.value;
    
    if (!Array.isArray(data)) return;
    
    data.forEach((item, index) => {
      // 设置行索引
      item._index = index;
      
      // 设置唯一标识
      if (!item._uuid) {
        item._uuid = `row_${Date.now()}_${index}`;
      }
      
      // 设置表单验证路径
      item._propPath = `data[${index}]`;
      
      // 设置选中状态
      if (item._selected === undefined) {
        item._selected = false;
      }
      
      // 递归处理子节点
      if (item[keyName] && Array.isArray(item[keyName])) {
        setPrivateKey({ data: item[keyName], childrenKey: keyName });
      }
    });
  };
  
  /**
   * 分页处理方法
   * 处理分页大小和页码变化
   * 
   * @param {Number} value - 新的值
   * @param {String} type - 变化类型：'page-size' 或 'current'
   */
  const handlePaging = (value, type) => {
    if (type === 'page-size' && pagination.current > 1) {
      pagination.current = 1;
    }
    pagination[type] = value;
    
    // 重新获取数据
    if (hasRequestApi.value) {
      fetchData(searchParamsCache.value);
    }
  };
  
  // ==================== 监听器 ====================
  
  // 监听配置变化，更新分页设置
  watch(
    () => config.value?.pagination,
    (newPagination) => {
      if (typeof newPagination === 'boolean') {
        showPagination.value = newPagination;
      } else if (Utils.getType(newPagination) === 'Object') {
        Object.assign(pagination, newPagination);
      }
    },
    { immediate: true }
  );
  
  // ==================== 返回接口 ====================
  
  return {
    // 响应式状态
    dataList,
    tableData,
    loading,
    pagination: mergedPagination,
    showPagination,
    searchParamsCache,
    
    // 计算属性
    hasRequestApi,
    
    // 方法
    fetchData,
    setData,
    setPage,
    setParams,
    setPrivateKey,
    handlePaging,
    
    // 别名方法（保持向后兼容）
    getDataList: fetchData,
    requestData: fetchData,
  };
}

/**
 * 默认导出
 */
export default useTableData;
