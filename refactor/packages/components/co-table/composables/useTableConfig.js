/**
 * Co-Table 表格配置管理 Composable
 *
 * 负责表格配置的处理、表头配置、样式配置、操作列配置等
 * 处理表格的各种配置选项，包括列定义、样式设置、操作按钮配置等
 *
 * 主要功能：
 * - 配置对象合并与处理
 * - 表头配置动态处理
 * - 操作列配置管理
 * - 表格属性配置
 * - 选择列配置处理
 * - 样式类名管理
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0.0 (Composition API)
 */

import { ref, computed, watch } from 'vue';
import Utils from '../utils';
import defaultConfig, { dateType } from '../config.js';

/**
 * 表格配置管理 Composable
 * 
 * @param {Object} props - 组件props
 * @param {Object} attrs - 组件attrs
 * @param {Object} options - 可选配置
 * @returns {Object} 配置管理相关的响应式状态和方法
 */
export function useTableConfig(props, attrs = {}, options = {}) {
  // ==================== 响应式状态 ====================
  
  // 处理后的表头配置
  const processedHeader = ref([]);
  
  // 选择列配置
  const selectionConfig = ref(null);
  
  // 选择文本
  const selectionText = ref('');
  
  // 表格唯一标识
  const tableKey = ref(attrs.id || '');
  
  // 当前行键名
  const currentRowKey = ref('_uuid');
  
  // 子节点键名（树形结构）
  const childrenKey = ref('children');
  
  // 是否隐藏操作列
  const hiddenOperation = ref(false);
  
  // ==================== 计算属性 ====================
  
  /**
   * 合并后的表格配置
   * 将默认配置与用户配置进行深度合并
   */
  const mergedConfig = computed(() => {
    return Utils.deepMerge(defaultConfig, props.config || {});
  });
  
  /**
   * 合并后的表格属性
   * 将默认属性与传入的attrs合并
   */
  const mergedAttrs = computed(() => {
    return { ...defaultConfig.attrs, ...attrs };
  });
  
  /**
   * 操作列配置
   * 处理操作列的各种配置选项
   */
  const operationConfig = computed(() => {
    const config = mergedConfig.value;
    
    // 如果明确设置为false，隐藏操作列
    if (config.operation === false) {
      hiddenOperation.value = true;
      return null;
    }
    
    // 默认操作列配置
    const defaultOperationOpts = {
      showCount: -1,           // 显示按钮数量，-1表示显示全部
      fixed: false,            // 是否固定列
      trigger: 'hover',        // 下拉菜单触发方式
      align: 'center',         // 对齐方式
      label: '操作',           // 列标题
      width: 'auto',           // 列宽度
      more: {
        width: '200px',        // 下拉菜单宽度
        text: '更多',          // 更多按钮文本
        trigger: 'hover',      // 下拉菜单触发方式
        attrs: {               // 更多按钮属性
          type: 'text',
          size: attrs.size || 'default',
        },
      },
    };
    
    // 合并用户配置
    return config.operation 
      ? Utils.deepMerge(defaultOperationOpts, config.operation)
      : defaultOperationOpts;
  });
  
  /**
   * 表格列配置
   * 处理表头配置，过滤选择列
   */
  const tableHeader = computed(() => {
    return processedHeader.value;
  });
  
  /**
   * 是否包含表单项
   * 检查表头中是否包含可编辑的表单组件
   */
  const hasFormItem = computed(() => {
    return !!processedHeader.value.some((item) =>
      ['input', 'select', 'switch', 'textarea', 'inputNumber', ...dateType].includes(item.type)
    );
  });
  
  /**
   * 顶部操作按钮列表
   * 过滤出显示在表格顶部的操作按钮
   */
  const topOperationList = computed(() => {
    const operationList = operationConfig.value?.list || [];
    const currentTableKey = tableKey.value;
    
    return operationList.filter((btn) => 
      +btn.inTable === 2 && (btn.tableKey === currentTableKey || !btn.tableKey)
    );
  });
  
  /**
   * 表格行键名配置
   * 确定用于行唯一标识的字段名
   */
  const rowKeyConfig = computed(() => {
    return attrs['row-key'] || attrs['current-row-key'] || '_uuid';
  });
  
  /**
   * 树形结构配置
   * 获取树形数据的子节点字段名
   */
  const treePropsConfig = computed(() => {
    const treeProps = attrs['tree-props'];
    return {
      children: (treeProps && treeProps.children) || 'children',
      hasChildren: (treeProps && treeProps.hasChildren) || 'hasChildren',
    };
  });
  
  // ==================== 核心方法 ====================
  
  /**
   * 处理表头配置
   * 分离选择列和普通列，设置相关配置
   * 
   * @param {Array} headerConfig - 表头配置数组
   */
  const processHeader = (headerConfig) => {
    if (!Array.isArray(headerConfig)) {
      console.warn('[Co-Table] header配置必须是数组类型');
      return;
    }
    
    // 查找选择列配置
    const configHeader = mergedConfig.value?.header || headerConfig;
    const selection = configHeader.find((item) => item.type === 'selection');
    
    if (selection) {
      selectionConfig.value = selection;
      selectionText.value = selection.label || '';
      
      // 过滤掉选择列，只保留普通列
      processedHeader.value = headerConfig.filter((item) => 
        !['selection'].includes(item.type)
      );
    } else {
      selectionConfig.value = null;
      selectionText.value = '';
      processedHeader.value = [...headerConfig];
    }
  };
  
  /**
   * 设置表头列属性
   * 动态修改指定列的属性
   * 
   * @param {String} prop - 列的prop属性
   * @param {Object} data - 要设置的属性数据
   * @returns {Object} 返回设置方法对象
   */
  const setHeader = (prop, data) => {
    const target = processedHeader.value.find((item) => item.prop === prop);
    
    if (target) {
      Object.assign(target, data);
    } else {
      console.warn(`[Co-Table] 未找到prop为"${prop}"的列配置`);
    }
    
    return {
      setHeader,
    };
  };
  
  /**
   * 获取表头单元格样式类名
   * 处理选择列的特殊样式
   * 
   * @param {Object} data - 单元格数据
   * @returns {String} 样式类名
   */
  const getHeaderCellClassName = (data) => {
    const columnInfo = data.column;
    const hasLabel = columnInfo.type === 'selection' && selectionText.value;
    
    if (columnInfo.type === 'selection') {
      if (hasLabel) {
        return 'selection-header';
      }
    }
    
    // 处理其他自定义样式类名
    const customClassName = attrs['header-cell-class-name'];
    if (typeof customClassName === 'function') {
      return customClassName(data);
    } else if (typeof customClassName === 'string') {
      return customClassName;
    }
    
    return '';
  };
  
  /**
   * 获取行样式类名
   * 处理行高亮等样式
   * 
   * @param {Object} data - 行数据
   * @returns {String} 样式类名
   */
  const getRowClassName = (data) => {
    let className = '';
    
    // 处理自定义行样式类名
    const customClassName = attrs['row-class-name'];
    if (typeof customClassName === 'function') {
      className = customClassName(data);
    } else if (typeof customClassName === 'string') {
      className = customClassName;
    }
    
    // 处理高亮样式
    if (props.highlightColor && data.row._selected) {
      className += ' highlight-row';
    }
    
    return className;
  };
  
  /**
   * 初始化配置
   * 设置各种初始配置值
   */
  const initConfig = () => {
    // 设置表格唯一标识
    tableKey.value = attrs.id || '';
    
    // 设置当前行键名
    const rowKey = attrs['row-key'];
    currentRowKey.value = typeof rowKey === 'string' 
      ? rowKey 
      : (attrs['current-row-key'] || '_uuid');
    
    // 设置子节点键名
    const treeProps = attrs['tree-props'];
    childrenKey.value = (treeProps && treeProps.children) || 'children';
    
    // 设置操作列隐藏状态
    hiddenOperation.value = mergedConfig.value.operation === false;
  };
  
  /**
   * 检查是否为日期类型
   * 
   * @param {String} type - 组件类型
   * @returns {Boolean} 是否为日期类型
   */
  const isDateType = (type) => {
    return dateType.includes(type);
  };
  
  // ==================== 监听器 ====================
  
  // 监听表头配置变化
  watch(
    () => props.header,
    (newHeader) => {
      processHeader(newHeader || []);
    },
    { immediate: true }
  );
  
  // 监听配置变化，重新初始化
  watch(
    () => [props.config, attrs],
    () => {
      initConfig();
    },
    { immediate: true, deep: true }
  );
  
  // ==================== 初始化 ====================
  
  // 初始化配置
  initConfig();
  
  // ==================== 返回接口 ====================
  
  return {
    // 响应式状态
    processedHeader,
    selectionConfig,
    selectionText,
    tableKey,
    currentRowKey,
    childrenKey,
    hiddenOperation,
    
    // 计算属性
    mergedConfig,
    mergedAttrs,
    operationConfig,
    tableHeader,
    hasFormItem,
    topOperationList,
    rowKeyConfig,
    treePropsConfig,
    
    // 方法
    processHeader,
    setHeader,
    getHeaderCellClassName,
    getRowClassName,
    initConfig,
    isDateType,
    
    // 别名方法（保持向后兼容）
    cellClass: getHeaderCellClassName,
    _rowClassName: getRowClassName,
  };
}

/**
 * 默认导出
 */
export default useTableConfig;
