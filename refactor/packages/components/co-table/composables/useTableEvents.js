/**
 * Co-Table 表格事件管理 Composable
 *
 * 负责表格的各种事件处理，包括搜索事件、分页事件、操作事件、生命周期事件等的统一管理和分发
 * 这是表格组件的事件中心，处理所有用户交互和系统事件
 *
 * 主要功能：
 * - 搜索事件处理
 * - 分页事件管理
 * - 操作事件分发
 * - 生命周期事件
 * - 文件上传事件
 * - 表格交互事件
 * - 事件参数标准化
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0.0 (Composition API)
 */

import { ref, computed, watch, nextTick } from 'vue';
import Utils from '../utils';

/**
 * 表格事件管理 Composable
 * 
 * @param {Function} emit - 事件发射器
 * @param {Object} tableData - 表格数据管理
 * @param {Object} tableConfig - 表格配置管理
 * @param {Object} tableSelection - 表格选择管理
 * @param {Object} tableDictionary - 表格字典管理
 * @param {Object} tableValidation - 表格验证管理
 * @param {Object} attrs - 组件属性
 * @param {Object} options - 可选配置
 * @returns {Object} 事件管理相关的响应式状态和方法
 */
export function useTableEvents(
  emit, 
  tableData, 
  tableConfig, 
  tableSelection, 
  tableDictionary, 
  tableValidation,
  attrs = {},
  options = {}
) {
  // ==================== 响应式状态 ====================
  
  // 搜索参数缓存
  const searchParamsCache = ref({});
  
  // Element Plus表格引用
  const elTableRef = ref(null);
  
  // 表单引用
  const tableFormRef = ref(null);
  
  // ==================== 计算属性 ====================
  
  /**
   * 是否有请求API配置
   */
  const hasRequestApi = computed(() => {
    return tableData.hasRequestApi?.value || false;
  });
  
  /**
   * 表格唯一标识
   */
  const tableKey = computed(() => {
    return tableConfig.tableKey?.value || '';
  });
  
  /**
   * 分页配置
   */
  const pagination = computed(() => {
    return tableData.pagination?.value || {};
  });
  
  /**
   * 返回设置方法对象
   * 用于loaded事件回调
   */
  const returnSetFn = computed(() => {
    return {
      setData: tableData.setData,
      setPage: tableData.setPage,
      setParams: tableData.setParams,
    };
  });
  
  // ==================== 核心方法 ====================
  
  /**
   * 搜索事件处理
   * 处理搜索表单提交事件
   * 
   * @param {Object} searchData - 搜索表单数据
   */
  const handleSearch = (searchData) => {
    // 缓存搜索参数
    searchParamsCache.value = searchData;
    
    // 检查是否有自定义搜索处理器
    const customSearchHandler = attrs.search;
    if (typeof customSearchHandler === 'function') {
      return customSearchHandler(searchData);
    }
    
    // 默认搜索处理：重置分页并重新请求数据
    if (hasRequestApi.value) {
      // 重置到第一页
      if (pagination.value.current) {
        pagination.value.current = 1;
      }
      
      // 重新请求数据
      tableData.fetchData(searchData);
    }
    
    // 发送搜索事件
    emit('search', searchData);
  };
  
  /**
   * 搜索重置事件处理
   * 
   * @param {Object} resetData - 重置后的表单数据
   */
  const handleReset = (resetData) => {
    // 清空搜索参数缓存
    searchParamsCache.value = {};
    
    // 发送重置事件
    emit('reset', resetData);
    
    // 如果有API请求，重新加载数据
    if (hasRequestApi.value) {
      tableData.fetchData({});
    }
  };
  
  /**
   * 搜索条件变化事件处理
   * 
   * @param {String} prop - 变化的属性名
   * @param {*} value - 新的值
   */
  const handleSearchChange = (prop, value) => {
    emit('search-change', prop, value);
  };
  
  /**
   * 分页大小变化事件处理
   * 
   * @param {Number} size - 新的分页大小
   */
  const handleSizeChange = (size) => {
    // 更新分页配置
    if (pagination.value) {
      pagination.value['page-size'] = size;
      // 重置到第一页
      if (pagination.value.current > 1) {
        pagination.value.current = 1;
      }
    }
    
    // 重新请求数据
    if (hasRequestApi.value) {
      tableData.fetchData(searchParamsCache.value);
    } else {
      // 没有API时发送操作事件
      emit('operation', { type: 'page-size', value: size });
    }
    
    // 发送分页大小变化事件
    emit('size-change', size);
  };
  
  /**
   * 当前页变化事件处理
   * 
   * @param {Number} page - 新的页码
   */
  const handlePageChange = (page) => {
    // 更新分页配置
    if (pagination.value) {
      pagination.value.current = page;
    }
    
    // 重新请求数据
    if (hasRequestApi.value) {
      tableData.fetchData(searchParamsCache.value);
    } else {
      // 没有API时发送操作事件
      emit('operation', { type: 'current', value: page });
    }
    
    // 发送页码变化事件
    emit('page-change', page);
  };
  
  /**
   * 分页操作统一处理
   * 
   * @param {Number} value - 新的值
   * @param {String} type - 操作类型：'page-size' 或 'current'
   */
  const handlePaging = (value, type) => {
    if (type === 'page-size') {
      handleSizeChange(value);
    } else if (type === 'current') {
      handlePageChange(value);
    }
  };
  
  /**
   * 操作事件分发处理
   * 统一的操作事件处理入口
   * 
   * @param {Object} params - 操作参数
   */
  const handleOperation = (params) => {
    emit('operation', params);
  };
  
  /**
   * 表格加载完成事件
   * 在表格初始化完成后触发
   */
  const handleLoaded = () => {
    const loadedParams = {
      elTableRef: elTableRef.value,
      getDataList: tableData.fetchData,
      ...returnSetFn.value,
    };
    
    emit('loaded', loadedParams);
  };
  
  /**
   * 字典加载完成事件处理
   * 
   * @param {Object} dicEnumData - 字典数据
   */
  const handleDicLoaded = (dicEnumData) => {
    emit('dicLoaded', dicEnumData);
  };
  
  /**
   * 文件上传事件处理
   * 
   * @param {Object} data - 上传数据
   * @param {Object} row - 行数据
   * @param {Number} index - 行索引
   */
  const handleUpload = (data, row, index) => {
    // 处理文件上传逻辑
    const { fileUrl, fileName, linkProp } = data;
    
    if (linkProp) {
      // 查找对应的表头配置
      const findLinkItem = tableConfig.processedHeader?.value.find(
        (item) => item.prop === linkProp
      );
      
      // 根据类型设置对应的值
      const resValue = findLinkItem && findLinkItem.type === 'img' ? fileUrl : fileName;
      
      if (row[linkProp]) {
        row[linkProp] = resValue;
      } else {
        row[linkProp] = resValue;
      }
      
      return;
    }
    
    // 发送上传事件
    handleOperation({ 
      type: 'upload', 
      data, 
      row, 
      index,
      id: tableKey.value 
    });
  };
  
  /**
   * 表格行选择事件处理
   * 
   * @param {Array} selection - 选中的行数据
   */
  const handleSelectionChange = (selection) => {
    // 更新选择状态
    if (tableSelection.handleSelectionChange) {
      tableSelection.handleSelectionChange(selection);
    }
    
    // 发送选择变化事件
    emit('selection-change', selection);
  };
  
  /**
   * 当前行变化事件处理
   * 
   * @param {Object} currentRow - 当前行数据
   * @param {Object} oldCurrentRow - 之前的当前行数据
   */
  const handleCurrentChange = (currentRow, oldCurrentRow) => {
    // 更新当前行状态
    if (tableSelection.handleCurrentChange) {
      tableSelection.handleCurrentChange(currentRow, oldCurrentRow);
    }
    
    // 发送当前行变化事件
    emit('current-change', currentRow, oldCurrentRow);
  };
  
  /**
   * 行点击事件处理
   * 
   * @param {Object} row - 点击的行数据
   * @param {Object} column - 点击的列信息
   * @param {Event} event - 原始点击事件
   */
  const handleRowClick = (row, column, event) => {
    // 处理行选择逻辑
    if (tableSelection.handleRowClick) {
      tableSelection.handleRowClick(row, column, event);
    }
    
    // 发送行点击事件
    emit('row-click', row, column, event);
  };
  
  /**
   * 设置表格引用
   * 
   * @param {Object} ref - Element Plus表格组件引用
   */
  const setElTableRef = (ref) => {
    elTableRef.value = ref;
  };
  
  /**
   * 设置表单引用
   * 
   * @param {Object} ref - 表单组件引用
   */
  const setTableFormRef = (ref) => {
    tableFormRef.value = ref;
    
    // 同时设置验证管理的表单引用
    if (tableValidation.setFormRef) {
      tableValidation.setFormRef(ref);
    }
  };
  
  /**
   * 初始化事件监听
   * 设置各种事件监听器
   */
  const initEventListeners = () => {
    // 监听字典加载完成
    if (tableDictionary.dicLoaded) {
      watch(
        tableDictionary.dicLoaded,
        (loaded) => {
          if (loaded && tableDictionary.dicEnumData) {
            handleDicLoaded(tableDictionary.dicEnumData);
          }
        }
      );
    }
  };
  
  // ==================== 初始化 ====================
  
  // 初始化事件监听器
  initEventListeners();
  
  // ==================== 返回接口 ====================
  
  return {
    // 响应式状态
    searchParamsCache,
    elTableRef,
    tableFormRef,
    
    // 计算属性
    hasRequestApi,
    tableKey,
    pagination,
    returnSetFn,
    
    // 搜索相关事件
    handleSearch,
    handleReset,
    handleSearchChange,
    
    // 分页相关事件
    handleSizeChange,
    handlePageChange,
    handlePaging,
    
    // 操作相关事件
    handleOperation,
    handleUpload,
    
    // 生命周期事件
    handleLoaded,
    handleDicLoaded,
    
    // 表格交互事件
    handleSelectionChange,
    handleCurrentChange,
    handleRowClick,
    
    // 引用设置方法
    setElTableRef,
    setTableFormRef,
    
    // 别名方法（保持向后兼容）
    onSearch: handleSearch,
    dispatchHandle: handleOperation,
    onDownLoad: handleUpload,
  };
}

/**
 * 默认导出
 */
export default useTableEvents;
