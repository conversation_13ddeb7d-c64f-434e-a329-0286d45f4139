/**
 * Co-Table 表格验证管理 Composable
 *
 * 负责表格内编辑的表单验证功能
 * 处理表格单元格的编辑验证、错误提示、验证规则等
 *
 * 主要功能：
 * - 表格内表单验证
 * - 验证规则管理
 * - 错误状态跟踪
 * - 验证结果处理
 * - 表单引用管理
 * - 批量验证支持
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0.0 (Composition API)
 */

import { ref, reactive, computed, watch, nextTick } from 'vue';
import Utils from '../utils';

/**
 * 表格验证管理 Composable
 * 
 * @param {Object} tableData - 表格数据
 * @param {Object} tableHeader - 表格表头配置
 * @param {Object} tableSelection - 表格选择管理
 * @param {Object} props - 组件props
 * @param {Object} options - 可选配置
 * @returns {Object} 验证管理相关的响应式状态和方法
 */
export function useTableValidation(tableData, tableHeader, tableSelection, props, options = {}) {
  // ==================== 响应式状态 ====================
  
  // 验证错误信息存储
  const validationErrors = reactive({});
  
  // 表单引用
  const tableFormRef = ref(null);
  
  // 验证状态
  const validating = ref(false);
  
  // 最后一次验证结果
  const lastValidationResult = ref(true);
  
  // ==================== 计算属性 ====================
  
  /**
   * 是否启用验证
   * 根据props.isValidate或选择状态决定是否启用验证
   */
  const isValidationEnabled = computed(() => {
    return props.isValidate || !!tableSelection.selectionConfig?.value;
  });
  
  /**
   * 需要验证的表头列
   * 过滤出包含验证规则的列
   */
  const validatableColumns = computed(() => {
    return tableHeader.value.filter(col => col.rules && Array.isArray(col.rules));
  });
  
  /**
   * 是否有验证错误
   */
  const hasValidationErrors = computed(() => {
    return Object.keys(validationErrors).length > 0;
  });
  
  /**
   * 验证错误数量
   */
  const validationErrorCount = computed(() => {
    return Object.keys(validationErrors).length;
  });
  
  // ==================== 核心方法 ====================
  
  /**
   * 设置表单引用
   * 
   * @param {Object} formRef - 表单组件引用
   */
  const setFormRef = (formRef) => {
    tableFormRef.value = formRef;
  };
  
  /**
   * 验证单个字段
   * 
   * @param {Number} rowIndex - 行索引
   * @param {String} field - 字段名
   * @param {*} value - 字段值
   * @param {Array} rules - 验证规则数组
   * @param {Object} row - 行数据（用于自定义验证）
   * @returns {Boolean} 验证是否通过
   */
  const validateField = (rowIndex, field, value, rules, row = {}) => {
    if (!rules || !Array.isArray(rules)) {
      return true;
    }
    
    const errors = [];
    const fieldKey = `${rowIndex}-${field}`;
    
    // 遍历验证规则
    for (const rule of rules) {
      try {
        // 必填验证
        if (rule.required) {
          if (value === null || value === undefined || value === '') {
            errors.push(rule.message || `${field}为必填项`);
            continue;
          }
        }
        
        // 如果值为空且不是必填，跳过其他验证
        if ((value === null || value === undefined || value === '') && !rule.required) {
          continue;
        }
        
        // 最小长度验证
        if (rule.min !== undefined) {
          const length = typeof value === 'string' ? value.length : String(value).length;
          if (length < rule.min) {
            errors.push(rule.message || `${field}长度不能少于${rule.min}个字符`);
            continue;
          }
        }
        
        // 最大长度验证
        if (rule.max !== undefined) {
          const length = typeof value === 'string' ? value.length : String(value).length;
          if (length > rule.max) {
            errors.push(rule.message || `${field}长度不能超过${rule.max}个字符`);
            continue;
          }
        }
        
        // 正则表达式验证
        if (rule.pattern) {
          const regex = rule.pattern instanceof RegExp ? rule.pattern : new RegExp(rule.pattern);
          if (!regex.test(String(value))) {
            errors.push(rule.message || `${field}格式不正确`);
            continue;
          }
        }
        
        // 自定义验证函数
        if (rule.validator && typeof rule.validator === 'function') {
          const result = rule.validator(rule, value, (error) => {
            if (error) {
              errors.push(error.message || error);
            }
          }, { row, field, rowIndex });
          
          // 如果返回Promise，需要异步处理
          if (result instanceof Promise) {
            result.catch(error => {
              errors.push(error.message || error);
            });
          }
        }
        
        // 枚举值验证
        if (rule.enum && Array.isArray(rule.enum)) {
          if (!rule.enum.includes(value)) {
            errors.push(rule.message || `${field}值必须是${rule.enum.join('、')}中的一个`);
            continue;
          }
        }
        
        // 数值范围验证
        if (rule.type === 'number') {
          const numValue = Number(value);
          if (isNaN(numValue)) {
            errors.push(rule.message || `${field}必须是数字`);
            continue;
          }
          
          if (rule.min !== undefined && numValue < rule.min) {
            errors.push(rule.message || `${field}不能小于${rule.min}`);
            continue;
          }
          
          if (rule.max !== undefined && numValue > rule.max) {
            errors.push(rule.message || `${field}不能大于${rule.max}`);
            continue;
          }
        }
        
      } catch (error) {
        console.error(`[Co-Table] 验证规则执行失败:`, error);
        errors.push(`${field}验证失败`);
      }
    }
    
    // 更新验证错误状态
    if (errors.length > 0) {
      validationErrors[fieldKey] = errors;
      return false;
    } else {
      delete validationErrors[fieldKey];
      return true;
    }
  };
  
  /**
   * 验证单行数据
   * 
   * @param {Object} row - 行数据
   * @param {Number} rowIndex - 行索引
   * @returns {Boolean} 验证是否通过
   */
  const validateRow = (row, rowIndex) => {
    let isValid = true;
    
    validatableColumns.value.forEach(col => {
      const fieldValid = validateField(
        rowIndex, 
        col.prop, 
        row[col.prop], 
        col.rules, 
        row
      );
      
      if (!fieldValid) {
        isValid = false;
      }
    });
    
    return isValid;
  };
  
  /**
   * 验证整个表格
   * 
   * @param {Array} dataToValidate - 要验证的数据，默认为所有表格数据
   * @returns {Boolean} 验证是否通过
   */
  const validateTable = (dataToValidate = null) => {
    const data = dataToValidate || tableData.value;
    let isValid = true;
    
    // 清空之前的验证错误
    clearValidationErrors();
    
    if (!Array.isArray(data)) {
      return true;
    }
    
    data.forEach((row, rowIndex) => {
      const rowValid = validateRow(row, rowIndex);
      if (!rowValid) {
        isValid = false;
      }
    });
    
    lastValidationResult.value = isValid;
    return isValid;
  };
  
  /**
   * 使用Element Plus表单验证
   * 调用表单组件的原生验证方法
   * 
   * @param {Function} callback - 验证回调函数
   * @returns {Promise} 验证结果Promise
   */
  const validateWithForm = (callback) => {
    return new Promise((resolve) => {
      if (!tableFormRef.value) {
        console.warn('[Co-Table] 表单引用不存在，无法进行表单验证');
        const result = validateTable();
        if (callback) {
          callback(result);
        }
        resolve(result);
        return;
      }
      
      validating.value = true;
      
      tableFormRef.value.validate((valid) => {
        validating.value = false;
        lastValidationResult.value = valid;
        
        if (callback) {
          callback(valid);
        }
        
        resolve(valid);
      });
    });
  };
  
  /**
   * 主验证方法
   * 根据配置选择验证方式
   * 
   * @param {Function} callback - 验证回调函数
   * @returns {Promise} 验证结果Promise
   */
  const validate = (callback) => {
    if (!isValidationEnabled.value) {
      // 未启用验证，返回选中数据或全部数据
      const result = tableSelection.selectedData.value.length 
        ? tableSelection.selectedData.value 
        : tableData.value;
      
      if (callback) {
        return callback(result);
      }
      
      return Promise.resolve(result);
    }
    
    // 使用表单验证
    return validateWithForm((valid) => {
      const result = valid 
        ? (tableSelection.selectedData.value.length 
            ? tableSelection.selectedData.value 
            : tableData.value)
        : valid;
      
      if (callback) {
        return callback(result);
      }
      
      return result;
    });
  };
  
  /**
   * 清空验证错误
   * 
   * @param {String} fieldKey - 特定字段键，不传则清空所有
   */
  const clearValidationErrors = (fieldKey = null) => {
    if (fieldKey) {
      delete validationErrors[fieldKey];
    } else {
      Object.keys(validationErrors).forEach(key => {
        delete validationErrors[key];
      });
    }
  };
  
  /**
   * 清空表单验证
   * 调用Element Plus表单的clearValidate方法
   */
  const clearValidate = () => {
    if (tableFormRef.value) {
      tableFormRef.value.clearValidate();
    }
    clearValidationErrors();
  };
  
  /**
   * 获取字段验证错误
   * 
   * @param {Number} rowIndex - 行索引
   * @param {String} field - 字段名
   * @returns {Array} 错误信息数组
   */
  const getFieldErrors = (rowIndex, field) => {
    const fieldKey = `${rowIndex}-${field}`;
    return validationErrors[fieldKey] || [];
  };
  
  /**
   * 获取行验证错误
   * 
   * @param {Number} rowIndex - 行索引
   * @returns {Object} 行的所有字段错误
   */
  const getRowErrors = (rowIndex) => {
    const rowErrors = {};
    
    Object.keys(validationErrors).forEach(key => {
      if (key.startsWith(`${rowIndex}-`)) {
        const field = key.substring(`${rowIndex}-`.length);
        rowErrors[field] = validationErrors[key];
      }
    });
    
    return rowErrors;
  };
  
  // ==================== 监听器 ====================
  
  // 监听表格数据变化，清空验证错误
  watch(
    () => tableData.value,
    () => {
      clearValidationErrors();
    },
    { deep: true }
  );
  
  // ==================== 返回接口 ====================
  
  return {
    // 响应式状态
    validationErrors,
    tableFormRef,
    validating,
    lastValidationResult,
    
    // 计算属性
    isValidationEnabled,
    validatableColumns,
    hasValidationErrors,
    validationErrorCount,
    
    // 方法
    setFormRef,
    validateField,
    validateRow,
    validateTable,
    validateWithForm,
    validate,
    clearValidationErrors,
    clearValidate,
    getFieldErrors,
    getRowErrors,
  };
}

/**
 * 默认导出
 */
export default useTableValidation;
