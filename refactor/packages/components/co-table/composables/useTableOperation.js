/**
 * Co-Table 表格操作管理 Composable
 *
 * 负责表格操作按钮的管理，包括权限控制、按钮渲染、事件处理、下拉菜单等功能
 * 这是表格交互的核心部分，处理所有操作相关的逻辑
 *
 * 主要功能：
 * - 操作按钮列表管理
 * - 权限控制与过滤
 * - 按钮渲染逻辑
 * - 事件处理分发
 * - 下拉菜单管理
 * - 顶部操作按钮
 * - 行内操作按钮
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0.0 (Composition API)
 */

import { ref, reactive, computed, watch, getCurrentInstance } from 'vue';
import Utils from '../utils';
import tableJs from '../table.js';
import defaultConfig from '../config.js';

/**
 * 表格操作管理 Composable
 * 
 * @param {Object} config - 表格配置对象
 * @param {Object} tableConfig - 表格配置管理
 * @param {Function} emit - 事件发射器
 * @param {Object} options - 可选配置
 * @returns {Object} 操作管理相关的响应式状态和方法
 */
export function useTableOperation(config, tableConfig, emit, options = {}) {
  // ==================== 响应式状态 ====================
  
  // 操作按钮列表
  const operationList = ref([]);
  
  // 表格内行按钮数据（每行的按钮配置）
  const inTableRowBtn = reactive({});
  
  // 显示的权限按钮数据（过滤后的按钮）
  const showPermisBtn = reactive({});
  
  // 获取当前组件实例
  const instance = getCurrentInstance();
  
  // ==================== 计算属性 ====================
  
  /**
   * 操作列配置
   */
  const operationConfig = computed(() => {
    return tableConfig.operationConfig?.value || {};
  });
  
  /**
   * 是否隐藏操作列
   */
  const hiddenOperation = computed(() => {
    return tableConfig.hiddenOperation?.value || false;
  });
  
  /**
   * 表格唯一标识
   */
  const tableKey = computed(() => {
    return tableConfig.tableKey?.value || '';
  });
  
  /**
   * 当前行键名
   */
  const currentRowKey = computed(() => {
    return tableConfig.currentRowKey?.value || '_uuid';
  });
  
  /**
   * 顶部操作按钮列表
   * 过滤出显示在表格顶部的操作按钮
   */
  const topOperationList = computed(() => {
    const currentTableKey = tableKey.value;
    
    return operationList.value.filter((btn) => 
      +btn.inTable === 2 && (btn.tableKey === currentTableKey || !btn.tableKey)
    );
  });
  
  /**
   * 表格内操作按钮列表
   * 过滤出显示在表格行内的操作按钮
   */
  const inTableOperationList = computed(() => {
    return operationList.value.filter((btn) => +btn.inTable !== 2);
  });
  
  // ==================== 核心方法 ====================
  
  /**
   * 渲染操作按钮
   * 解析配置中的操作按钮，处理权限合并和按钮属性
   */
  const renderOperation = () => {
    const currentConfig = config.value;
    const attrs = options.attrs || {};
    
    const operList = currentConfig?.operation?.list || [];
    const operMerge = currentConfig?.operation?.merge;
    
    // 获取路由权限
    const metaPermis = instance?.proxy?.$route?.meta?.[defaultConfig.metaPermisKey] || [];
    
    let btnList = [...operList];
    
    // 处理权限合并
    if (metaPermis.length) {
      if (operMerge) {
        btnList = operMerge === 'push' 
          ? [...metaPermis, ...operList] 
          : [...operList, ...metaPermis];
      } else {
        btnList = metaPermis;
      }
    }
    
    // 处理按钮属性
    if (btnList.length) {
      btnList.forEach((btn) => {
        // 兼容旧系统字段
        if ('text' in btn) {
          btn.name = btn.text;
          delete btn.text;
        }
        if ('showType' in btn) {
          btn.type = btn.showType;
        }
        if ('tableName' in btn) {
          btn.tableKey = btn.tableName;
        }
        if ('class' in btn) {
          delete btn.class;
        }
        
        // 设置默认属性
        btn.size = btn.size || attrs.size || '';
        btn.loading = false;
        
        // 处理Element Plus的link text属性
        const btnAttrs = btn.attributes;
        if (btnAttrs && typeof btnAttrs === 'string') {
          const attrArr = btnAttrs.split(',');
          attrArr.forEach(key => {
            btn[key] = true;
          });
          delete btn.attributes;
        }
      });
      
      operationList.value = btnList;
    }
  };
  
  /**
   * 设置行权限
   * 为每一行数据设置对应的操作按钮权限
   * 
   * @param {Object} row - 行数据
   */
  const setPermission = (row) => {
    const currentTableKey = tableKey.value;
    const currentRowKeyValue = currentRowKey.value;
    const currentConfig = config.value;
    
    // 过滤并分析按钮权限
    const analysisBtn = inTableOperationList.value.filter((btn) => {
      const rule = btn.rule;
      
      // 处理存储规则
      if (rule && rule.includes('storage')) {
        tableJs.matchStorage(rule);
      }
      
      // 执行权限规则
      let ruleResult = true;
      if (rule) {
        try {
          const ruleFunction = new Function('row', 'storage', 'store', `return ${rule}`);
          ruleResult = ruleFunction(row, window.storage, null);
        } catch (error) {
          console.warn('[Co-Table] 权限规则执行失败:', rule, error);
          ruleResult = false;
        }
      }
      
      // 检查表格键匹配
      const tableKeyMatch = !btn.tableKey || currentTableKey.includes(btn.tableKey);
      const basicPermission = ruleResult && tableKeyMatch;
      
      // 检查隐藏列表
      const operation = currentConfig.operation;
      if (operation?.hiddenList) {
        return basicPermission && !operation.hiddenList(row, currentTableKey);
      }
      
      return basicPermission;
    });
    
    // 设置每行要显示的按钮
    const rowKey = currentRowKeyValue + row[currentRowKeyValue];
    inTableRowBtn[rowKey] = Utils.deepClone(analysisBtn);
    
    // 设置按钮折叠（根据showCount配置）
    const { showCount } = operationConfig.value;
    Object.keys(inTableRowBtn).forEach(key => {
      showPermisBtn[key] = showCount < 0 
        ? inTableRowBtn[key] 
        : inTableRowBtn[key].slice(0, showCount);
    });
  };
  
  /**
   * 操作事件分发处理
   * 统一的操作事件处理入口
   * 
   * @param {Object} params - 操作参数
   */
  const dispatchHandle = (params) => {
    emit('operation', params);
  };
  
  /**
   * 处理顶部操作按钮点击
   * 
   * @param {Object} btn - 按钮配置
   */
  const handleTopOperation = (btn) => {
    dispatchHandle({
      field: btn.mark,
      btn: btn,
      id: tableKey.value,
    });
  };
  
  /**
   * 处理行内操作按钮点击
   * 
   * @param {Object} btn - 按钮配置
   * @param {Object} row - 行数据
   * @param {Number} index - 行索引
   */
  const handleRowOperation = (btn, row, index) => {
    dispatchHandle({
      field: btn.mark,
      btn: btn,
      row: row,
      index: index,
      id: tableKey.value,
    });
  };
  
  /**
   * 处理下拉菜单命令
   * 
   * @param {Object} command - 下拉菜单命令对象
   */
  const handleDropdownCommand = (command) => {
    dispatchHandle(command);
  };
  
  /**
   * 获取行的操作按钮列表
   * 
   * @param {Object} row - 行数据
   * @returns {Array} 操作按钮列表
   */
  const getRowOperations = (row) => {
    const rowKey = currentRowKey.value + row[currentRowKey.value];
    return inTableRowBtn[rowKey] || [];
  };
  
  /**
   * 获取行的显示按钮列表
   * 
   * @param {Object} row - 行数据
   * @returns {Array} 显示的按钮列表
   */
  const getRowVisibleOperations = (row) => {
    const rowKey = currentRowKey.value + row[currentRowKey.value];
    return showPermisBtn[rowKey] || [];
  };
  
  /**
   * 检查是否有更多操作按钮
   * 
   * @param {Object} row - 行数据
   * @returns {Boolean} 是否有更多按钮
   */
  const hasMoreOperations = (row) => {
    const rowKey = currentRowKey.value + row[currentRowKey.value];
    const allBtns = inTableRowBtn[rowKey] || [];
    const visibleBtns = showPermisBtn[rowKey] || [];
    
    return allBtns.length > visibleBtns.length;
  };
  
  /**
   * 获取更多操作按钮列表
   * 
   * @param {Object} row - 行数据
   * @returns {Array} 更多按钮列表
   */
  const getMoreOperations = (row) => {
    const rowKey = currentRowKey.value + row[currentRowKey.value];
    const allBtns = inTableRowBtn[rowKey] || [];
    const { showCount } = operationConfig.value;
    
    return showCount < 0 ? [] : allBtns.slice(showCount);
  };
  
  /**
   * 清空操作按钮数据
   */
  const clearOperations = () => {
    operationList.value = [];
    Object.keys(inTableRowBtn).forEach(key => {
      delete inTableRowBtn[key];
    });
    Object.keys(showPermisBtn).forEach(key => {
      delete showPermisBtn[key];
    });
  };
  
  // ==================== 监听器 ====================
  
  // 监听配置变化，重新渲染操作按钮
  watch(
    () => config.value?.operation,
    () => {
      renderOperation();
    },
    { immediate: true, deep: true }
  );
  
  // ==================== 返回接口 ====================
  
  return {
    // 响应式状态
    operationList,
    inTableRowBtn,
    showPermisBtn,
    
    // 计算属性
    operationConfig,
    hiddenOperation,
    tableKey,
    currentRowKey,
    topOperationList,
    inTableOperationList,
    
    // 方法
    renderOperation,
    setPermission,
    dispatchHandle,
    handleTopOperation,
    handleRowOperation,
    handleDropdownCommand,
    getRowOperations,
    getRowVisibleOperations,
    hasMoreOperations,
    getMoreOperations,
    clearOperations,
    
    // 别名方法（保持向后兼容）
    setPermis: setPermission,
  };
}

/**
 * 默认导出
 */
export default useTableOperation;
