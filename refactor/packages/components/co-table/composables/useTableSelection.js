/**
 * Co-Table 表格选择管理 Composable
 *
 * 负责表格行选择功能，包括单选、多选、高亮显示、选中状态管理等
 * 处理行选择的各种交互逻辑和状态管理
 *
 * 主要功能：
 * - 单选和多选模式管理
 * - 行选择状态控制
 * - 高亮显示处理
 * - 选择事件分发
 * - 树形结构级联选择
 * - 选中数据管理
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0.0 (Composition API)
 */

import { ref, reactive, computed, watch, nextTick } from 'vue';
import Utils from '../utils';

/**
 * 表格选择管理 Composable
 * 
 * @param {Object} props - 组件props
 * @param {Object} tableData - 表格数据
 * @param {Object} tableConfig - 表格配置
 * @param {Function} emit - 事件发射器
 * @param {Object} options - 可选配置
 * @returns {Object} 选择管理相关的响应式状态和方法
 */
export function useTableSelection(props, tableData, tableConfig, emit, options = {}) {
  // ==================== 响应式状态 ====================
  
  // 多选模式下的选中数据数组
  const selectedData = ref([]);
  
  // 单选模式下的选中数据
  const singleData = ref('');
  
  // 当前行（用于高亮显示）
  const currentRow = ref(null);
  
  // 表格内行按钮数据
  const inTableRowBtn = reactive({});
  
  // 显示的权限按钮数据
  const showPermisBtn = reactive({});
  
  // ==================== 计算属性 ====================
  
  /**
   * 选择列配置
   * 从表头配置中提取selection类型的列
   */
  const selectionConfig = computed(() => {
    return tableConfig.selectionConfig?.value || null;
  });
  
  /**
   * 是否为单选模式
   */
  const isSingleMode = computed(() => {
    return !!props.singleMode;
  });
  
  /**
   * 当前行键名
   */
  const currentRowKey = computed(() => {
    return tableConfig.currentRowKey?.value || '_uuid';
  });
  
  /**
   * 子节点键名（树形结构）
   */
  const childrenKey = computed(() => {
    return tableConfig.childrenKey?.value || 'children';
  });
  
  /**
   * 表格唯一标识
   */
  const tableKey = computed(() => {
    return tableConfig.tableKey?.value || '';
  });
  
  /**
   * 是否启用点击行选择
   */
  const isCurrentRowEnabled = computed(() => {
    return !!props.currentRow;
  });
  
  /**
   * 高亮颜色
   */
  const highlightColor = computed(() => {
    return props.highlightColor || '';
  });
  
  // ==================== 核心方法 ====================
  
  /**
   * 处理行选择变化（多选）
   * Element Plus表格的selection-change事件处理
   * 
   * @param {Array} selection - 当前选中的行数据数组
   */
  const handleSelectionChange = (selection) => {
    selectedData.value = selection;
    
    // 更新行的选中状态标记
    if (Array.isArray(tableData.value)) {
      tableData.value.forEach(row => {
        row._selected = selection.some(item => 
          item[currentRowKey.value] === row[currentRowKey.value]
        );
      });
    }
    
    // 发送选择变化事件
    emit('selection-change', selection);
  };
  
  /**
   * 处理当前行变化（单选高亮）
   * Element Plus表格的current-change事件处理
   * 
   * @param {Object} row - 当前选中的行数据
   * @param {Object} oldRow - 之前选中的行数据
   */
  const handleCurrentChange = (row, oldRow) => {
    currentRow.value = row;
    
    // 发送当前行变化事件
    emit('current-change', row, oldRow);
  };
  
  /**
   * 处理行点击事件
   * 根据配置决定是否触发行选择
   * 
   * @param {Object} row - 点击的行数据
   * @param {Object} column - 点击的列信息
   * @param {Event} event - 原始点击事件
   */
  const handleRowClick = (row, column, event) => {
    // 如果启用了点击行选择功能
    if (isCurrentRowEnabled.value) {
      handleCurrentChange(row, currentRow.value);
    }
    
    // 发送行点击事件
    emit('row-click', row, column, event);
  };
  
  /**
   * 单选处理方法
   * 处理单选模式下的行选择
   * 
   * @param {*} val - 选择值
   * @param {Object} row - 行数据
   * @param {Number} index - 行索引
   * @param {String} from - 调用来源
   */
  const handleSingleClick = (val, row, index, from = '') => {
    // 设置当前行
    if (options.elTableRef?.value) {
      options.elTableRef.value.setCurrentRow(row);
    }
    
    // 切换行的选中状态
    row._selected = !row._selected;
    
    // 更新单选数据
    selectedData.value.length = 0;
    selectedData.value.push(row);
    singleData.value = row[currentRowKey.value];
    
    // 发送选择事件
    if (!from) {
      const hasChildren = row[childrenKey.value] && row[childrenKey.value].length;
      emit('select', {
        row,
        index: hasChildren ? row._index : index,
        tableId: tableKey.value,
      });
    }
  };
  
  /**
   * 切换行选择状态
   * 支持单选和多选模式，支持树形结构级联选择
   * 
   * @param {Object} row - 要切换的行数据
   * @param {Boolean} selected - 目标选中状态
   */
  const toggleRowSelection = (row, selected = true) => {
    const cRowKey = currentRowKey.value;
    const childKey = childrenKey.value;
    
    if (isSingleMode.value) {
      // 单选模式
      singleData.value = row[cRowKey];
      selectedData.value = [row];
    } else {
      // 多选模式：递归处理
      const setToggleRow = (row, selected) => {
        row._selected = selected;
        
        if (selected) {
          // 选中：添加到选中数组（非父节点）
          if (!row[childKey]) {
            selectedData.value.push(row);
          }
        } else {
          // 取消选中：从选中数组中移除
          const index = selectedData.value.findIndex(item => 
            item[cRowKey] === row[cRowKey]
          );
          if (index > -1) {
            selectedData.value.splice(index, 1);
          }
        }
        
        // 递归处理子节点
        if (row[childKey] && Array.isArray(row[childKey])) {
          row[childKey].forEach(child => {
            setToggleRow(child, selected);
          });
        }
      };
      
      setToggleRow(row, selected);
    }
    
    // 调用Element Plus表格的原生方法
    if (options.elTableRef?.value) {
      options.elTableRef.value.toggleRowSelection(row, selected);
    }
  };
  
  /**
   * 获取行样式类名
   * 处理选中行的高亮样式
   * 
   * @param {Object} data - 行数据对象 {row, rowIndex}
   * @returns {String} 样式类名
   */
  const getRowClassName = (data) => {
    let className = '';
    
    // 处理自定义行样式类名
    const customClassName = options.rowClassName;
    if (typeof customClassName === 'function') {
      className = customClassName(data);
    } else if (typeof customClassName === 'string') {
      className = customClassName;
    }
    
    // 处理高亮样式
    if (highlightColor.value) {
      const cutRowKey = currentRowKey.value;
      
      // 检查是否为选中行
      for (const selectedRow of selectedData.value) {
        if (selectedRow[cutRowKey] === data.row[cutRowKey]) {
          className += ' row-highlight-color';
          break;
        }
      }
      
      // 检查是否为当前行（单选高亮）
      if (options.highlightCurrentRow && currentRow.value === data.row) {
        className += ' current-row-color';
      }
    }
    
    return className.trim();
  };
  
  /**
   * 清空选择
   * 清空所有选中状态
   */
  const clearSelection = () => {
    selectedData.value.length = 0;
    singleData.value = '';
    currentRow.value = null;
    
    // 清空表格选择
    if (options.elTableRef?.value) {
      options.elTableRef.value.clearSelection();
    }
    
    // 清空行的选中标记
    if (Array.isArray(tableData.value)) {
      tableData.value.forEach(row => {
        row._selected = false;
      });
    }
  };
  
  /**
   * 设置选中行
   * 
   * @param {Array} rows - 要选中的行数据数组
   */
  const setSelection = (rows) => {
    if (!Array.isArray(rows)) {
      console.warn('[Co-Table] setSelection: 参数必须是数组类型');
      return;
    }
    
    // 先清空选择
    clearSelection();
    
    // 设置新的选择
    rows.forEach(row => {
      toggleRowSelection(row, true);
    });
  };
  
  /**
   * 表单验证方法
   * 验证选中的数据
   * 
   * @param {Function} callback - 验证回调函数
   * @returns {Promise} 验证结果
   */
  const validate = (callback) => {
    let result = true;
    const { isValidate } = props;
    
    if (isValidate || selectionConfig.value) {
      // 这里可以添加表单验证逻辑
      // 目前返回选中的数据
    }
    
    const res = result 
      ? (selectedData.value.length ? selectedData.value : tableData.value) 
      : result;
    
    if (callback) {
      return callback(res);
    }
    
    return Promise.resolve(res);
  };
  
  // ==================== 监听器 ====================
  
  // 监听表格数据变化，重置选择状态
  watch(
    () => tableData.value,
    (newData) => {
      if (Array.isArray(newData)) {
        // 重置所有行的选中状态
        newData.forEach(row => {
          if (row._selected === undefined) {
            row._selected = false;
          }
        });
      }
    },
    { immediate: true, deep: true }
  );
  
  // ==================== 返回接口 ====================
  
  return {
    // 响应式状态
    selectedData,
    singleData,
    currentRow,
    inTableRowBtn,
    showPermisBtn,
    
    // 计算属性
    selectionConfig,
    isSingleMode,
    currentRowKey,
    childrenKey,
    tableKey,
    isCurrentRowEnabled,
    highlightColor,
    
    // 方法
    handleSelectionChange,
    handleCurrentChange,
    handleRowClick,
    handleSingleClick,
    toggleRowSelection,
    getRowClassName,
    clearSelection,
    setSelection,
    validate,
    
    // 别名方法（保持向后兼容）
    singleClick: handleSingleClick,
    _rowClassName: getRowClassName,
  };
}

/**
 * 默认导出
 */
export default useTableSelection;
