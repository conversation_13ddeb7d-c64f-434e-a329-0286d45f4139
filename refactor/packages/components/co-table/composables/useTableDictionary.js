/**
 * Co-Table 表格字典管理 Composable
 *
 * 负责字典数据的加载、缓存、转换等功能
 * 处理表格中的字典字段显示和数据转换
 *
 * 主要功能：
 * - 字典数据加载（本地/远程）
 * - 字典数据缓存管理
 * - 字典值转换显示
 * - 异步加载状态控制
 * - 字典颜色样式处理
 * - 字典过滤器支持
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0.0 (Composition API)
 */

import { ref, reactive, computed, watch, nextTick } from 'vue';
import Utils from '../utils';
import defaultConfig from '../config.js';

/**
 * 表格字典管理 Composable
 * 
 * @param {Object} config - 表格配置对象
 * @param {Function} emit - 事件发射器
 * @param {Object} options - 可选配置
 * @returns {Object} 字典管理相关的响应式状态和方法
 */
export function useTableDictionary(config, emit, options = {}) {
  // ==================== 响应式状态 ====================
  
  // 字典枚举对象（用于模板使用设置内容及样式）
  const dicEnum = reactive({});
  
  // 字典枚举数据（用于渲染下拉框等组件）
  const dicEnumData = reactive({});
  
  // 字典加载状态
  const loading = ref(false);
  
  // 已加载的字典数量
  const loadDicNum = ref(0);
  
  // 字典键名数组
  const dicKeyArr = ref([]);
  
  // ==================== 计算属性 ====================
  
  /**
   * 字典是否全部加载完成
   */
  const dicLoaded = computed(() => {
    return loadDicNum.value === dicKeyArr.value.length;
  });
  
  /**
   * 字典配置
   */
  const dicConfig = computed(() => {
    return config.value?.dic || {};
  });
  
  /**
   * 字典键名配置
   */
  const dicKeys = computed(() => {
    return defaultConfig.dicKeys || ['label', 'value'];
  });
  
  /**
   * 获取字典方法
   */
  const getDicMethod = computed(() => {
    return defaultConfig.getDic;
  });
  
  // ==================== 核心方法 ====================
  
  /**
   * 初始化字典数据
   * 解析字典配置，开始加载字典数据
   */
  const initDictionaries = async () => {
    const currentDicConfig = dicConfig.value;
    
    // 重置状态
    resetDictionaries();
    
    // 获取字典键名数组
    dicKeyArr.value = currentDicConfig ? Object.keys(currentDicConfig) : [];
    
    if (dicKeyArr.value.length === 0) {
      // 没有字典配置，直接完成
      return Promise.resolve();
    }
    
    loading.value = true;
    
    try {
      // 使用nextTick确保DOM更新后再加载字典
      await nextTick();
      
      // 遍历字典配置，逐个加载
      const loadPromises = [];
      
      for (const [dicKey, dicBody] of Object.entries(currentDicConfig)) {
        // 判断是否为远程字典
        if (isRemoteDictionary(dicBody)) {
          loadPromises.push(loadRemoteDictionary(dicKey, dicBody));
        } else {
          loadPromises.push(transformDictionary(dicKey, dicBody));
        }
      }
      
      // 等待所有字典加载完成
      await Promise.all(loadPromises);
      
    } catch (error) {
      console.error('[Co-Table] 字典初始化失败:', error);
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 判断是否为远程字典
   * 
   * @param {*} dicBody - 字典配置体
   * @returns {Boolean} 是否为远程字典
   */
  const isRemoteDictionary = (dicBody) => {
    return (
      typeof dicBody === 'function' ||
      typeof dicBody === 'string' ||
      (dicBody.value && Utils.getType(dicBody.value) === 'String') ||
      (dicBody.data && Utils.getType(dicBody.data) === 'String')
    );
  };
  
  /**
   * 加载远程字典数据
   * 
   * @param {String} dicKey - 字典键名
   * @param {*} dicBody - 字典配置体
   * @returns {Promise} 加载Promise
   */
  const loadRemoteDictionary = async (dicKey, dicBody) => {
    const getDic = getDicMethod.value;
    
    if (!getDic) {
      throw new Error('[Co-Table] 缺少getDic方法配置');
    }
    
    try {
      // 获取请求参数
      const params = typeof dicBody === 'string' || typeof dicBody === 'function' 
        ? dicBody 
        : dicBody.value || dicBody.data;
      
      // 确定请求方法
      let method = typeof dicBody === 'function' ? eval(dicBody) : getDic;
      
      // 执行请求
      const response = await method(params);
      
      // 转换字典数据
      await transformDictionary(dicKey, { 
        data: response, 
        color: dicBody.color,
        filter: dicBody.filter 
      });
      
    } catch (error) {
      console.error(`[Co-Table] 远程字典加载失败: ${dicKey}`, error);
      // 即使失败也要增加计数，避免阻塞
      loadDicNum.value++;
    }
  };
  
  /**
   * 转换字典数据
   * 将原始字典数据转换为组件可用的格式
   * 
   * @param {String} dicKey - 字典键名
   * @param {Object} dicBody - 字典配置体
   * @returns {Promise} 转换Promise
   */
  const transformDictionary = async (dicKey, dicBody) => {
    const { data, color, filter = null } = dicBody;
    const [labelKey, valueKey] = dicKeys.value;
    
    try {
      // 记录字典执行数量
      loadDicNum.value++;
      
      // 应用过滤器
      const dicData = filter && typeof filter === 'function' 
        ? filter(data) 
        : (data || []);
      
      if (!Array.isArray(dicData) || dicData.length === 0) {
        console.warn(`[Co-Table] 字典数据为空: ${dicKey}`);
        return;
      }
      
      // 创建字典枚举对象（用于模板显示）
      dicEnum[dicKey] = {
        data: Object.fromEntries(
          dicData.map((item) => [item[valueKey], item[labelKey]])
        ),
        color: color || null,
      };
      
      // 创建字典枚举数据（用于渲染组件）
      dicEnumData[dicKey] = dicData;
      
    } catch (error) {
      console.error(`[Co-Table] 字典转换失败: ${dicKey}`, error);
    }
  };
  
  /**
   * 获取字典显示值
   * 根据字典键和值获取对应的显示文本
   * 
   * @param {String} dictKey - 字典键名
   * @param {*} value - 字典值
   * @returns {String} 显示文本
   */
  const getDictLabel = (dictKey, value) => {
    const dict = dicEnum[dictKey];
    
    if (!dict || !dict.data) {
      return value;
    }
    
    return dict.data[value] || value;
  };
  
  /**
   * 获取字典颜色样式
   * 根据字典键和值获取对应的颜色样式
   * 
   * @param {String} dictKey - 字典键名
   * @param {*} value - 字典值
   * @returns {String|null} 颜色样式
   */
  const getDictColor = (dictKey, value) => {
    const dict = dicEnum[dictKey];
    
    if (!dict || !dict.color) {
      return null;
    }
    
    return dict.color[value] || null;
  };
  
  /**
   * 获取字典选项列表
   * 获取指定字典的完整选项列表
   * 
   * @param {String} dictKey - 字典键名
   * @returns {Array} 字典选项列表
   */
  const getDictOptions = (dictKey) => {
    return dicEnumData[dictKey] || [];
  };
  
  /**
   * 检查字典是否存在
   * 
   * @param {String} dictKey - 字典键名
   * @returns {Boolean} 字典是否存在
   */
  const hasDictionary = (dictKey) => {
    return !!dicEnum[dictKey];
  };
  
  /**
   * 重置字典数据
   * 清空所有字典相关状态
   */
  const resetDictionaries = () => {
    // 清空字典数据
    Object.keys(dicEnum).forEach(key => {
      delete dicEnum[key];
    });
    Object.keys(dicEnumData).forEach(key => {
      delete dicEnumData[key];
    });
    
    // 重置状态
    loadDicNum.value = 0;
    dicKeyArr.value = [];
    loading.value = false;
  };
  
  /**
   * 添加字典数据
   * 动态添加新的字典数据
   * 
   * @param {String} dictKey - 字典键名
   * @param {Array} data - 字典数据
   * @param {Object} options - 可选配置
   */
  const addDictionary = (dictKey, data, options = {}) => {
    const { color, filter } = options;
    
    transformDictionary(dictKey, {
      data,
      color,
      filter,
    });
  };
  
  /**
   * 移除字典数据
   * 
   * @param {String} dictKey - 字典键名
   */
  const removeDictionary = (dictKey) => {
    if (dicEnum[dictKey]) {
      delete dicEnum[dictKey];
    }
    if (dicEnumData[dictKey]) {
      delete dicEnumData[dictKey];
    }
    
    // 更新键名数组
    const index = dicKeyArr.value.indexOf(dictKey);
    if (index > -1) {
      dicKeyArr.value.splice(index, 1);
    }
  };
  
  // ==================== 监听器 ====================
  
  // 监听字典配置变化，重新初始化
  watch(
    () => config.value?.dic,
    () => {
      initDictionaries();
    },
    { immediate: true, deep: true }
  );
  
  // 监听字典加载完成状态
  watch(
    dicLoaded,
    (loaded) => {
      if (loaded && dicKeyArr.value.length > 0) {
        // 发送字典加载完成事件
        emit('dicLoaded', dicEnumData);
      }
    }
  );
  
  // ==================== 返回接口 ====================
  
  return {
    // 响应式状态
    dicEnum,
    dicEnumData,
    loading,
    loadDicNum,
    dicKeyArr,
    
    // 计算属性
    dicLoaded,
    dicConfig,
    dicKeys,
    
    // 方法
    initDictionaries,
    loadRemoteDictionary,
    transformDictionary,
    getDictLabel,
    getDictColor,
    getDictOptions,
    hasDictionary,
    resetDictionaries,
    addDictionary,
    removeDictionary,
    
    // 别名方法（保持向后兼容）
    getRemoteDic: loadRemoteDictionary,
    transferDic: transformDictionary,
  };
}

/**
 * 默认导出
 */
export default useTableDictionary;
