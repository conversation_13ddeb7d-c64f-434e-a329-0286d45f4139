<!--
  Co-Search 搜索组件 (Composition API 版本)

  功能特性：
  - 动态表单项渲染
  - 支持多种表单组件类型
  - 支持前置组件和分割字段
  - 支持搜索和重置功能
  - 支持自定义样式配置
  - 支持插槽自定义

  使用场景：
  - 表格顶部搜索表单
  - 独立的搜索表单
  - 需要动态配置的查询界面
-->
<template>
	<!--
		Element Plus 表单组件
		- ref="searchFormRef": 表单引用，用于访问表单方法
		- :model="model": 绑定表单数据模型
		- v-bind="$attrs": 绑定父组件传递的所有属性
		- :inline: 内联模式，默认为 true
		- :size: 表单组件尺寸，默认为 'default'
		- :validate-on-rule-change="false": 禁用规则变化时的自动验证
		- :[styleName]="_styles": 动态绑定样式属性（class 或 style）
	-->
	<el-form
		ref="searchFormRef"
		:model="model"
		v-bind="$attrs"
		:inline="$attrs.inline || true"
		:size="$attrs.size || 'default'"
		:validate-on-rule-change="false"
		:[styleName]="_styles"
	>
		<!-- 动态渲染搜索表单项 -->
		<template v-for="item in searchItems" :key="item.prop">
			<!--
				表单项容器
				- v-if="!item.hidden": 只渲染非隐藏的表单项
				- :prop="item.prop": 设置表单项的属性名
				- v-bind="item.attrs": 绑定表单项的配置属性
			-->
			<el-form-item
				v-if="!item.hidden"
				:prop="item.prop"
				v-bind="item.attrs"
			>
				<!--
					表单项内容插槽
					- :name: 根据使用场景动态生成插槽名称
					- :item: 传递表单项配置
					- :data: 传递表单数据模型
				-->
				<slot
					:name="$attrs.scene ? `search_${item.prop}` : item.prop"
					:item="item"
					:data="model"
				>
					<!--
						动态组件渲染
						- :is: 根据类型动态选择组件
						- :item: 传递表单项配置
						- :data: 传递表单数据
						- :dic: 传递字典数据
						- @change: 监听值变化事件
					-->
					<component
						:is="'co-' + getType(item)"
						:item="item"
						:data="model"
						:dic="dic"
						@change="onItemChange"
					/>
				</slot>
			</el-form-item>
		</template>

		<!-- 操作按钮区域 -->
		<el-form-item v-if="searchItems.length > 0">
			<!--
				操作按钮插槽
				- :name: 根据使用场景动态生成插槽名称
				- :handle: 传递操作处理函数
			-->
			<slot
				:name="$attrs.scene ? 'search_operation' : 'operation'"
				:handle="onHandle"
			>
				<!-- 搜索按钮 -->
				<el-button
					v-bind="searchProps"
					@click="onHandle('search')"
				>
					{{ searchProps.name }}
				</el-button>

				<!-- 重置按钮 -->
				<el-button
					v-bind="resetProps"
					@click="onHandle('reset')"
				>
					{{ resetProps.name }}
				</el-button>
			</slot>
		</el-form-item>
	</el-form>
</template>

<script setup>
/**
 * Co-Search 搜索组件 (Composition API 版本)
 *
 * 基于 Element Plus Form 组件封装的动态搜索表单
 * 支持多种表单组件类型和复杂的数据处理逻辑
 *
 * 主要功能：
 * - 动态表单项渲染
 * - 多种表单组件支持
 * - 前置组件处理
 * - 分割字段支持
 * - 搜索和重置功能
 * - 数据回显和设置
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0.0 (Composition API)
 */

import { ref, reactive, computed, provide, onMounted } from 'vue';
// 导入默认配置和日期类型枚举
import defaultConfig, { dateType } from './config.js';
// 导入表单组件集合
import coFormItem from './components/co-form-item/index.js';
// 导入工具类
import Utils from './utils';

// 组件名称
defineOptions({
	name: 'CoSearch',
	components: {
		...coFormItem,
	},
	inheritAttrs: false,
});

/**
 * 组件属性定义
 */
const props = defineProps({
	// 表单数据模型
	model: {
		type: Object,
		default: () => ({}),
	},
	// 搜索表单配置对象
	config: {
		type: Object,
		default: () => ({}),
	},
	// 字典数据对象
	dic: {
		type: Object,
		default: () => null,
	},
});

/**
 * 组件事件定义
 */
const emit = defineEmits([
	'search',      // 搜索事件
	'change',      // 表单项变化事件
	'update:model' // v-model 双向绑定事件
]);

// ==================== 响应式状态 ====================

// 表单引用
const searchFormRef = ref(null);

// 表单数据模型（暂未使用）
const formModel = reactive({});

// 组件实例集合，用于访问各个表单组件的方法
const widgetItem = reactive({});

// 保存初始数据模型的副本，用于重置功能
const oldModel = ref({ ...props.model });

// 样式配置
const _styles = ref(null);
const styleName = ref('class');

// 按钮配置
const searchProps = ref({});
const resetProps = ref({});

// ==================== 计算属性 ====================

/**
 * 搜索表单项配置
 */
const searchItems = computed(() => {
	return props.config.items || [];
});

// ==================== 核心方法 ====================

/**
 * 获取表单项的组件类型
 * 将日期时间相关类型统一映射为 'date' 类型
 *
 * @param {Object} item - 表单项配置对象
 * @returns {string} 组件类型名称
 */
const getType = (item) => {
	// 如果是日期时间相关类型，统一返回 'date'
	if (dateType.includes(item.type)) {
		return 'date';
	}
	// 其他类型直接返回原类型
	return item.type;
};

/**
 * 处理表单项值变化事件
 * 更新数据模型并向父组件发送事件
 *
 * @param {Object} params - 变化参数对象
 * @param {string} params.prop - 属性名
 * @param {*} params.value - 新值
 */
const onItemChange = ({ prop, value }) => {
	// 更新数据模型中对应属性的值
	props.model[prop] = value;

	// 如果在特定场景下使用，发送 update:model 事件（用于 v-model 双向绑定）
	// 同时发送 change 事件通知父组件
	if (props.$attrs?.scene) {
		emit('update:model', props.model);
	}
	emit('change', { prop, value });
};

/**
 * 处理搜索和重置操作
 * 这是搜索组件的核心方法，处理复杂的数据转换和前置组件逻辑
 *
 * @param {string} [type='search'] - 操作类型：'search' 或 'reset'
 */
const onHandle = (type = 'search') => {
	// 获取表单数据模型的所有字段名
	const formModelArr = Object.keys(props.model);
	// 搜索结果对象，用于存储处理后的搜索参数
	const searchResult = {};

	// 遍历所有表单字段进行处理
	for (const widget of formModelArr) {
		if (type === 'reset') {
			// === 重置操作处理 ===

			// 获取字段的初始值
			const setVal = oldModel.value ? oldModel.value[widget] : '';

			// 如果字段对应的组件实例存在，调用其重置方法
			if (widgetItem[widget]) {
				widgetItem[widget].resetField(setVal);
			}

			// 更新数据模型中的值
			props.model[widget] = setVal;
		} else {
			// === 搜索操作处理 ===

			// 判断是否为前置组件字段（约定以 '_prepend' 结尾）
			const hasPrepend = widget.includes('_prepend');

			if (hasPrepend) {
				// === 前置组件字段处理 ===

				if (props.model[widget]) {
					// 前置组件有值时的处理

					// 获取主字段名（移除 '_prepend' 后缀）
					const mainProp = widget.replace(/\_prepend$/, '');

					if (widgetItem[mainProp]?.isDateType) {
						// === 日期时间类型的前置组件处理 ===

						// 获取分割字段配置
						const splitProp = widgetItem[mainProp]?.item.splitProp;

						if (splitProp && Utils.getType(splitProp) === 'Array') {
							// 有分割字段：将日期范围分别赋值给不同字段
							if (props.model[mainProp]?.length) {
								// 生成动态字段名并赋值
								searchResult[`${props.model[widget]}${splitProp[0]}`] = props.model[mainProp][0] ?? '';
								searchResult[`${props.model[widget]}${splitProp[1]}`] = props.model[mainProp][1] ?? '';
							}
						} else {
							// 无分割字段：将日期数组用分隔符连接
							searchResult[props.model[widget]] = props.model[mainProp].join(splitProp || ',');
						}
					} else {
						// === 非日期类型的前置组件处理 ===

						// 如果主字段有值，使用前置组件选择的字段名作为键
						props.model[mainProp] && (searchResult[props.model[widget]] = props.model[mainProp]);
					}
				} else {
					// 前置组件无值时，清理之前缓存的字段
					delete searchResult[widgetItem[widget].cacheKey];
					widgetItem[widget].cacheKey = undefined;
				}
			} else if (!widgetItem[widget]?.item.prepend || !widgetItem[widget]?.item.prepend.prop.includes('_prepend')) {
				// === 普通字段处理 ===

				// 不是前置组件字段，且没有前置组件配置，直接使用字段名和值
				searchResult[widget] = props.model[widget];
			}
		}
	}

	// 向父组件发送搜索事件，传递处理后的搜索参数和操作类型
	emit('search', searchResult, type);
};

/**
 * 设置表单数据
 * 用于外部设置搜索表单的数据，支持数据回显
 *
 * @param {Object} data - 要设置的数据对象
 */
const setData = (data) => {
	// 创建响应式的表单数据模型
	const formModel = reactive(props.model);
	// 获取当前表单模型的所有字段名
	const formModelArr = Object.keys(formModel);

	// 遍历要设置的数据
	for (const [key, value] of Object.entries(data)) {
		// 如果对应的组件实例存在，调用其重置方法
		if (widgetItem[key]) {
			widgetItem[key].resetField(value);
		}

		// 更新表单数据模型
		if (formModelArr.includes(key)) {
			// 字段已存在，更新值
			formModel[key] = value || '';
		} else {
			// 字段不存在，添加新字段
			formModel[key] = value || '';
		}
	}
};

// ==================== 生命周期 ====================

/**
 * 组件挂载后的初始化逻辑
 */
onMounted(() => {
	// 从配置中解构获取样式配置
	const styles = props.config.styles;

	// 设置组件样式，优先使用配置的样式，其次使用默认配置
	_styles.value = styles || defaultConfig.search.style;

	// 根据样式类型确定属性绑定方式
	styleName.value = typeof _styles.value === 'string' ? 'class' : 'style';

	// 保存初始数据模型的副本，用于重置功能
	oldModel.value = { ...props.model };

	// 设置搜索按钮的属性配置
	searchProps.value = defaultConfig.search.search;

	// 设置重置按钮的属性配置
	resetProps.value = defaultConfig.search.reset;
});

// ==================== 依赖注入 ====================

/**
 * 向子组件提供数据
 * 使子组件能够访问 widgetItem 对象
 */
provide('widgetItem', widgetItem);

// ==================== 暴露方法 ====================

/**
 * 暴露给父组件的方法
 */
defineExpose({
	setData,
	searchFormRef,
});
</script>

<!--
  搜索组件样式定义
  主要用于设置内联表单中各种组件的宽度
-->
<style lang="scss">
/* 内联表单样式 */
.el-form--inline {
	.el-form-item {
		/* 基础表单组件宽度设置 */
		.el-input,
		.el-cascader,
		.el-select,
		.el-autocomplete {
			width: 220px;
		}

		/* 日期选择器宽度设置 */
		.el-date-editor {
			width: 360px;

			/* 日期时间范围选择器 */
			&.el-date-editor--datetimerange {
				width: 380px;
			}

			/* 月份范围和年份范围选择器 */
			&.el-date-editor--monthrange,
			&.el-date-editor--yearrange {
				width: 270px;
			}
		}

		/* 输入框组合中的前置和后置组件 */
		.el-input-group__prepend,
		.el-input-group__append {
			.el-select {
				width: 120px;
			}
		}

		/* 带前置或后置组件的输入框 */
		.el-input:has(.el-input-group__prepend, .el-input-group__append) {
			width: 320px;
		}

		/* 同时带前置和后置组件的输入框 */
		.el-input.el-input-group--append.el-input-group--prepend {
			width: 400px;
		}
	}
}
</style>
