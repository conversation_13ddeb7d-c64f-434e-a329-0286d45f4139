/**
 * Co-Table 组件库入口文件 (Composition API 版本)
 *
 * 这是一个功能完整的Vue表格组件库，提供以下核心功能：
 * - 数据表格展示与操作
 * - 内置搜索功能
 * - 分页支持
 * - 表单编辑
 * - 权限控制
 * - 字典数据支持
 * - 文件上传下载
 * - 三种API模式兼容（co-table、page-table、统一模式）
 *
 * 使用方式：
 * 1. 作为Vue插件安装：app.use(CoTable, options)
 * 2. 直接导入组件：import CoTable from 'co-table'
 * 3. 按需导入：import { CoTable, CoSearch } from 'co-table'
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0.0 (Composition API)
 */

// 导入默认配置对象
import defaultConfig from "./config.js";
// 导入工具类
import Utils from "./utils";
// 导入主表格组件（重构后的Composition API版本）
import CoTable from "./table.vue";
// 导入搜索组件（重构后的Composition API版本）
import CoSearch from "./search.vue";
// 导入公共样式文件
import "./styles/common.scss";

// 在全局window对象上创建storage属性，用于存储组件间共享的数据
if (typeof window !== "undefined") {
  window.storage = window.storage || {};
}

/**
 * Vue插件安装函数
 * 负责注册组件到Vue应用实例，并合并用户配置
 *
 * @param {Object} app - Vue应用实例
 * @param {Object} [opts={}] - 用户自定义配置选项
 *
 * @example
 * // 基本安装
 * app.use(CoTable);
 *
 * // 带配置安装
 * app.use(CoTable, {
 *    getDic: (params) => {
 *        return getDictionary(params).then(({ dicList }) => {
 *          return formatDic(dicList);
 *        });
 *      },
 *      attrs: {
 *        'header-cell-style': { 
 *          backgroundColor: 'var(--el-table-row-hover-bg-color)', 
 *          color: 'var(--el-text-color-primary)', 
 *          align: 'left' 
 *        },
 *        border: true,
 *      },
 * });
 */
const install = (app, opts = {}) => {
  // 如果用户提供了配置选项，则与默认配置进行深度合并
  if (Object.keys(opts).length) {
    Utils.deepMerge(defaultConfig, opts);
  }

  // 全局注册表格组件
  app.component(CoTable.name || 'CoTable', CoTable);
  // 全局注册搜索组件
  app.component(CoSearch.name || 'CoSearch', CoSearch);
};

/**
 * 为主表格组件添加install方法，使其可以作为Vue插件使用
 * 这是Vue插件的标准做法
 */
CoTable.install = (app, opts = {}) => {
  install(app, opts);
};

/**
 * 自动安装插件
 * 如果在浏览器环境中检测到全局Vue对象，则自动安装插件
 * 这样可以通过script标签直接引入使用，无需手动调用app.use()
 * 
 * 注意：Vue 3中不再有全局Vue对象，这里主要是为了向后兼容
 */
if (typeof window !== "undefined" && window.Vue) {
  install(window.Vue, {});
}

// 导出搜索组件，支持按需导入
export { CoSearch };

// 导出配置对象，允许外部访问和修改
export { defaultConfig as config };

// 导出工具类，提供给外部使用
export { Utils };

// 导出安装函数，支持手动安装
export { install };

// 导出主表格组件作为默认导出
// 用户可以通过 import CoTable from 'co-table' 的方式导入
export default CoTable;
